<template>
  <div class="section-container">
    <div class="section-title">基础信息</div>
    <div class="form-content">
      <i-form ref="formRef" :model="formData" label-position="left">
        <i-form-item label="场景分类" :label-width="150" required>
          <i-select v-model="formData.classification" style="width: 200px" :disabled="isView" @on-change="handleClassificationChange">
            <i-option
              v-for="(item, index) in classificationType"
              :key="index"
              :value="item.fuelUse"
            >
              {{ item.fuelUse }}
            </i-option>
          </i-select>
        </i-form-item>
        <i-form-item label="场景" :label-width="150" required>
          <i-select v-model="formData.scene_name" style="width: 200px" :disabled="isView">
            <i-option
              v-for="(item, index) in currentSceneOptions"
              :key="index"
              :value="item"
            >
              {{ item }}
            </i-option>
          </i-select>
        </i-form-item>
        <i-form-item label="说明" :label-width="150" required>
          <i-input v-model="formData.scene_desc" type="textarea" :maxlength="200" show-word-limit :disabled="isView" />
        </i-form-item>
        <i-form-item label="是否拦截" :label-width="150" required>
          <i-switch v-model="formData.intercept_status" :disabled="isView" />
        </i-form-item>
        <i-form-item label="状态" :label-width="150">
          <span>{{ formData.enable_status ? "启用" : "停用" }}状态</span>
          <i-button
            v-if="!isView"
            type="primary"
            @click="handleSave"
            style="margin-left: 16px"
            :disabled="type === 'create'"
          >
            {{ !formData.enable_status ? "启用" : "停用" }}
          </i-button>
        </i-form-item>
        <i-form-item label="名单操作" :label-width="150">
          <i-button  type="primary" @click="handleOpenWhiteNamelist">白名单</i-button>
          <i-button  type="primary" @click="handleOpenBlackNamelist" style="margin-left: 10px;">黑名单</i-button>
        </i-form-item>
      </i-form>
    </div>
    <!-- 名单控制弹窗 -->
    <view-namelist
      :show.sync="showViewNamelistModal"
      :title="viewNamelistTitle"
      :biz_type="biz_type"
      :viewStatus="viewNamelistStatus"
      :type="type"
      @cancel="handleViewNamelistCancel"
    />
  </div>
</template>

<script>
import ViewNamelist from "../profile/modelViewNamelist.vue";

export default {
  name: 'BaseInfoModel',
  components: {
    ViewNamelist
  },
  props: {
    type: {
      type: String,
      default: 'view' // view, edit, create
    },
    biz_type: {
      type: String,
      default: ''
    },
    scene_id: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      formData: {
        classification: '',
        scene_name: '',
        scene_desc: '',
        intercept_status: true,
        enable_status: false, // 默认状态改为停用
        create_by: "admin"
      },
      classificationType: [
        { fuelUse: '风险控制', scenes: [ '积分商城兑换实物商品',
            '积分商城兑换虚拟商品',
            '用户注册',
            '积分商城兑换优惠券',
            '燃值抽奖',
            '运动连积分抵扣',
            '穿线积分抵扣',
            '积分抵现'] },
        // { fuelUse: '燃值使用', scenes: ['积分商城兑换实物商品'] }
      ],
      currentSceneOptions: [], // 当前场景选项列表
      // 控制 viewNamelist 弹窗的属性
      showViewNamelistModal: false,
      viewNamelistTitle: '',
      viewNamelistBizType: '',
      viewNamelistStatus: false, // false: 白名单, true: 黑名单
    }
  },
  computed: {
    isView() {
      return this.type === 'view'
    }
  },
  mounted() {
    if (this.type === 'create') {
      // 新建模式下默认选择第一个分类
      const firstClassification = this.classificationType[0];
      this.formData.classification = firstClassification.fuelUse;
      this.currentSceneOptions = firstClassification.scenes;
      this.formData.scene_name = firstClassification.scenes[0];
    }
  },
  methods: {
    // 设置表单数据（由父组件调用）
    setFormData(data) {
      // 先设置场景分类
      this.formData.classification = data.classification;

      // 根据场景分类设置场景选项
      const classificationItem = this.classificationType.find(item => item.fuelUse === data.classification);
      if (classificationItem) {
        this.currentSceneOptions = classificationItem.scenes;
      }

      // 设置其他表单数据
      this.formData = {
        ...this.formData,
        ...data
      }
    },

    async handleSave() {
      try {
        // 切换状态
        this.formData.enable_status = !this.formData.enable_status;

        const params = {
          scene_id: this.scene_id,
          classification: this.formData.classification,
          scene_name: this.formData.scene_name,
          scene_desc: this.formData.scene_desc,
          intercept_status: this.formData.intercept_status,
          enable_status: this.formData.enable_status,
          create_by: this.formData.create_by
        }

        // 调用更新场景状态的API
        const resp = await this.$api.updateScene(params)

        if (resp.status === 'success') {
          this.$message.success(this.formData.enable_status ? '启用成功' : '停用成功')
          // 通知父组件保存成功，触发数据刷新
          this.$emit('save-success')
        }
      } catch (error) {
        // 如果保存失败，恢复原状态
        this.formData.enable_status = !this.formData.enable_status;
        this.$message.error('状态变更失败')
        console.error('状态变更失败:', error)
      }
    },

    // 处理场景分类变化
    handleClassificationChange(value) {
      const classificationItem = this.classificationType.find(item => item.fuelUse === value);
      if (classificationItem) {
        this.currentSceneOptions = classificationItem.scenes;
        // 场景分类改变时，选择对应场景列表的第一个选项
        this.formData.scene_name = classificationItem.scenes[0];
      }
    },

    // 通用方法：打开名单控制弹窗
    openNamelistModal(isBlacklist) {
      this.viewNamelistBizType = this.biz_type;
      this.viewNamelistStatus = isBlacklist;
      this.viewNamelistTitle = isBlacklist ? '黑名单控制' : '白名单控制';
      this.showViewNamelistModal = true;
    },

    // 点击"白名单控制"按钮
    handleOpenWhiteNamelist() {
      this.openNamelistModal(false);
    },

    // 点击"黑名单控制"按钮
    handleOpenBlackNamelist() {
      this.openNamelistModal(true);
    },

    // viewNamelist 弹窗关闭事件
    handleViewNamelistCancel() {
      this.showViewNamelistModal = false;
    },

    // 获取表单数据
    getFormData() {
      return this.formData;
    }
  }
}
</script>

<style lang="less" scoped>
.section-container {
  margin-bottom: 20px;
  width: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #17233d;
}

.form-content {
  border: 1px solid #e8eaec;
  padding: 20px;
  border-radius: 4px;
  background: #fff;
}

/deep/ .ivu-form-item {
  margin-bottom: 24px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
