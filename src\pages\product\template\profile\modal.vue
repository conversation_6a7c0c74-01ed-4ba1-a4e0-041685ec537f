<template>
  <div class="container">
    <i-modal footer-hide width="1000" :mask-closable="false" :closable="false" :title="title" v-model="showDialog">
      <div class="header mt20 mb30">
        <i-input v-model="modelCode" class="search mr15" search enter-button placeholder="精确查询(Model Code)" @on-search="searchModel" />
      </div>
      <i-table border highlight-row :columns="productColumns" :data="productList" @on-current-change="selectProduct" />
      <div class="item-center justify-between mt20" v-if="selectedProduct">
        <i-form :model="template">
          <i-row>
            <i-col :span=12>
              <i-form-item label="名称" class="modal-item">
                <i-input v-model="template.title" class="text" placeholder="请输入产品名称" :maxlength="10" />
              </i-form-item>
            </i-col>
            <i-col :span=12>
              <i-form-item label="描述" class="modal-item">
                <i-input v-model="template.description" class="text" type="textarea" :autosize="{minRows:3,maxRows:8}" placeholder="请输入产品描述" :maxlength="255"/>
              </i-form-item>
            </i-col>
          </i-row>
        </i-form>
      </div>
      <div class="mt30 flex row justify-center">
        <i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
        <i-button type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
      </div>
    </i-modal>
  </div>
</template>

<script>
export default {
  name: 'template_repo_modal',
  props: {
    title: '',
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      showDialog: true,

      modelCode: '',

      productList: [],     // DKT产品列表
      productColumns: [
        { title: 'Model Code', key: 'modelCode', className: 'th-class' },
        { title: 'Item Code', key: 'itemCode', className: 'th-class' },
        { title: '名称', key: 'title', className: 'th-class' },
        { title: '描述', key: 'description', className: 'th-class' },
        { title: '图片', className: 'pt5 th-class', fixed: 'right', width: 160, render: (h, params) => { return this.showRowImage(h, params.row.imageUrls) }},
      ],

      selectedProduct: '',

      template: {
        title: '',
        description: '',
      },
    }
  },
  methods: {
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('span', { class: 'span-pic-box' }, [h('img', { attrs: { src: imageUrls[0], class: 'table-img', width: '100px', height: '100px' } })])
    },

    // 搜索DKT产品库
    async searchModel() {
      if (this.modelCode) {
        let resp = await this.$api.searchModel({'modelCode': this.modelCode})
        this.productList = resp.data
        if (this.productList.length == 0) {
          this.$message.info({ content: '什么产品也没找到!', closable: true, duration: 3 })
        }
      } else {
        this.$message.warning({ content: '请输入模型编号', closable: true, duration: 3 })
      }
    },

    // 表格行选中事件
    selectProduct(currentRow, oldRow) {
      this.selectedProduct = currentRow
      this.template.title = this.selectedProduct.title
      this.template.description = this.selectedProduct.description
    },

    async certainClick() {
      if (this.selectedProduct) {
        this.selectedProduct.title = this.template.title
        this.selectedProduct.description = this.template.description
        if (this.selectedProduct.title.length > 10) {
          this.$message.warning({ content: '请控制商品名称在10字以内', closable: true, duration: 3 })
          return
        }
        let resp = await this.$api.createTemplateProduct(JSON.stringify(this.selectedProduct))
        if (resp.status == 'success') {
          this.certain && this.certain()
        }
      } else {
        this.$message.warning({ content: '请选择要添加的商品', closable: true, duration: 3 })
      }
    },
    cancelClick() {
      this.showDialog = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
  }
}
</script>

<style lang="less" scoped>
.search {
  width: 100%;
}

.span-pic-box {
  display: block;
  height: 80px;
  .table-img {
    width: 80px;
    height: 60px;
    margin-top: 10px;
  }
}

.th-class {
  text-align: center!important;
  vertical-align: middle!important;
  min-width: 120px!important;
}

.text {
  width: 90%;
}
</style>
