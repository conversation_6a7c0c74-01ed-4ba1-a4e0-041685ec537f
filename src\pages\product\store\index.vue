<template>
  <div class="store_product">
    <div class="mt20 mb30 row item-center justify-between">
      <div>
        <i-select v-model="opFrom.batchNumber" style="width:160px" placeholder="月份" @on-change="changeBatchNumber" clearable>
          <i-option :value="item" v-for="item in batchNumberList" :key="item">{{item}}</i-option>
        </i-select>
      </div>
      <div v-if="opFrom.batchNumber">
        <i-button v-if="batchProducts.status=='PUBLISHED'" type="primary" @click="publishBatch" :disabled="true">已发布</i-button>
        <i-button v-else type="primary" @click="publishBatch" v-has="['PRODUCT_STORE_LIST_UPDATE']">发布</i-button>
      </div>
    </div>
    <div class="content mt10 row justify-between">
      <div class="content-item store-left-content" v-if="opFrom.batchNumber">
        <div class="mt10" v-for="level in levelList" :key="level.code">
          <div class="row justify-between">
            <div>{{level.label}}</div>
            <div><i-button class="mr15" @click="handleLevelEdit(level)" v-has="['PRODUCT_STORE_LIST_UPDATE']">编辑此项</i-button></div>
          </div>
          <div style="height:70px">
            <i-row>
              <i-col v-for="batch in batchProducts[level.dataKey]" :key="batch.id" class="span-pic-box" :span="4">
                <img :src="batch['productInfo']['imageUrls'][0]" class="table-img">
              </i-col>
            </i-row>
          </div>
        </div>
      </div>
      <div class="content-item store-right-content">
        <div class="mt20" v-if="opFrom.batchNumber && level">
          <div class="row item-center justify-between">
            <div> 您正在编辑:【{{opFrom.batchNumber}} - {{level.label}}】的产品</div>
            <div>
              <i-button @click="popupSearch" v-if="opFrom.onSale && opFrom.currentSize < 5" 
                v-has="['PRODUCT_TEMPLATE_CREATION', 'PRODUCT_STORE_LIST_UPDATE']">从产品库搜索</i-button>
              <i-button @click="popupSearch" v-else-if="!opFrom.onSale && opFrom.currentSize < 5" 
                v-has="['PRODUCT_STORE_LIST_UPDATE']">从模板库添加</i-button>
            </div>
          </div>
          <div class="mt20">
            <i-table border :columns="productColumns" :data="productList"/>
          </div>
        </div>
      </div>
    </div>
    <m-modal v-if="showModelDialog" :certain="modalCertain" :cancel="modalCancel" :op-from="opFrom" />

    <t-modal v-if="showTemplateDialog" :certain="modalCertain" :cancel="modalCancel" :op-from="opFrom" />

    <e-modal v-if="showEditDialog" :certain="modalCertain" :cancel="modalCancel" :id="id" />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import cfg from '../config'
import ModelModal from './profile/model'
import EditProductModal from '../edit'
import TemplateModal from './profile/template'

export default {
  name: 'product_batch',
  components: {
    'm-modal': ModelModal,
    't-modal': TemplateModal,
    'e-modal': EditProductModal
  },
  data() {
    return {
      batchNumberList: [],

      productList: [],     // 产品模板列表

      // 表格操作按钮
      opBtnList: [
        { code: 'edit', text: '编辑', permissions: ['PRODUCT_STORE_LIST_UPDATE'] },
        { code: 'disable', text: '下架', type: 'warning', permissions:['PRODUCT_STORE_LIST_UPDATE'] }
      ],

      productColumns: [
        { title: 'Model Code', key: 'modelCode', className: 'th-class', render: (h, params) => { return h('span', params.row.productInfo.modelCode) } },
        { title: 'Item Code', key: 'itemCode', className: 'th-class', render: (h, params) => { return h('span', params.row.productInfo.itemCode) } },
        { title: '商品名称', key: 'title', className: 'th-class', render: (h, params) => { return h('span', params.row.productInfo.title) }},
        { title: '兑换燃值', key: 'points', className: 'th-class', render: (h, params) => { return h('span', params.row.points) }},
        { title: '库存', key: 'stock', className: 'th-class', render: (h, params) => { return h('span', params.row.remainingStocks) } },
        { title: '操作',  key: 'action', className: 'th-class', fixed: 'right', width: 120, render: (h, params) => { return this.createOpBtn(h, params.row) }}
      ],

      level: '',
      levelList: cfg.levelList,

      // 搜索产品库
      opFrom: {
        onSale: false,
        type: '',
        batchNumber: '',
        currentSize: '',
      },
      showModelDialog: false,
      showTemplateDialog: false,
      batchProducts: {},
      showEditDialog: false,
      id: '',
    }
  },
  mounted() {
    this.loadData()
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  watch: {
    // 监控门店切换, 刷新数据
    currentStore(newVal, oldVal) {
      this.loadData()
      this.searchBatchProducts()
    }
  },
  methods: {
    // 初始数据
    async loadData() {
      let resp = await this.$api.listBatch()
      if (resp && resp['status'] === 'success') {
        this.batchNumberList = resp.data
      }
    },

    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let disabled = row.expiredSoon
      let operations = []
      for (let i=0; i < this.opBtnList.length; i++) {
        let value = this.opBtnList[i], btn
        if (value.code == 'disable') {
          btn = h('i-button', {
            class: 'ml5', 
            props: { size: 'small', type: value.type, disabled: disabled },
            directives: [ { name: 'has', value: value.permissions } ],
            on: { click: () => { this.rowOperation(value.code, row) } } 
          }, disabled ? "即将下架" : value.text)
        } else {
          if (disabled || this.batchProducts.status == 'PUBLISHED') {
            continue
          } else {
            btn = h('i-button', {
              class: 'ml5', 
              props: { size: 'small', type: value.type },
              directives: [ { name: 'has', value: value.permissions } ],
              on: { click: () => { this.rowOperation(value.code, row) } } 
            }, value.text)
          }
        }
        operations.push(btn)
      }
      return h('div', operations)
    },

    // 表格项操作
    rowOperation(code, row) {
      switch(code) {
        case 'disable':
          this.$modal.confirm({
					  'title': '商品下架',
            'content': `您即将下架: ${this.opFrom.batchNumber}${this.level.label}的商品<span class="confirm-msg">${row.productInfo.title}</span>. 请确认?`,
					  'okText': '确定',
					  'cancelText': '取消',
					  'onOk': () => {
						  this.disableProduct(row.id)
					  }
          })
          break
        case 'edit':
          this.editProduct(row.id)
          break
      }
    },

    // 改变月份
    changeBatchNumber(value) {
      this.level = ''
      this.searchBatchProducts()
    },

    // 搜索当月产品
    async searchBatchProducts() {
      if (this.opFrom.batchNumber) {
        let resp = await this.$api.searchBatchProducts(this.opFrom.batchNumber, this.currentStore.dktStoreNumber)
        if (resp['status'] == 'success') {
          this.batchProducts = resp.data
          this.refreshProductList()
        }
      }
    },

    // 编辑此项点击事件
    handleLevelEdit(item) {
      if (this.opFrom.batchNumber == '') {
        this.$modal.warning({ 'title': '提示', 'content': '请选择月份' })
      }
      this.level = item
      switch (item.value) {
        case 'ON_SALE':
          this.opFrom['onSale'] = true
          break
        default:
          this.opFrom['onSale'] = false
          break
      }
      this.opFrom['type'] = item.value
      this.opFrom.currentSize = this.batchProducts[item.dataKey] ? this.batchProducts[item.dataKey].length : 0
      if (this.batchProducts[item.dataKey]) {
        this.productList = this.batchProducts[item.dataKey]
      } else {
        this.productList = []
      }
      console.log('Click: ', item.value)
      console.log('onSale: ', this.opFrom.onSale)
      console.log('currentSize: ', this.opFrom.currentSize)
      console.log('opFrom.onSale && opFrom.currentSize < 3: ', this.opFrom.onSale && this.opFrom.currentSize < 3)
      console.log('!opFrom.onSale && opFrom.currentSize < 5: ', !this.opFrom.onSale && this.opFrom.currentSize < 5)
    },
    
    // 刷新右侧表格(如果之前有点击过左边编辑按钮)
    refreshProductList() {
      let type = this.opFrom['type']
      if (type) {
        this.levelList.forEach((level) => {
          if (level.value == type) {
            if (this.batchProducts[level.dataKey]) {
              this.productList = this.batchProducts[level.dataKey]
              this.opFrom.currentSize = this.productList.length
            } else {
              this.productList = []
            }
            return
          }
        })
      }
    },
    // 产品库搜索弹出框
    popupSearch() {
      if (this.opFrom.onSale == true) {
        this.showModelDialog = true
      } else {
        this.showTemplateDialog = true
      }
    },
    // 下架商品
    async disableProduct(id) {
      let resp = await this.$api.disableProduct(id, this.currentStore.dktStoreNumber)
      if (resp['status'] === 'success') {
        this.searchBatchProducts()
      }
    },
    // 编辑产品
    editProduct(id) {
      this.showEditDialog = true
      this.id = id
    },
    // 发布批次产品
     publishBatch() {
      let template = '门店['+ this.currentStore.dktStoreNumber + '] - 批次[' + this.opFrom.batchNumber + ']的商品.'
      let flag = true
      for (let i=0; i < cfg.levelList.length && flag; i++) {
        let level = cfg.levelList[i]
        let levelProducts = this.batchProducts[level.dataKey]
        if (levelProducts == null) {
          levelProducts = []
        }
        if (levelProducts && levelProducts.length >= level.min && levelProducts.length <= level.max) {
          flag = true
        } else {
          flag = false
          this.$message.warning({ content: level.label + '的产品数量限制为:' + level.min + '-' + level.max + ', 请确认!', closable: true, duration: 3 })
        }
      }
      if (!flag) {
        return
      }
      
      this.$modal.confirm({
        'title': '商品发布',
        'content': '您即将发布: ' + template + ' 请确认?',
        'okText': '确定',
        'cancelText': '取消',
        'onOk': async () => {
          let resp = await this.$api.publishBatchProduct(this.opFrom.batchNumber, this.currentStore.dktStoreNumber)
          if (resp['status'] == 'success') {
            this.$message.success({ content: '您已成功发布: ' + template, closable: true, duration: 3 })
            this.searchBatchProducts()
          }
        }
      })
    },

    modalCertain() {
      this.closeDialog()
      this.searchBatchProducts()
    },
    modalCancel() {
      this.closeDialog()
    },
    closeDialog() {
      if (this.opFrom.onSale == true) {
        this.showModelDialog = false
      } else {
        this.showTemplateDialog = false
      }
      if (this.id) {
        this.showEditDialog = false
        this.id = ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.store_product {
  .store-left-content {
    width: 30vw;
    .span-pic-box {
      width: 5vw;
      display: block;
      height: 80px;
      .table-img {
        width: 3.5vw;
        height: 3.5vw;
        margin-top: 10px;
      }
    }
  }
  .store-right-content {
    width: 50vw;
  }
  
  .th-class {
    text-align: center!important;
    vertical-align: middle!important;
    min-width: 150px!important;
  }
}
.ml5 {
  margin-left: 5px;
}
.mr5 {
  margin-right: 5px;
}
</style>
