<template>
  <div class="template_repo template-repo-box">
    <i-row class="mt20 mb30">
      <i-col span="24">
        <i-col span="4" class="mr10 text-center">
          <i-input v-model="searchForm.word" class="" :autofocus="true" search placeholder="手机号、会员卡号搜索" clearable />
        </i-col>
        <i-col span="6" class="mr10 text-center" style="margin-left:-20px">
          <i-date-picker 
            ref="datePicker"
            type="datetimerange" 
            format="yyyy-MM-dd HH:mm" 
            placeholder="时间" 
            style="width: 260px"
            @on-change="changePicker" 
            @on-clear="closePicker" 
            @on-ok="closePicker"
            v-model="timeRange"
          >
          </i-date-picker>
        </i-col>
        <i-col span="4" class="mr10 text-left"  style="margin-left:-20px">
          <i-cascader
            change-on-select
            ref="store"
            :data="storeList"
            @on-change="storeChange"
            class="cascader-store"
            filterable
            placeholder="请选择门店"
            v-if="storeList && storeList.length"
            v-model="store"
          />
        </i-col>
        <i-col span="3" class="mr10">
          <i-select v-model="searchForm.status" placeholder="订单状态" clearable>
            <i-option v-for="l in statusList" :key="l.val" :value="l.val" >{{l.label}}</i-option>
          </i-select>
        </i-col>
        <i-col span="4">
          <i-button class="ml10 mr15" @click="searchClick">查询</i-button>
          <i-button class="mr15" @click="resetClick">重置</i-button>
        </i-col>
      </i-col>
    </i-row>

    <i-row>
      <i-table class="text-center" border :columns="templateColumns" :data="orderList" />
    </i-row>
    <i-row class="mt15">
      <i-col class="text-right">
        <i-page :total="page.total" show-total 
          :current="page.no"
          :page-size="page.size" show-sizer 
          :page-size-opts="pageSizeOpts"
          @on-change="changePageNo" @on-page-size-change="changePageSize" />
      </i-col>
    </i-row>
    <!-- 添加弹窗 -->
    <!-- 编辑弹窗 -->
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import paidMemberApi from '@/api/paidMember/index'
import { pageSizeOpts, checkPermissions, checkFill, judgeFill } from '@/utils/util'
let lastSearch = 0;

export default {
  name: 'template_repo',
  components: {
  },
  data() {
    return {
      store: [],
      timeRange: [],
      open: false,
      storeList: [],
      statusList: [
        { label: '已支付', val: 'PAID' },
        { label: '已退款', val: 'REFUNDED' },
        { label: '已过期', val: 'EXPIRED' },
      ],
      statusType: {
        PAID: '已支付',
        REFUNDED: '已退款',
        EXPIRED: '已过期',
      },
      // 分页相关配置
      pageSizeOpts: pageSizeOpts,
      page: {
        no: 1,
        size: 10,
        total: 0
      },
      searchForm: {
        word: '',
        status: '',
        city: '',
        page_num: 1,
        page_size: 10,
        store_number: '',
        begin_create_time: '',
        end_create_time: ''
      },
      orderList: [],     // 订单列表
      searchBackup: {
        word: '',
        status: '',
        city: '',
        page_num: 1,
        page_size: 10,
        store_number: '',
        begin_create_time: '',
        end_create_time: ''
      },
      title: '',            // 对话框标题
      showDialog: false,

      // 表格操作按钮
      opBtnList: [
        { code: 'view', text: '查看'},
      ],
      goRefundBtn: [{ code: 'goRefund', text: '退卡并退款', type: 'primary', permissions: ['PAID_MEMBER_ORDER_REFUND'] }],
      refundedBtn:  [{ code: 'refunded', text: '已发起退款', type: 'primary', permissions: ['PAID_MEMBER_ORDER_REFUND'] }],
      templateColumns: [
        { title: '姓名', key: 'name',  width: 170, className: 'th-class pro-img-box'},
        { title: '手机号', key: 'mobile', className: 'th-class pro-img-box'},
        { title: '会员卡号', key: 'card_number', className: 'th-class'},
        { title: '开卡城市', key: 'city', className: 'th-class'},
        { title: '开卡门店', key: 'store_name', className: 'th-class'},
        { title: '支付时间', key: 'create_time', width: 160, className: 'th-class'},
        { title: '订单状态', key: 'status', width: 80, className: 'th-class', render: (h, params) => { return this.getStatusText(h, params.row.status)  }},
        { title: '支付金额', key: 'price', className: 'th-class', render: (h, params) => { return this.formatPrice(h, params.row) }},
        { title: '操作',  key: 'action', className: 'th-class', fixed: 'right', width: 220, render: (h, params) => { return this.createOpBtn(h, params.row) }}
      ],
    }
  },
  beforeRouteEnter(to, from, next) {
    if(from.params.lastSearch) {
      lastSearch = 1;
    } else {
      lastSearch = 0;
    }
    next()
  },
  computed: {
    ...mapGetters(['orderSearchParams'])
  },
  created() {
    if( lastSearch ) {
      this.page.no =  this.orderSearchParams['page_num'];
      this.page.size = this.orderSearchParams['page_size'];
      this.searchForm = this.orderSearchParams;
      this.store = [this.searchForm.city, this.searchForm.store_number]
      this.timeRange = [this.searchForm.begin_create_time, this.searchForm.end_create_time]
    }
    this.searchNationalProduct()
    this.getCityStoreList();
  },
  mounted() {
  },
  methods: {
    ...mapActions(['setOrderSearchParams']),
    changePicker(date) {
      this.searchForm.begin_create_time = date[0];
      this.searchForm.end_create_time = date[1];
      this.open = false
    },
    // 关闭日期选择
    closePicker() {
      this.open = false
    },
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'table-pic-box' }, [h('img', { attrs: { src: imageUrls[0], class: 'table-img proImg' } })])
    },
    // 获取状态
    getStatusText(h, status) {
      return h('span', this.statusType[status] )
    },
    storeChange(val, selectData) {
      this.searchForm.city = val[0] || '';
      this.searchForm.store_number = val[1] || '';
    },
    // formatPrice
    formatPrice(h, row) {
      let price = row.price;
      price = (price == 0 || price) ? price.toFixed(1) : '';
      return h('span', '¥' + price)
    },
    checkBtnVisible(val) {
      let hasPermission = checkPermissions([val]);
      return hasPermission;
    },
    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      let newBtnList = [];
      let generatedBtn = [] ;
      let hasRefundAuth = this.checkBtnVisible('PAID_MEMBER_ORDER_REFUND');
      if(row.status === 'PAID' && hasRefundAuth) {
        generatedBtn = this.goRefundBtn;
      } else if(row.status === 'REFUNDED'&& hasRefundAuth) {
        generatedBtn = this.refundedBtn
      }
      newBtnList = this.opBtnList.concat( generatedBtn ); 
      newBtnList.forEach((value) => {
        let isDisabled = value.code === 'refunded';
        let finalClass = 'ml5 mr5 ' + (isDisabled ? 'btn-disabled' : '');
        operations.unshift(
          h('i-button', { 
            class: finalClass, 
            props: { type: value.type },
            on: { click: () => { this.rowOperation(value.code, row) } } },
            value.text)
        )
      })
      return h('div', operations)
    },
    // 查询
    searchClick() {
      this.page.no = 1;
      this.searchNationalProduct()
    },
    // 重置搜索条件
    resetClick() {
      this.searchForm = JSON.parse(JSON.stringify(this.searchBackup));
      this.$refs.store.clearSelect();
      this.$refs.datePicker.handleClear();
      this.searchNationalProduct()
    },
    async getCityStoreList() {
      let resp = await paidMemberApi.getCityStore();
      if (resp.status === 'success') {
        this.formatStoreList(resp.data);
      }
    },
    formatStoreList(list) {
      let finalArr = [];
      let arrItem = {};
      list.forEach(item => {
        arrItem = {
          label: item.city,
          value: item.city,
          children: []
        };
        item.store_list.forEach(storeItem => {
          storeItem['label'] = storeItem.name;
          storeItem['value'] = storeItem.store_number;
        })
        arrItem.children = item.store_list;
        finalArr.push(arrItem)
      })
      this.storeList = finalArr;
    },
    // get订单列表
    async searchNationalProduct() {
      this.searchForm['page_num'] = this.page.no
      this.searchForm['page_size'] = this.page.size
      this.setOrderSearchParams(this.searchForm);
      let resp = await paidMemberApi.getOrderList(this.searchForm);
      if (resp.status === 'success') {
        this.orderList = resp.data.items || [];
        this.page.total = resp.data.total
      }
    },

    // 分页操作
    changePageNo(no) {
      this.page.no = no
      this.searchNationalProduct()
    },
    changePageSize(size) {
      this.page.size = size
      this.searchNationalProduct()
    },

    // 表格项操作
    rowOperation(code, row) {
      switch(code) {
        case 'goRefund':
          this.$modal.confirm({
            'title': '确认退卡并退款？',
            'content': `确定后将终止当前用户的会员身份 <br/>并发起退款`,
            'okText': '确定',
            'cancelText': '取消',
            'onOk': () => {
              this.goRefundRequest(row)
            }
          })
          break
        // case 'refunded':
        //   break
        case 'view':
          // 跳转订单详情
          this.$router.push({
            path: `/orderDetail/${row.paid_member_id}`
          })
          break
      }
    },
    async goRefundRequest(row) {
      const { campaign_id, card_number, external_id, paid_member_id, store_number } = row;
      let params = {
        campaign_id,
        card_number,
        external_id,
        paid_member_id,
        store_number 
      }
      let resp = await paidMemberApi.handleOrderRefund(params);
      if (resp.status === 'success') {
        this.$message.success({
          content: '操作成功',
          closable: true,
          duration: 3
        })
        this.searchNationalProduct()
      }
    },
  }
}
</script>

<style lang="less">
.template-repo-box {
  .btn-disabled{
    opacity: 0.38;
    cursor: not-allowed;
  }
  .pro-img-box {
    .table-pic-box {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      padding:10px 0;
      height: auto;
      overflow: hidden;
      text-align: center;
      box-sizing: border-box;
      .proImg {
        width: 2.5vw!important;
        height: 2.5vw!important;
      }
    }
  }
  .cascader-store {
   color:#515a6e;
  }
}
.ml5 {
  margin-left: 5px;
}
.mr5 {
  margin-right: 5px;
}

</style>
