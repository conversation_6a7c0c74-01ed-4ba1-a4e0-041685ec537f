// 基础类型选项
export const baseTypes = [
  { id: 'store', name: "常去门店" },
  { id: 'regtime', name: "注册时间" },
  { id: 'orderdate', name: "订单日期" },
  { id: 'amount', name: "订单金额" },
  { id: 'product', name: "单个商品" },
  { id: 'address', name: "收货地址" },
  { id: 'points', name: "历史积分余额" },
  { id: 'history', name: "90天前订单" },
  { id: 'profile', name: "个人信息" }
];

// 判断条件选项
export const judges = [
  { id: 'gt', name: "大于" },
  { id: 'lt', name: "小于" },
  { id: 'eq', name: "等于" },
  { id: 'gte', name: "大于等于" },
  { id: 'lte', name: "小于等于" },
  { id: 'contains', name: "包含" },
  { id: 'noNeed', name: "无条件" },
  // { id: 'isBefore', name: "之内" },
  // { id: 'isAfter', name: "之外" },
];

// 选择类型选项
export const selectTypes = [
  { id: 'channel', name: "渠道" },
  { id: 'days', name: "天数" },
  { id: 'orders', name: "订单笔数" },
  { id: 'quantity', name: "购买数量" },
  { id: 'total', name: "行商品总金额" },
  { id: 'addrlen', name: "地址字符长度" },
  { id: 'addrch', name: "地址中文字数" },
  { id: 'netamt', name: "净订单金额" },
  { id: 'name', name: "姓名" },
  { id: 'gender', name: "性别" },
  { id: 'birthday', name: "生日" }
];

// 渠道选项
export const channelOptions = [
  { id: 'mp', name: "mp&官网" },
  { id: 'tmall', name: "tmall" },
  { id: 'jd', name: "jd" },
  { id: 'pdd', name: "pdd" },
  { id: 'jddj', name: "jddj" },
  { id: 'store', name: "store" },
  { id: 'meituan', name: "美团" }
];

// 关系选项
export const relations = [
  { id: 'and', name: "且" },
  { id: 'or', name: "或" }
];
