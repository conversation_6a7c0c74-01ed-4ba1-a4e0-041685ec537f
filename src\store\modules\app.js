import { local } from '../../utils/util'

const state = {
  // 切换的当前门店
  currentStore: {},
  orderSearchParams: {},
  // awardActivityInfo: {
  //   curAwardIndex: null,
  //   curAwardInfo: null
  // },
}

const getters = {
  currentStore: state => state.currentStore,
  orderSearchParams: state => state.orderSearchParams,
  // awardActivityInfo: state => state.awardActivityInfo,
}

const actions = {

  // 存放门店
  setStore({ commit }, value) {
    if (!value) {
      console.warn('value is invalid')
      return
    }
    // 存入本地
    local.setStorage('STORE', value)
    commit('SETSTORE', value)
  },
  setOrderSearchParams({ commit }, value) {
    if (!value) {
      console.warn('value is invalid')
      return
    }
    commit('SETORDERPARAMS', value)
  },
  // setAwardActivityInfo({ commit }, value) {
  //   if (!value) {
  //     console.warn('value is invalid')
  //     return
  //   }
  //   console.log('actions 奖品活动信息', value)    
  //   commit('SETAWARDACTIVITYINFO', value)
  // },
}

const mutations = {
   // 存放切换的门店
   SETSTORE: (state, value) => {
     state.currentStore = value ? value : []
  },
  SETORDERPARAMS: (state, value) => {
    state.orderSearchParams = value ? value : {}
 },
  // SETAWARDACTIVITYINFO: (state, value) => {
  //   state.awardActivityInfo = value ? value : {}
  // },
}

export default {
  state,
  getters,
  actions,
  mutations
}