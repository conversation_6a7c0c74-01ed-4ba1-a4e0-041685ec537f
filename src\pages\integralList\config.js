// 判断必填项
const rule = {
  planName: '请填写规则名称',
  eventType: '请选择规则类型',
  startTime: '请填写开始时间',
  endTime: '请填写结束时间',
  formula: '请填写赠送积分',
  description: '请填写规则描述',
  amount: '请填写购物总价'
}
const modalTitle = {
  creat: '创建积分规则',
  edit: '编辑积分规则',
  views: '积分规则详情'
}
const modalDataFilter = {
  planName: '',
  description: '',
  startTime: '',
  endTime: '',
  eventType: '',
  formula: '',
  amount: ''
}

const rules = {
  planName: {
    show: false,
    error: '请输入规则名称'
  },
  description: {
    show: false,
    error: '请输入规则描述'
  },
  startTime: {
    show: false,
    error: '请输入开始时间'
  },
  endTime: {
    show: false,
    error: '请输入结束时间'
  },
  eventType: {
    show: false,
    error: '请选择规则类型'
  },
  formula: {
    show: false,
    error: '请输入赠送积分'
  }
  // amount: {
  //   show: false,
  //   error: '请输入购物金额'
  // }
}
const opBtnList = [
  {
    code: 'edit',
    text: '编辑',
    permissions: ['POINT_RULE_UPDATE']
  },
  {
    code: 'views',
    text: '查看',
    type: 'success',
    permissions: ['POINT_RULE_VIEW']
  }
]
const integralType = [{ type: 'REGISTER', name: '注册送积分' }, { type: 'PURCHASE', name: '购物送积分' }]
const statusType = [{ type: 'ENABLE', name: '已启用' }, { type: 'DISABLE', name: '未启用' }]
const switchState = {
  ENABLE: true,
  DISABLE: false
}
export default {
  rule,
  rules,
  opBtnList,
  modalTitle,
  statusType,
  switchState,
  integralType,
  modalDataFilter
}
