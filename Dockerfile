# Version 1
# base image
#FROM node:latest
FROM acrcsz.azurecr.cn/dktcn/node-base/node:22-alpine-2024-11-13

# maintainer
LABEL maintainer=<EMAIL>
LABEL org.opencontainers.image.authors=CS-CRM-ASIA

ENV HTTP_SERVER_VERSION 0.9.0
ENV PUBLIC_FOLDER /opt/www
ENV PATH_ROOT backoffice

ENV LISTEN_FILE_NAME CP7xJtbpSt
ENV LISTEN_FILE_HASH 82500a9a6a471c2932af39d90d583bfd

ENV LISTEN_FILE_NAME1 TlznJXuWah
ENV LISTEN_FILE_HASH1 0e80e25bcdd715402006fb1233adee43
ENV LISTEN_FILE_NAME2 a1e40706492d4c1591aead6aeb1dec18
ENV LISTEN_FILE_HASH2 a1e40706492d4c1591aead6aeb1dec18
ENV LISTEN_FILE_NAME3 umNO3lDDkF
ENV LISTEN_FILE_HASH3 c51d5285a7d7ce6870194d886c6e0da9
ENV LISTEN_FILE_NAME4 150aae9c4958b6c0e9a5c387eb64bc05
ENV LISTEN_FILE_HASH4 150aae9c4958b6c0e9a5c387eb64bc05


RUN mkdir -p $PUBLIC_FOLDER/$PATH_ROOT/

COPY dist $PUBLIC_FOLDER/$PATH_ROOT/
RUN echo ${LISTEN_FILE_HASH} > $PUBLIC_FOLDER/${LISTEN_FILE_NAME}.txt
RUN echo ${LISTEN_FILE_HASH1} > $PUBLIC_FOLDER/${LISTEN_FILE_NAME1}.txt
RUN echo ${LISTEN_FILE_HASH2} > $PUBLIC_FOLDER/${LISTEN_FILE_HASH2}.html
RUN echo ${LISTEN_FILE_HASH3} > $PUBLIC_FOLDER/${LISTEN_FILE_NAME3}.txt
RUN echo ${LISTEN_FILE_HASH4} > $PUBLIC_FOLDER/${LISTEN_FILE_HASH4}.html

EXPOSE 8080

# RUN npm config set proxy ${http_proxy} && npm config set https-proxy ${https_proxy}
#RUN npm config set registry http://registry.npmjs.org/
#RUN npm install phantomjs-prebuilt --ignore-scripts
#RUN npm install --loglevel verbose
#RUN npm audit fix

RUN unset http_proxy https_proxy && npm install --dd -g --registry=https://nexus-cn.dktapp.cloud/repository/npm-group/ http-server@0.9.0

CMD http-server $PUBLIC_FOLDER
