/**
 * jsdoc-vue 配置文件
 */
module.exports = {
  "tags": {
    "allowUnknownTags": true,
    // 指定所用词典
    "dictionaries": [
      "jsdoc"
    ]
  },
  // 查找文件的深度 需要用 -r 参数
  "recurseDepth": 10,
  "source": {
    // 需要编译的文件路径
    "include": [
      "./src/components"
    ],
    "includePattern": ".+\\.(vue)$",
    "excludePattern": "(^|\\/|\\\\)_"
  },
  // 插件 编译vue模版
  "plugins": [
    "./plugins/jsdoc-vue"
  ],
  "templates": {
    "cleverLinks": false,
    "monospaceLinks": true,
    "useLongnameInNav": false,
    "showInheritedInNav": true
  },
  // 文档输出使用 minami模版
  "opts": {
    "destination": "./doc",
    "encoding": "utf8",
    "private": true,
    "recurse": true,
    "template": "./node_modules/minami"
  }
}