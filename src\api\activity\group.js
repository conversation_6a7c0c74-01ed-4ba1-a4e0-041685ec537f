/**
 * 集团活动模版
 */
import fetch from '@/utils/network/fetch'

const createOrUpdateTemplate = (body = {}) => {
  if (body.id) {
    return fetch(`/api/v1/sports/template/${body.id}`, { method: 'put', data: JSON.stringify(body) })
  } else {
    return fetch('/api/v1/sports/template', { method: 'post', data: JSON.stringify(body) })
  }
}

const disableTemplate = id => {
  return fetch(`/api/v1/sports/${id}`, { method: 'delete' })
}

const getTemplateById = id => {
  return fetch(`/api/v1/sports/${id}`)
}

const searchTemplate = (query = {}) => {
  return fetch('/api/v1/sports', { data: query })
}

const countNationalStarTemplate = id => {
  if (id) {
    return fetch(`/api/v1/sports/starCount?id=${id}`)
  } else {
    return fetch(`/api/v1/sports/starCount`)
  }
}

export default {
  createOrUpdateTemplate,
  disableTemplate,
  getTemplateById,
  searchTemplate,
  countNationalStarTemplate
}
