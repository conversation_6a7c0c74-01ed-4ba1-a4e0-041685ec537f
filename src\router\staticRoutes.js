/**
 * 静态路由配置
 */

// 元信息设置(区分环境)
const env = process.env.NODE_ENV
const meta = {
  requiresAuth: env === 'development' ? false : true,
  keepAlive: true
}

// common layout
const layout = () => import('@/layouts/Layout')

// 左边菜单路由
const appRouter = [
  {
    path: '/',
    name: 'paidMember',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'paidMemberTemplate',
        name: 'paidMemberTemplate',
        meta: {
          ...meta
        },
        component: () => import('@/pages/paidMember/template/index')
      },
      {
        path: 'storeOpenCard',
        name: 'storeOpenCard',
        meta: {
          ...meta
        },
        component: () => import('@/pages/paidMember/storeOpenCard/index')
      },
      {
        path: 'orderManagement',
        name: 'orderManagement',
        meta: {
          ...meta
        },
        component: () => import('@/pages/paidMember/orderManagement/index')
      },
      {
        path: 'orderDetail/:id',
        name: 'orderDetail',
        meta: {
          ...meta
        },
        component: () => import('@/pages/paidMember/orderDetail/index')
      }
    ]
  },
  {
    path: '/',
    name: 'product',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'templateRepo',
        name: 'templateRepo',
        meta: {
          ...meta
        },
        component: () => import('@/pages/product/template/index')
      },
      {
        path: 'storeProduct',
        name: 'storeProduct',
        meta: {
          ...meta
        },
        component: () => import('@/pages/product/store/index')
      }
    ]
  },
  {
    path: '/',
    name: 'order',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'productOrder',
        name: 'productOrder',
        meta: {
          ...meta
        },
        component: () => import('@/pages/order/index')
      }
    ]
  },
  {
    path: '/',
    name: 'integralList',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'integralList',
        name: 'integralList',
        meta: {
          ...meta
        },
        component: () => import('@/pages/integralList/index')
      }
    ]
  },
  {
    path: '/',
    name: 'banner',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'bannerMP/:bannerType',
        name: 'bannerMP',
        meta: {
          ...meta
        },
        component: () => import('@/pages/banner/index'),
        props: true
      },
      {
        path: 'bannerMPM/:bannerType',
        name: 'bannerMPM',
        meta: {
          ...meta
        },
        component: () => import('@/pages/banner/index'),
        props: true
      }
    ]
  },
  {
    path: '/',
    name: 'risk',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'riskNameList',
        name: 'riskNameList',
        meta: {
          ...meta
        },
        component: () => import('@/pages/risk/nameList'),
        props: true
      },
      {
        path: 'riskModel',
        name: 'riskModel',
        meta: {
          ...meta
        },
        component: () => import('@/pages/risk/modelView'),
        props: true
      },
      {
        path: 'riskRules',
        name: 'riskRules',
        meta: {
          ...meta
        },
        component: () => import('@/pages/risk/rulesList'),
        props: true
      },
    ]
  },
  {
    path: '/',
    name: '',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'luckyDraw',
        name: 'luckyDraw',
        meta: {
          ...meta
        },
        component: () => import('@/pages/luckyDraw/index')
      }
    ]
  },
  {
    path: '/',
    name: '',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'luckyDrawDetail/:id',
        name: 'luckyDrawDetail',
        meta: {
          ...meta
        },
        component: () => import('@/pages/luckyDraw/profile/luckyDrawDetail/index')
      }
    ]
  },
  {
    path: '/luckyDraw',
    name: 'luckyDrawManage',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'activityManage',
        name: 'activityManage',
        meta: {
          ...meta
        },
        component: () => import('@/pages/luckyDraw/activityManage')
      },
      {
        path: 'awardActivityConfig',
        name: 'awardActivityConfig',
        meta: {
          ...meta
        },
        component: () => import('@/pages/luckyDraw/awardActivityConfig')
      },
      {
        path: 'editSingleAward/:id',
        name: 'editSingleAward',
        meta: {
          ...meta
        },
        component: () => import('@/pages/luckyDraw/editSingleAward')
      },
    ]
  },
  {
    path: '/',
    name: 'activity',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'galleryActivity',
        name: 'galleryActivity',
        meta: {
          ...meta
        },
        component: () => import('@/pages/activity/gallery/index')
      },
      {
        path: 'groupActivity',
        name: 'groupActivity',
        meta: {
          ...meta
        },
        component: () => import('@/pages/activity/group/index')
      },
      {
        path: 'storeActivity',
        name: 'storeActivity',
        meta: {
          ...meta
        },
        component: () => import('@/pages/activity/store/index')
      }
    ]
  },
  {
    path: '/',
    name: 'customer',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'customerSearch',
        name: 'customerSearch',
        meta: {
          ...meta
        },
        component: () => import('@/pages/customer/index')
      }
    ]
  },
  {
    path: '/',
    name: 'report',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'storeProductReport',
        name: 'storeProductReport',
        meta: {
          ...meta
        },
        component: () => import('@/pages/report/store_product')
      },
      {
        path: 'sessionReport',
        name: 'sessionReport',
        meta: {
          ...meta
        },
        component: () => import('@/pages/report/session_report')
      },
      {
        path: 'orderReport',
        name: 'orderReport',
        meta: {
          ...meta
        },
        component: () => import('@/pages/report/order_report')
      },
      {
        path: 'luckyDrawReport',
        name: 'luckyDrawReport',
        meta: {
          ...meta
        },
        component: () => import('@/pages/report/lucky_draw_report')
      }
    ]
  },
  {
    path: '/',
    name: 'system',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'role',
        name: 'role',
        meta: {
          ...meta
        },
        component: () => import('@/pages/role/role/index')
      }
    ]
  },
  {
    path: '/',
    name: 'myDecathlon',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'myDecathlon',
        name: 'myDecathlonPage',
        meta: {
          ...meta
        },
        component: () => import('@/pages/myDecathlon')
      }
    ]
  },
  {
    path: '/',
    name: 'wecomDecathlon',
    meta: {
      ...meta
    },
    component: layout,
    children: [
      {
        path: 'wecomDecathlon',
        name: 'wecomDecathlonPage',
        meta: {
          ...meta
        },
        component: () => import('@/pages/wecomDecathlon')
      }
    ]
  }
]

// profile
const profileRouter = []

// 模拟器路由
const simulatorRouter = []

const routes = [
  {
    path: '/login',
    name: 'login',
    meta: {
      requiresAuth: true,
      keepAlive: true
    },
    component: () => import('@/pages/login')
  },
  {
    path: '/error',
    component: () => import('@/pages/error')
  },
  ...appRouter,
  ...profileRouter
]

export default routes
