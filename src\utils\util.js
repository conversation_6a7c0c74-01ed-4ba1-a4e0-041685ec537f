/*
 * 常用工具
 */
import localConfig from '@/config/localConfig'
import sessionConfig from '@/config/sessionConfig'

const getDecimalLength = (num) => {
  const parts = num.toString().split('.');
  return parts.length > 1 ? parts[1].length : 0;
};

const toInteger = (num, multiplier) => {
  return Math.round(num * multiplier);
};

const getMultiplier = (num1, num2) => {
  const num1Digits = getDecimalLength(num1);
  const num2Digits = getDecimalLength(num2);
  return Math.pow(10, Math.max(num1Digits, num2Digits));
};

export const addNum = (num1, num2) => {
  const multiplier = getMultiplier(num1, num2);
  return (toInteger(num1, multiplier) + toInteger(num2, multiplier)) / multiplier;
};

export const subtractNum = (num1, num2) => {
  const multiplier = getMultiplier(num1, num2);
  return (toInteger(num1, multiplier) - toInteger(num2, multiplier)) / multiplier;
};

export const multiplyNum = (num1, num2) => {
  const num1Digits = getDecimalLength(num1);
  const num2Digits = getDecimalLength(num2);
  const multiplier = Math.pow(10, num1Digits + num2Digits);
  return (toInteger(num1, Math.pow(10, num1Digits)) * toInteger(num2, Math.pow(10, num2Digits))) / multiplier;
};

export const divideNum = (num1, num2) => {
  const num1Digits = getDecimalLength(num1);
  const num2Digits = getDecimalLength(num2);
  const multiplier = Math.pow(10, num2Digits - num1Digits);
  return toInteger(num1, Math.pow(10, num1Digits)) / toInteger(num2, Math.pow(10, num2Digits)) * multiplier;
};

// 根据URL参数解析得到Object
export const getObjectByUrlParams = (paramsString, joinChar = '&') => {
  if (!paramsString) return null

  const params = {}
  const paramsArray = paramsString.split(joinChar)
  for (const item of paramsArray) {
    const itemArray = item.split('=')
    params[itemArray[0]] = itemArray[1] || ''
  }
  return params
}

/**
 * 封装sessionStorage
 */
export const session = {
  // 存储
  setStorage(key, value) {
    if (!sessionConfig[key]) throw Error('plase configure key /src/comConfig/sessionConfig')
    let data = {
      value: value
    }
    sessionStorage.setItem(key, JSON.stringify(data))
  },

  // 获取
  getStorage(key) {
    if (!sessionConfig[key]) throw Error('plase configure key /src/comConfig/sessionConfig')
    let data = sessionStorage.getItem(key)
    if (data) {
      return JSON.parse(data).value
    } else {
      return null
    }
  },

  // 删除
  removeStorage(key) {
    if (!sessionConfig[key]) throw Error('plase configure key /src/comConfig/sessionConfig')
    // 删除
    sessionStorage.removeItem(key)
  }
}

/**
 * 封装localStorage
 */
export const local = {
  // 存储
  setStorage(key, value, timer) {
    if (!localConfig[key]) throw Error('plase configure key /src/comConfig/localConfig')

    let data = {
      value: value,
      timer: timer * 60 * 1000,
      createAt: new Date().getTime()
    }
    localStorage.setItem(key, JSON.stringify(data))
  },

  // 获取
  getStorage(key) {
    if (!localConfig[key]) throw Error('plase configure key /src/comConfig/localConfig')

    let data = localStorage.getItem(key)
    let currentTimer = new Date().getTime()
    if (data) {
      if (data.timer && data.createAt + data.timer <= currentTimer) {
        localStorage.removeItem(key)
      } else {
        return JSON.parse(data).value
      }
    }
    return null
  },

  // 删除
  removeStorage(key) {
    if (!localConfig[key]) throw Error('plase configure key /src/comConfig/localConfig')
    // 删除
    localStorage.removeItem(key)
  }
}


// 判断权限
export const checkPermissions = list => {
  let localPermissions = local.getStorage('PERMISSIONS')
  let permissions = localPermissions ? localPermissions : []
   for (let per of list) {
     // 权限列表中属否存在该权限
     if (permissions.indexOf(per) > -1) {
       return true
     }
   }
  // 返回false, 说明用户没有该权限
  return false
}

/**
 * 获取元素类型
 */
export const getType = value => {
  let toString = Object.prototype.toString
  let map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object'
  }
  if (value instanceof Element) {
    return 'element'
  }
  return map[toString.call(value)]
}

/**
 * 深拷贝
 */
export const deepClone = value => {
  let type = getType(value)
  let objc = null
  switch (type) {
    case 'array':
      objc = []
      value.forEach(item => {
        objc.push(item)
      })
      break
    case 'object':
      objc = {}
      for (let key in value) {
        if (object.hasOwnProperty(key)) {
          objc[key] = deepClone(value[key])
        }
      }
      break
    default:
      return value
  }
  return objc
}

/**
 * 正则手机号
 */
export const isPhone = value => {
  return /(^1([358][0-9]|4[579]|66|7[0135678]|9[89])[0-9]{8})$/.test(value)
}

/**
 * 正则邮箱
 */
export const isEmail = value => {
  return /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i.test(value)
}

/**
 * 是否值正数
 */
export const isPositive = value => {
  return /^[1-9]+[0-9]*$/.test(value)
}

// 填写项排除
export const checkFill = value => {
  for (let key in value) {
    value[key]['error'] = !value[key]['value']
  }
}

// 填写项判断 符合规则: ture
export const judgeFill = value => {
  for (let key in value) {
    if (value[key].error) {
      return false
    }
  }
  return true
}

export const gmtToString = value => {
  if (!value) {
    return value
  } else {
    let date = new Date(value)
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
  }
}


// 格式化时间 时间戳格式 返回
export const formatDate = t => {
  var time = new Date(t)
  var year = time.getFullYear()
  var month = time.getMonth() + 1
  var date = time.getDate()
  var hour = time.getHours()
  var minute = time.getMinutes()
  var second = time.getSeconds()
  return year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
}
// 表格size选项
export const pageSizeOpts = [10, 20, 50, 100]

Date.prototype.format = function(fmt) {
  var o = {
    'M+': this.getMonth() + 1, //月份
    'd+': this.getDate(), //日
    'h+': this.getHours(), //小时
    'm+': this.getMinutes(), //分
    's+': this.getSeconds(), //秒
    S: this.getMilliseconds() //毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return fmt
}
