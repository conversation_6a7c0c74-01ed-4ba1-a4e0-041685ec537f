<template>
  <div class="container">
    <i-modal footer-hide width="1000" :mask-closable="false" :closable="false" :title="title" v-model="showDialog">
      <div class="header mt20 mb30">
        <i-input v-model="modelCode" class="search mr15" search enter-button placeholder="精确查询(Model Code)" @on-search="searchModel" />
      </div>
      <i-table  border highlight-row :columns="productColumns" :data="productList" @on-row-click="rowClick" @on-current-change="selectProduct" >
        <template slot-scope="{ index }" slot="id">
          <i-checkbox v-model="productList[index].isSelect" />
        </template>
        <template slot-scope="{ index }" slot="title">
						<i-input placeholder="请输入名称" v-model="productList[index].title" />
        </template>
        <template slot-scope="{ index }" slot="short_name">
						<i-input placeholder="请输入简称" v-model="productList[index].short_name" />
        </template>
        <template slot-scope="{ index }" slot="color">
						<i-input placeholder="请输入颜色" v-model="productList[index].color" />
        </template>
        <template slot-scope="{ index }" slot="size">
						<i-input placeholder="请输入尺码" v-model="productList[index].size" />
        </template>
        <template slot-scope="{ index }" slot="description">
						<i-input :autosize="{minRows:3,maxRows:8}" type="textarea" placeholder="请输入描述" v-model="productList[index].description" />
        </template>
        <template slot-scope="{ index }" slot="image_url">
          <div class="img-box">
            <i-upload
              class="img-upload-wrap"
              :before-upload="beforeUpload"
              :show-upload-list="false"
              action
              :max-size="1"
              name="file"
              type="select"
            >
              <div class="img-wrap">
                <img :src="productList[index].image_urls[0]" alt  class="cards-img" />
                <p class="change">更换</p>
              </div>
            </i-upload>
          </div>
        </template>
      </i-table>
      <!-- <div class="item-center justify-between mt20" v-if="selectedProduct">
        <i-form :model="template">
          <i-row>
            <i-col :span=12>
              <i-form-item label="名称" class="modal-item">
                <i-input v-model="template.title" class="text" placeholder="请输入产品名称" :maxlength="10" />
              </i-form-item>
            </i-col>
            <i-col :span=12>
              <i-form-item label="描述" class="modal-item">
                <i-input v-model="template.description" class="text" type="textarea" :autosize="{minRows:3,maxRows:8}" placeholder="请输入产品描述" :maxlength="255"/>
              </i-form-item>
            </i-col>
          </i-row>
        </i-form>
      </div> -->
      <div class="mt30 flex row justify-center">
        <i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
        <i-button type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
      </div>
    </i-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import paidMemberApi from '@/api/paidMember/index'

export default {
  name: 'template_repo_modal',
  props: {
    title: '',
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  watch: {
    currentStore(newVal, oldVal) {
    }
  },
  data() {
    return {
      showDialog: true,
      uploadFile: null,
      modelCode: '',
      currentChooseIndex: '',
      productList: [],     // DKT产品列表
      productColumns: [
        { title: '选择', slot: 'id', width: 70, align: 'center'},
        { title: 'Model Code', key: 'model_code', className: 'th-class' },
        { title: 'Item Code', key: 'item_code', className: 'th-class' },
        { title: '名称', slot: 'title', className: 'th-class',  },
        { title: '简称', slot: 'short_name', className: 'th-class' },
        { title: '颜色', slot: 'color', className: 'th-class' },
        { title: '尺码', slot: 'size', className: 'th-class' },
        { title: '描述', slot: 'description', className: 'th-class', width: 180 },
        { title: '图片', slot: 'image_url', className: 'pt5 th-class', fixed: 'right', width: 160 },
        // { title: '图片', className: 'pt5 th-class', fixed: 'right', width: 160, render: (h, params) => { return this.showRowImage(h, params.row.image_urls) }},
      ],

      selectedProduct: '',

      template: {
        title: '',
        description: '',
      },
    }
  },
  methods: {
    showRadioSelect(h, params) {
      let index = params.index;
      let flag = false;
      if (this.currentChooseIndex === index) {
        flag = true
      }
      let self = this
      return h('div', [
        h('i-radio', {
          props: {
            value: flag
          },
          on: {
            'on-change': () => {
              self.currentChooseIndex = index;
            }
          }
        })
      ])
    },
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('span', { class: 'span-pic-box' }, [h('img', { attrs: { src: imageUrls[0], class: 'table-img', width: '100px', height: '100px' } })])
    },
    beforeImgUpload(file) {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPGOrPNG) {
        this.$message.error('上传图片只能是 JPG，png 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPGOrPNG && isLt2M
    },
    beforeUpload(file) {
      this.uploadFile = file;
      let isValidPic = this.beforeImgUpload(file);
      if( !isValidPic ){ 
        return false; 
      } else {
        this.handleImgUpload();
      }
    },
     // 处理表单数据
    handleFormData(file) {
      let params = new FormData()
      params.append('file', this.uploadFile, this.uploadFile.name)
      return params
    },
    async handleImgUpload() {
      let objc = {
          isForm: true,
          method: 'post',
          data: this.handleFormData()
        }
				let resp = await paidMemberApi.uploadProcuctTemplateImage(objc);
        if (resp.status === 'success') {
          let image_url = resp.data.image_url;
          this.$set(this.productList[this.currentChooseIndex].image_urls, 0, image_url)
        } 
    },
    // 搜索DKT产品库
    async searchModel() {
      this.productList = [];
      if (this.modelCode) {
        let params = {
          model_code: this.modelCode,
          on_sale: true,
          type_id: 8
        }
        let resp = await paidMemberApi.searchModel(params)

        resp.data && resp.data.forEach( (item, index) => {
          if(!item.image_urls || item.image_urls.length < 1) {
            item.image_urls = []
          }
          item.isSelect = false; //初始化是否选中
        })
        this.productList = resp.data
        if (this.productList.length == 0) {
          this.$message.info({ content: '什么产品也没找到!', closable: true, duration: 3 })
        }
      } else {
        this.$message.warning({ content: '请输入模型编号', closable: true, duration: 3 })
      }
    },
    rowClick(currentRow, index) {
      this.currentChooseIndex = index;
    },
    // 表格行选中事件
    selectProduct(currentRow, index) {
    },
    
    // 获取当前选择的模板库产品（多选）
    getSelectedList() {
      if(this.productList && this.productList.length > 0) {
        const isSelectList = this.productList.filter(item => item.isSelect);
        const selectList = JSON.parse(JSON.stringify(isSelectList))
        selectList.forEach(curItem => {
          let hasIsSelctParam = curItem.hasOwnProperty('isSelect');
          if(hasIsSelctParam) {
            delete curItem.isSelect;
          }
        })
        return selectList || [];
      } else {
        return [];
      }
    },
    checkListHasEmptyParam(selectArr) {
      let curItem = null;
      let hasEmptyField = false;
      for(let i=0; i<selectArr.length; i++) {
        curItem = selectArr[i];
        const { title, short_name, description, image_urls } = curItem
        if (!title) {
          this.$message.warning({ content: '请填写名称', closable: true, duration: 3 });
          hasEmptyField = true;
          break;
        }
        if (!short_name) {
          this.$message.warning({ content: '请填写简称', closable: true, duration: 3 });
          hasEmptyField = true;
          break
        }
        if (!description) {
          this.$message.warning({ content: '请填写描述', closable: true, duration: 3 });
          hasEmptyField = true;
          break
        }
        if (!image_urls || (image_urls.length<1)) {
          this.$message.warning({ content: '请选择图片', closable: true, duration: 3 });
          hasEmptyField = true;
          break
        }
      }
      return hasEmptyField;
    },
    async certainClick() {
      const selectArr = this.getSelectedList();
      if(!selectArr || selectArr.length < 1) {
        this.$message.warning({ content: '请选择要添加的商品', closable: true, duration: 3 })
        return;
      }
      // 校验每行信息是否填写
      let hasEmptyParam = this.checkListHasEmptyParam(selectArr);
      if( hasEmptyParam ) { return; }
      let params = selectArr;
      let resp = await paidMemberApi.createTemplateProduct(JSON.stringify(params))
      if (resp.status == 'success') {
        this.$message.success({ content: '添加成功！', closable: true, duration: 3 })
        this.certain && this.certain()
      }
    },
    cancelClick() {
      this.showDialog = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
  }
}
</script>

<style lang="less" scoped>
.search {
  width: 100%;
}

.span-pic-box {
  display: block;
  height: 80px;
  .table-img {
    width: 80px;
    height: 60px;
    margin-top: 10px;
  }
}

.th-class {
  text-align: center!important;
  vertical-align: middle!important;
  min-width: 120px!important;
}
.img-box{
  display: flex; justify-content: center;
  .img-wrap{
    width: 80px; height: 80px;
    position: relative;
    cursor: pointer;
    display: flex;
    img{ width: 80px; object-fit: contain;}
    .change{ 
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%; height: 20px; background: rgba(0, 15, 23, 0.6); 
      font-size: 14px; color: #FFFFFF;
    }
  }
}
.text {
  width: 90%;
}
</style>
