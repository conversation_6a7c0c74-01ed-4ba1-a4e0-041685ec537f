<template>
	<div class="repo product-template-box">
		<i-modal :mask-closable="false" @on-cancel="cancelClick" footer-hide title="模板库搜索" v-model="show" width="1000">
			<div class="header mt20 mb30 flex row item-center justify-between">
				<div class="template-search-content flex row item-center justify-between">
					<i-input :autofocus="true" class="search mr15" clearable placeholder="模糊查询(支持Item Code、商品名称)" v-model="word" />
					<i-date-picker
						:format="format"
						:open="open"
						:type="dateType"
						@on-change="changePicker"
						@on-clear="closePicker"
						@on-ok="closePicker"
						style="margin-top:-3px"
					>
						<i-input
							@on-blur="closePicker"
							@on-focus="openPicker"
							class="text mr15"
							clearable
							placeholder="发布时间"
							v-model="displayDate"
						>
							<i-select @on-change="changeType" slot="prepend" style="width:50px;" v-model="dateType">
								<i-option value="month">月</i-option>
								<i-option value="date">日</i-option>
							</i-select>
						</i-input>
					</i-date-picker>
				</div>
				<div>
					<i-button @click="resetClick" class="mr15">重置</i-button>
					<i-button @click="handleSearch" class="mr15">搜索</i-button>
				</div>
			</div>

			<i-table :columns="productColumns" :data="productList" class="product-template-table-box" />
			<div style="margin-top: 10px; overflow: hidden">
				<div style="float: right;">
					<i-page
						:page-size="page.size"
						:page-size-opts="pageSizeOpts"
						:total="page.total"
						@on-change="changePageNo"
						@on-page-size-change="changePageSize"
						show-sizer
						show-total
					/>
				</div>
			</div>
		</i-modal>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { pageSizeOpts, isPositive } from '@/utils/util'

export default {
  name: 'batch_manage_modal',
  props: {
    opFrom: {
      onSale: false,
      type: '',
      batchNumber: '',
      currentSize: ''
    },
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      show: true,

      dateType: 'month',
      format: 'yyyy-MM',
      open: false,
      displayDate: '',

      word: '', // 模糊搜索字段
      searchForm: {
        itemCode: '',
        title: '',
        typeName: '',
        publishMonth: '',
        publishDate: ''
      },

      pageSizeOpts: pageSizeOpts,
      page: {
        no: 1,
        size: 10,
        total: 0
      },

      stock: '', // 商品库存

      productList: [], // 模板库产品列表
      opBtnList: [
        // 表格操作按钮
        { code: 'enable', text: '启用', type: 'primary' }
      ],
      productColumns: [
        {
          title: '商品图片',
          key: 'imageUrls',
          className: 'th-class th-class-img',
          render: (h, params) => {
            return this.showRowImage(h, params.row.productInfo.imageUrls)
          }
        },
        {
          title: 'Item Code',
          key: 'itemCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.productInfo.itemCode)
          }
        },
        {
          title: '商品名称',
          key: 'itemCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.productInfo.title)
          }
        },
        {
          title: '兑换燃值',
          key: 'itemCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.points)
          }
        },
        {
          title: '发布日期',
          key: 'publishTime',
          className: 'th-class',
          render: (h, params) => {
            return h(
              'span',
              new Date(params.row.publishTime).format('yyyy-MM-dd hh:mm:ss')
            )
          }
        },
        {
          title: '操作',
          key: 'action',
          className: 'th-class',
          fixed: 'right',
          width: 120,
          render: (h, params) => {
            return this.createOpBtn(h, params.row)
          }
        }
      ]
    }
  },
  mounted() {
    this.searchForm.typeName = this.opFrom.type
    this.searchNationalRepo()
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  methods: {
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'tables-pic-box' }, [
        h('img', {
          attrs: {
            src: imageUrls[0],
            class: 'table-img mt5',
            width: '45px',
            height: '45px'
          }
        })
      ])
    },

    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      this.opBtnList.forEach(value => {
        operations.push(
          h(
            'i-button',
            {
              class: 'ml15',
              props: { type: value.type },
              on: {
                click: () => {
                  this.stock = ''
                  this.rowOperation(value.code, row)
                }
              }
            },
            value.text
          )
        )
      })
      return h('div', operations)
    },

    // 表格项操作
    rowOperation(code, row) {
      switch (code) {
        case 'enable':
          this.$modal.confirm({
            render: h => {
              return h('i-input', {
                props: {
                  value: this.stock,
                  autofocus: true,
                  placeholder: '请输入此商品的库存'
                },
                on: {
                  input: val => {
                    this.stock = val
                  }
                }
              })
            },
            onOk: () => {
              this.saveBatchProduct(row)
            }
          })
          break
      }
    },

    openPicker() {
      this.open = true
    },
    changeType(type) {
      switch (this.dateType) {
        case 'month':
          this.format = 'yyyy-MM'
          break
        case 'date':
          this.format = 'yyyy-MM-dd'
          break
      }
      this.displayDate = ''
    },
    changePicker(date) {
      this.displayDate = date
      switch (this.dateType) {
        case 'month':
          this.searchForm.publishMonth = date
          this.searchForm.publishDate = ''
          break
        case 'date':
          this.searchForm.publishMonth = ''
          this.searchForm.publishDate = date
          break
      }
      this.open = false
    },
    // 关闭日期选择
    closePicker() {
      this.open = false
    },
    // 重置搜索条件
    resetClick() {
      this.searchForm = {}
      this.word = ''
      this.displayDate = ''
      this.searchNationalRepo()
    },
    handleSearch() {
      this.searchForm['itemCode'] = this.word
      this.searchForm['title'] = this.word
      this.searchNationalRepo()
    },

    // 分页操作
    changePageNo(no) {
      this.page.no = no
      this.searchNationalRepo()
    },
    changePageSize(size) {
      this.page.size = size
      this.searchNationalRepo()
    },

    // 搜索模板产品库
    async searchNationalRepo() {
      this.searchForm['pageNo'] = this.page.no
      this.searchForm['pageSize'] = this.page.size
      let resp = await this.$api.searchNationalProduct(this.searchForm)
      this.productList = resp.data.items
      this.page.total = resp.data.total
    },

    async saveBatchProduct(row) {
      let productCreate = {}
      productCreate.productInfo = row.productInfo
      productCreate['stock'] = this.stock
      productCreate['batchNumber'] = this.opFrom.batchNumber
      productCreate['dktStoreNumber'] = this.currentStore.dktStoreNumber
      productCreate['nationalProductId'] = row.id
      if (this.opFrom.currentSize >= 5) {
        this.$message.warning({
          content: '普通产品最多5个',
          closable: true,
          duration: 3
        })
        return
      }
      if (!isPositive(productCreate.stock)) {
        this.$message.warning({
          content: '库存必须是正数',
          closable: true,
          duration: 3
        })
        return
      } else if (productCreate.stock > 1500) {
        this.$message.warning({
          content: '库存不得大于1500',
          closable: true,
          duration: 3
        })
        return
      }
      let resp = await this.$api.createLevelProduct(
        JSON.stringify(productCreate)
      )
      if (resp.status == 'success') {
        this.certain && this.certain()
      }
    },
    cancelClick() {
      this.show = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
  }
}
</script>

<style lang="less" scoped>
.template-search-content {
  width: 720px;
}
.product-template-table-box {
  .tables-pic-box {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    line-height: 60px;
    margin: 0 auto;
    .table-img {
      margin-top: 10px !important;
    }
  }

  .th-class {
    text-align: center !important;
    vertical-align: middle !important;
    min-width: 120px !important;
  }
}
.mt5 {
  margin-top: 5px;
}
</style>
