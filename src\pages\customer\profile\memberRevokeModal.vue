<template>
  <div class="phone-modify-wrapper">
    <i-modal :title="modalTitle" class="vertical-center-modal" footer-hide :mask-closable="false" v-model="visible" @on-cancel="cancelClick">
      <div class="modal-container">
        <p class="red-tips">
          注销前，请告知会员在小程序、电商平台进行账号解绑。<br />请确认会员燃值是否为负。<br />
          请再次确认会员信息，以及顾客是否确实需要注销。<br />注意：所有的注销操作将被记录。
        </p>
        <div class="member-info">
          <p class="mr50">会员卡号：{{customer.dktCardNo}}</p>
          <p>手机号码：{{customer.dktMobile}}</p>
        </div>
        <div class="member-info">
          <p class="mr50">
            礼品卡余额：
            <span :style="{ color: accountSummary.total_gift_card_balance > 0 ? '#ff0000' : '#515a6e' }">{{accountSummary.total_gift_card_balance}}元</span>
            <span style="color: red">{{accountSummary.total_gift_card_balance > 0 ? '（大于0，请会员使用后再注销）':''}}</span>
          </p>
        </div>
        <div class="member-info">
          <p class="mr50">
            积分余额：
            <span :style="{ color: accountSummary.point_balance < 0 ? '#ff0000' : '#515a6e' }">{{accountSummary.point_balance}}分</span>
            <span style="color: red">{{accountSummary.point_balance < 0 ? '（负积分账户请谨慎操作）':''}}</span>
          </p>
        </div>
        <i-form :label-width="88" label-position="left">
          <i-row>
            <i-col span="24">
              <i-form-item class="integralList-form" label="注销原因：">
                <div style="display: flex;">
                  <i-select v-model="modalData.revokeReason" placeholder="请选择注销原因" style="width: 270px;">
                    <i-option v-for="item in revokeReasonList" :value="item.value" :key="item.value">
                      {{item.label}}
                    </i-option>
                  </i-select>
                </div>
              </i-form-item>
            </i-col>
          </i-row>
          <i-row>
            <i-col>
              <i-form-item class="integralList-form" label="备注：">
                <div style="display: flex;">
                  <i-input
                    class="integralList-form-input"
                    placeholder="请输入备注，至多200个字符"
										type="textarea"
                    :maxlength="200"
                    :autosize="{minRows:3,maxRows:8}"
                    v-model="modalData.remark"
                  />
                </div>
              </i-form-item>
            </i-col>
          </i-row>
          <div class="bottom-tips">
            <p>你可以感谢顾客的支持和信任，并跟他说，我们非常期待您下次光临我们的门店或访问迪卡侬官网及小程序。</p>
            <p class="mt10">同时，请提醒顾客：</p>
            <ul class="mt10 ul-list">
              <li><i class="dot"></i>您将不会再收到来自迪卡侬的任何通讯信息；</li>
              <li class="red"><i class="dot"></i>如您账号有礼品卡余额，请在使用完后再注销；</li>
              <li class="red"><i class="dot"></i>您将无法再在线查看您的订单、优惠券、积分等所有信息；</li>
              <li class="red"><i class="dot"></i>您的会员注销是永久且不可逆的。</li>
            </ul>
          </div>
          <div class="bottom-btns mt10 text-center">
            <i-button @click="cancelClick" class="cancel-btn mr30">取消</i-button>
            <i-button @click="goShowReconfirmModal" type="primary" class="confirm-btn">提交</i-button>
          </div>
        </i-form>
      </div>
		</i-modal>

    <!-- 二次确认弹窗 -->
    <i-modal :title="reconfirmModalTitle" class="vertical-center-modal" footer-hide :mask-closable="false" v-model="reconfirmVisible" @on-cancel="cancelReconfirmClick">
      <div class="modal-container">
        <div class="member-info">
          <p class="mr50">会员卡号：{{customer.dktCardNo}}</p>
          <p>手机号码：{{customer.dktMobile}}</p>
        </div>
        <p class="red-tips reconfirm-tips">注意：会员注销是不可逆的！</p>

        <div class="bottom-btns mt10 text-center">
          <i-button @click="cancelReconfirmClick" class="cancel-btn mr30">取消</i-button>
          <i-button @click="goRevoke" type="primary" class="confirm-btn">确认</i-button>
        </div>
      </div>
		</i-modal>

  </div>
</template>

<script>
import cfg from '../config'

export default {
  name: 'PhoneModifyModal',
  props: {
    customer: {
      type: Object,
      default: () => {
        return {
          dktCardNo: '',  // 会员卡号
          dktMobile: '', // 手机号
          dktPersonId: '', // personId
        };
      }
    }
  },
  data() {
    return {
      visible: true,
      reconfirmVisible: false,
      modalTitle: '注销会员',
      reconfirmModalTitle: '确认注销会员',
      revokeReasonList: [

      ],
      modalData: {
        revokeReason: '',  // 注销原因
        remark: ''  // 备注
      },
      accountSummary: {
        point_balance:  0,
        total_gift_card: 0,
        total_gift_card_balance:0
      }
    };
  },
  mounted() {
    this.deleteReason();
    this.getAccountSummary();
  },
  methods: {
    async getAccountSummary() {
      const { dktPersonId, dktCardNo } = this.customer;
      const queryParams = {
        cardNumber: dktCardNo
      }
      let res = await this.$api.queryAccountSummary(dktPersonId, queryParams);
      this.accountSummary = res.data;
    },
    async deleteReason() {
      let res = await this.$api.deleteReasonList();
      // 前端意义不大，后端意义大 后段返回的数据是一个数组，数组里面是字符串，要统一成str，不方便塞obj。所以要放在前端mapping
      this.revokeReasonList = res.data.map(item => {
        return cfg.memberRevokeReasonList.find(cfgItem =>
          cfgItem.value === item
        );
      });
    },
    cancelClick() {
      this.$emit('hideRevoke');
    },
    cancelReconfirmClick() {
      this.reconfirmVisible = false;
    },
    goShowReconfirmModal() {
      // 注销原因必选
      if (!this.modalData.revokeReason) {
        this.$message.error('请选择注销原因');
        return;
      }
      // 当选择其他原因时，备注必填
      if (this.modalData.revokeReason === '其他' && !this.modalData.remark) {
        this.$message.error('请填写备注');
        return;
      }
      // 弹出二次确认弹窗
      this.reconfirmVisible = true;
    },
    async goRevoke() {
      const { id, dktPersonId } = this.customer;
      const { revokeReason, remark } = this.modalData;
      if(dktPersonId) {
        const params = {
          reason: revokeReason,
          comment: remark,
          membership_id: id || null
        }
        let res = await this.$api.revokeMember(dktPersonId, params);
        if(res && res.status === 'success') {
          this.$message.success('会员注销成功');
          setTimeout(() => {
            this.$router.go(0)
          }, 1000);
        }
      } else {
        this.$message.error('未能获取到personId信息');
      }

    }
  },
};
</script>

<style lang="less" scoped>
  .vertical-center-modal{
    .modal-container{
      padding: 0 10px;
    }
    /deep/.ivu-modal-body{
      .red-tips{
        color: red;
        font-weight: bolder;
        padding: 0 0 14px;
        &.reconfirm-tips{
          padding: 10px 0 18px;
          text-align: center;
        }
      }
      .member-info{
        display: flex; padding: 0 0 14px;
        // color: #c5c8ce;
      }
      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }
      // input[type="number"]{
      //   -moz-appearance: textfield;
      // }
      .ivu-input-wrapper{
        width: 270px;
      }
      .bottom-tips{
        padding: 0 0 14px;
        .ul-list{
          li{
            list-style: none;
            display: flex; align-items: center;
            &.red{
              color: red;
              .dot{ background-color: red; }
            }
            .dot{
              display: inline-block;
              width: 6px;
              height: 6px;
              background-color: #000;
              border-radius: 50%;
              margin: 0 20px 0 14px;
            }
          }
        }
      }
      .bottom-btns{
        .cancel-btn, .confirm-btn{
          width: 100px;
        }

      }
    }
    .sms-code{
      margin-left: 14px;
      &.count-down{
        padding: 0 15px;
        line-height: 13px;
      }
    }
  }

</style>
