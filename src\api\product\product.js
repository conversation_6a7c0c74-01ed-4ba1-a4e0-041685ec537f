import fetch from '@/utils/network/fetch'

// 搜索Meta产品库(一般商品/促销商品)
const searchModel = (data = {}) => {
  return fetch('/api/v1/products/model', { data: data })
}

// 创建促销产品
const createOnSaleProduct = (body = {}) => {
  return fetch('/api/v1/products/onsale', { method: 'post', data: body })
}

// 使用模板库产品创建L1-L4级别的产品
const createLevelProduct = (body = {}) => {
  return fetch('/api/v1/products', { method: 'post', data: body })
}

// 更新产品库产品
const updateProduct = (id, body = {}) => {
  return fetch(`/api/v1/products/${id}`, { method: 'put', data: body })
}

// 下架产品
const disableProduct = (id, storeNo) => {
  return fetch(`/api/v1/products/${id}?dktStoreNumber=${storeNo}`, { method: 'delete' })
}

// 搜索产品
const searchProduct = (query = {}) => {
  return fetch('/api/v1/products', { data: query })
}

// 月份列表
const listBatch = () => {
  return fetch('/api/v1/products/batches')
}

// 搜索某门店的某月份的产品列表
const searchBatchProducts = (batch, storeNo) => {
  return fetch(`/api/v1/products/batches/${batch}?dktStoreNumber=${storeNo}`)
}

// 发布某门店的某批次产品
const publishBatchProduct = (batch, storeNo) => {
  return fetch(`/api/v1/products/batches/${batch}?dktStoreNumber=${storeNo}`, { method: 'put' })
}

export default {
  searchModel,
  createOnSaleProduct,
  createLevelProduct,
  updateProduct,
  disableProduct,
  searchProduct,
  listBatch,
  searchBatchProducts,
  publishBatchProduct
}
