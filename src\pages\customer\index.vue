<template>
  <div class="customer">
    <div class="header mt20 mb30">
      <div class="flex row item-center justify-between">
        <div>
          <i-input class="search mr15" clearable placeholder="客户查询(手机号、邮箱、迪卡侬会员卡号)" prefix="ios-search" v-model="word" />
          <i-button @click="searchCustomer" class="mr15">查询</i-button>
        </div>
      </div>
    </div>
    <i-table :columns="customerColumns" :data="customerList" border />
    <c-modal :cancel="modalCancel" :customer="customer" :title="title" v-if="showDetailDialog" />
  </div>
</template>

<script>
import cfg from './config'
import CustomerModal from './profile/modal'
import { checkFill, judgeFill } from '@/utils/util'

export default {
  name: 'customer_search',
  components: {
    'c-modal': CustomerModal
  },
  data() {
    return {
      showDetailDialog: false,
      customer: '',
      title: '',
      word: '',
      // 表格操作按钮
      opBtnList: [{ code: 'view', text: '查看', permissions: ['CUSTOMER_DETAIL_VIEW'] }],

      customerList: [], // 客户列表
      customerColumns: [
        { title: '姓名', key: 'dktName', className: 'th-class' },
        { title: '会员卡号', key: 'dktCardNo', className: 'th-class' },
        { title: '手机号', key: 'dktMobile', className: 'th-class' },
        {
          title: '性别',
          key: 'gender',
          className: 'th-class',
          render: (h, params) => {
            return h('span', cfg.convert(params.row.gender, cfg.genderList))
          }
        },
        { title: '出生日期', key: 'dktBirthday', className: 'th-class' },
        { title: '邮箱', key: 'dktEmail', className: 'th-class' },
        {
          title: '操作',
          key: 'action',
          className: 'th-class',
          fixed: 'right',
          render: (h, params) => {
            return this.createOpBtn(h, params.row)
          }
        }
      ]
    }
  },
  mounted() {
    const phone = this.$route.query.phone;
    if (phone) {
      this.word = phone; // 填充搜索框
      this.searchCustomer(); // 可选：自动执行搜索
    }
  },
  methods: {
    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      this.opBtnList.forEach(value => {
        operations.push(
          h(
            'i-button',
            {
              class: '',
              directives: [
                {
                  name: 'has',
                  value: value.permissions
                }
              ],
              on: {
                click: () => {
                  this.rowOperation(value.code, row)
                }
              }
            },
            value.text
          )
        )
      })
      return h('div', operations)
    },
    // 搜索用户信息
    async searchCustomer() {
      if (this.word) {
        let resp = await this.$api.searchCustomer({ word: this.word })
        this.customerList = resp.data
      } else {
        this.$modal.warning({ title: '提示', content: '请输入客户查询条件' })
      }
    },

    // 表格项操作
    rowOperation(code, row) {
      switch (code) {
        case 'view':
          this.viewCustomer(row)
          break
      }
    },

    // 查看用户详情
    async viewCustomer(row) {
      // 会员数据库中存在该会员
      if (row.id) {
        let resp = await this.$api.getCustomerDetail(row.id)
        this.customer = resp.data
      } else {
        this.customer = row
      }

      this.customer['genderCn'] = cfg.convert(this.customer.gender, cfg.genderList)
      this.title = '查看用户详情'
      this.showDetailDialog = true
    },
    modalCancel() {
      this.showDetailDialog = false
    }
  }
}
</script>

<style lang="less" scoped>
.customer {
  .search {
    width: 280px;
  }

  .th-class {
    text-align: center !important;
    vertical-align: middle !important;
    min-width: 120px !important;
  }
}
</style>
