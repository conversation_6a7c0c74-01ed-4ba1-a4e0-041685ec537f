<script>
import { DatePicker } from 'iview'
import RuleConfiguratorRule from '../components/ruleConfiguratorRule'
import LeftRulePanelRule from '../components/leftRulePanelRule'

export default {
  name: 'RuleEdit',
  components: {
    DatePicker,
    RuleConfigurator: RuleConfiguratorRule,
    LeftRulePanel: LeftRulePanelRule
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    isView: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'view' // view, edit, create
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      ruleList: null, // 改为从API获取，初始为null
      show: true,
      saveLoading: false,
      updateLoading: false, // 新增：API调用loading状态
      searchQuery: '',
      currentPage: 1,
      pageSize: 10,
      totalMembers: 0,
      removeModal: false,
      currentRow: null,
      formData: {
        id: '',
        name: '',
        description: '',
        // timeRange: [],
        status: 'active'
      },
      formRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        // timeRange: [
        //   { required: true, type: 'array', message: '请选择时间范围', trigger: 'change' }
        // ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      memberColumns: [
        { title: 'object_id', key: 'object_id', width: 150 },
        { title: '判断时间', key: 'create_time', width: 180 },
        { title: '场景', key: 'scene_name', width: 220 },
        { title: '规则', key: 'rule_name', width: 220 },
        { title: '规则描述', key: 'rule_desc', },
      ],
      memberList: []
    }
  },
  computed: {
    modalTitle() {
      return this.title || (this.isView ? '查看规则' : (this.id ? '编辑规则' : '新建规则'))
    },
    // 可编辑的系统参数（用于左侧面板，enable_config=true的参数）
    editableSystemParams() {
      if (!this.ruleList || !this.ruleList.param_list) {
        return []
      }

      const result = this.ruleList.param_list
        .filter(param => param.enable_config === true)
        .map(param => this.transformParamToRuleItem(param))

      console.log('editableSystemParams:', result)
      return result
    },

    // 不可编辑系统参数（用于规则配置器左侧，enable_config=false的参数）
    nonEditableSystemParamsForRule() {
      if (!this.ruleList || !this.ruleList.param_list) {
        return []
      }
      return this.ruleList.param_list
        .filter(param => param.enable_config === false)
        .map(param => ({
          key: param.param_name,
          name: param.description,
          value: param.param_name,
          param_type: param.param_type
        }))
    },
    // 可编辑系统参数（用于规则配置器右侧，enable_config=true的参数）
    editableSystemParamsForRule() {
      if (!this.ruleList || !this.ruleList.param_list) {
        return []
      }
      return this.ruleList.param_list
        .filter(param => param.enable_config === true)
        .map(param => ({
          key: param.param_name,
          name: param.description,
          value: param.param_name,
          param_type: param.param_type
        }))
    }
  },
  watch: {
    title: {
      handler(val) {
        if (val) {
          this.show = true
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initData()
    // 编辑模式下，主动同步表达式到规则编辑器
    if (this.ruleList && this.ruleList.expression) {
      this.handleRuleChange(this.ruleList.expression)
    }
  },
  methods: {
    async initData() {
      if (this.id) {
        console.log('初始化数据 - 编辑模式')
        await this.queryBasicInfo()
        await this.queryMemberList()
      } else {
        console.log('初始化数据 - 新建模式')
        // 新建模式下调用 queryRules 获取基础数据
        await this.queryRulesForCreate()
        this.formData = {
          id: '',
          name: '',
          description: '',
          status: 'active'
        }
      }
    },
    async queryBasicInfo() {
      try {
        // 使用从父组件传递过来的真实 rule_id
        const resp = await this.$api.queryRulesDetail(this.id)
        // const resp = {
        //   status: 'success',
        //   data: {
        //     "rule_id": 5,
        //     "rule_name": "会员常去门店",
        //     "trigger_type": "个人信息变更",
        //     "expression": "#storeList.contains(#store) && #mark > #markMax || (#store != '' && #store != null)", // && #markMax > #mark || (#store != '' && #store != null)
        //     "rule_desc": "会员常去门店属于相关门店",
        //     "enable_status": false,
        //     "update_time": "2025-07-21 09:29:34",
        //     "create_by": "admin",
        //     "param_list": [
        //       {
        //         "param_name": "storeList",
        //         "param_type": "list",
        //         "default_value": "[\"MP&官网\", \"Tmall\", \"JD\", \"抖音\", \"PDD\", \"线下门店\",\"JDDJ\"]",
        //         "value": "[\"MP&官网\", \"Tmall\", \"JD\", \"抖音\", \"PDD\",\"JDDJ\"]",
        //         "description": "会员常去门店",
        //         "enable_config": true
        //       },
        //       {
        //         "param_name": "store",
        //         "param_type": "string",
        //         "default_value": null,
        //         "description": "常去门店",
        //         "enable_config": false
        //       },
        //       {
        //         "param_name": "markMax",
        //         "param_type": "string",
        //         "default_value": "100",
        //         "description": "积分数值",
        //         "enable_config": true
        //       },
        //       {
        //         "param_name": "mark",
        //         "param_type": "string",
        //         "default_value": null,
        //         "description": "积分多少",
        //         "enable_config": false
        //       }
        //     ]
        //   }
        // }

        if (resp.status === 'success') {
          // 将 API 返回的数据赋值给 ruleList
          this.ruleList = resp.data;

          // 同时设置 formData，进行字段映射
          this.formData = {
            id: resp.data.rule_id,
            name: resp.data.rule_name,
            description: resp.data.rule_desc,
            status: resp.data.enable_status ? 'active' : 'inactive'
          }
        }
      } catch (error) {
        this.$Message.error('获取规则信息失败')
      }
    },
    async queryMemberList() {
      try {
        // 准备 ruleHitRecord API 所需的四个参数
        const params = {
          card_number: this.searchQuery || '', // 使用搜索框的卡号，如果为空则传空字符串
          rule_id: this.id, // 使用当前规则的 ID
          page: this.currentPage, // 当前页码
          size: this.pageSize // 每页大小
        }

        const resp = await this.$api.ruleHitRecord(params)

        if (resp.status === 'success') {
          this.totalMembers = resp.data.total
          this.memberList = resp.data.result // API 返��的数组在 result 字段中
        }
      } catch (error) {
        this.$Message.error('获取成员列表失败')
        console.error('获取命中记录失败:', error)
      }
    },
    async queryRulesForCreate() {
      try {
        const resp = await this.$api.queryRules()
        // const resp = {
        //   status: 'success',
        //   data: {
        //     "rule_id": 3,
        //     "rule_name": "",
        //     "trigger_type": "",
        //     "expression": "",
        //     "rule_desc": "",
        //     "enable_status": false,
        //     "update_time": "",
        //     "create_by": "",
        //     "param_list": [
        //       {
        //         "param_name": "storeList",
        //         "param_type": "list",
        //         "default_value": "[\"MP&官网\", \"Tmall\", \"JD\", \"抖音\", \"PDD\", \"线下门店\",\"JDDJ\"]",
        //         "value": "[\"MP&官网\", \"Tmall\", \"JD\", \"抖音\", \"PDD\",\"JDDJ\"]",
        //         "description": "会员常去门店",
        //         "enable_config": true
        //       },
        //       {
        //         "param_name": "store",
        //         "param_type": "string",
        //         "default_value": null,
        //         "description": "常去门店",
        //         "enable_config": false
        //       },
        //       {
        //         "param_name": "markMax",
        //         "param_type": "string",
        //         "default_value": "100",
        //         "description": "积分数值",
        //         "enable_config": true
        //       },
        //       {
        //         "param_name": "mark",
        //         "param_type": "string",
        //         "default_value": null,
        //         "description": "积分多少",
        //         "enable_config": false
        //       }
        //     ]
        //   }
        // }

        if (resp.status === 'success') {
          // 设置 ruleList，只使用 param_list，其他字段为空
          this.ruleList = {
            rule_id: 3,
            rule_name: "",
            trigger_type: "",
            expression: "",
            rule_desc: "",
            enable_status: false,
            update_time: "",
            create_by: "",
            param_list: resp.data.param_list
          }
        }
      } catch (error) {
        console.error('获取基础规则数据失败:', error)
        this.$Message.error('获取基础规则数据失败')
      }
    },
    async handleSave() {
      try {
        // 先进行表单验证
        const valid = await new Promise((resolve) => {
          this.$refs.formRef.validate((valid) => {
            resolve(valid)
          })
        })

        if (!valid) {
          this.$Message.warning('请填写完整信息')
          return
        }

        this.saveLoading = true

        // 准备提��的数据结构
        const submitData = {
          rule_name: this.formData.name,
          trigger_type: this.ruleList.trigger_type || "个人信息变更", // 如果没有则使用默认值
          expression: this.ruleList.expression || "",
          description: this.formData.description,
          enable_status: this.formData.status === 'active',
          create_by: "admin", // 可以从用户信息中获取
          param_list: this.ruleList.param_list || []
        }

        // 根据是否有 rule_id 判断是新建还是编辑
        if (this.id && this.ruleList.rule_id) {
          // 编辑模式：调用 updateRules API
          submitData.rule_id = this.ruleList.rule_id

          console.log('更新规则数据:', submitData)
          const resp = await this.$api.updateRules(submitData)

          if (resp.status === 'success') {
            this.$message.success('更新成功')
            this.handleCancel()
          } else {
            throw new Error(resp.message || '更新失败')
          }
        } else {
          // 新建模式：调用 creatRules API
          console.log('创建规则数据:', submitData)
          const resp = await this.$api.creatRules(submitData)

          if (resp.status === 'success') {
            this.$message.success('创建成功')
            this.handleCancel()
          } else {
            throw new Error(resp.message || '创建失败')
          }
        }
      } catch (error) {
        this.$message.error(error.message || '保存失败')
      } finally {
        this.saveLoading = false
      }
    },
    handleSearch() {
      if (!this.searchQuery) {
        this.$message.warning('请输入搜索内容')
        return
      }
      this.currentPage = 1
      this.queryMemberList()
    },
    handleSearchReset() {
      this.searchQuery = ''
      this.currentPage = 1
      this.queryMemberList()
    },
    handlePageChange(page) {
      this.currentPage = page
      this.queryMemberList()
    },
    handlePageSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.queryMemberList()
    },
    // 数据转换方法：将system_param转换为LeftRuleItem期望的格式
    transformParamToRuleItem(param) {
      const ruleItem = {
        key: param.param_name,
        discribe: param.description,
        isReadOnly: false,
        paramType: param.param_type,
        originalParam: param // 保存原始参数引用，方便后续更新
      }

      if (param.param_type === 'list') {
        try {
          let allOptions = []
          let selectedOptions = []

          // 解析default_value作为所有选项
          if (param.default_value) {
            allOptions = JSON.parse(param.default_value)
          }

          // 解析value作为选中选项
          if (param.value) {
            // 如果value是字符串且看起来像JSON数组，则解析它
            if (typeof param.value === 'string' && param.value.startsWith('[')) {
              selectedOptions = JSON.parse(param.value)
            } else if (typeof param.value === 'string') {
              // 如果value是单个字符串，将其作为单选项
              selectedOptions = [param.value]
              // 如果没有default_value，将value也加入到所有选项中
              if (!param.default_value) {
                allOptions = [param.value]
              }
            } else if (Array.isArray(param.value)) {
              selectedOptions = param.value
            }
          }

          // 构建选项数组
          ruleItem.value = allOptions.map(option => ({
            id: option,
            name: option,
            selected: selectedOptions.includes(option)
          }))
        } catch (error) {
          console.error('解析JSON失败:', error, param)
          ruleItem.value = []
        }
      } else {
        // string类型，使用default_value作为初始值
        ruleItem.value = param.default_value || param.value || ''
      }

      return ruleItem
    },

    // 处理左侧规则面板的变化
    async handleLeftRuleChange(payload) {
      const { index, newValue, isList } = payload

      // 获取对应的可编辑参数
      const editableParams = this.ruleList.param_list.filter(p => p.enable_config === true)
      if (index >= editableParams.length) {
        console.error('参数索引超出范围')
        return
      }

      const paramToUpdate = editableParams[index]
      let updatedValue

      if (isList) {
        // 列表类型：newValue是选中的id数组
        updatedValue = JSON.stringify(newValue)
      } else {
        // 字符串类型：直接使用newValue
        updatedValue = newValue
      }

      // 调用API更新参数
      await this.updateSystemParam(paramToUpdate.param_name, updatedValue, paramToUpdate.param_type)
    },
    // API调用方法：更新系统参数
    async updateSystemParam(paramName, newValue, paramType) {
      if (this.updateLoading) {
        return // 防止重复调用
      }

      try {
        this.updateLoading = true

        // 更新本地数据
        const paramIndex = this.ruleList.param_list.findIndex(p => p.param_name === paramName)
        if (paramIndex !== -1) {
          // 创建新的ruleList对象以触发响应式更新
          const updatedRuleList = JSON.parse(JSON.stringify(this.ruleList))
          updatedRuleList.param_list[paramIndex].value = newValue

          // TODO: 替换为实际的API调用
          // const response = await this.$api.updateRuleSystemParam({
          //   ...updatedRuleList
          // })

          if (response.status === 'success') {
            // API调用成功，更新本地数据
            this.ruleList = updatedRuleList
            this.$Message.success('参数更新成功')
          } else {
            throw new Error(response.message || '更新失败')
          }
        }
      } catch (error) {
        console.error('更新系统参数失败:', error)
        this.$Message.error('参数更新失败: ' + (error.message || '未知错误'))

        // API调用失败，回滚本地状态（重新获取数据）
        await this.queryBasicInfo()
      } finally {
        this.updateLoading = false
      }
    },

    async confirmRemove() {
      try {
        // TODO: 替换为实际API
        const resp = { status: 'success' }
        if (resp.status === 'success') {
          this.$Message.success('移除成功')
          this.removeModal = false
          this.queryMemberList()
        }
      } catch (error) {
        this.$Message.error('移除���败')
      }
    },
    handleCancel() {
      this.show = false
      this.title = ''
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    },
    // 规则表达式变动时，直接更新 ruleList.expression
    async handleRuleChange(expression) {
      this.ruleList.expression = expression
      console.log('rules----', this.ruleList.expression)
      // 自动调用API
      try {
        // TODO: 填写你的API地址
        const url = ''
        // 这里用fetch举例，实际可用this.$api等
        const resp = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(this.ruleList)
        })
        if (resp.ok) {
          const data = await resp.json()
          // 假设response body和request body结构一致
          this.ruleList = data
        }
      } catch (e) {
        // 可选：错误处理
      }
    }


  }
}
</script>

<template>
  <div class="container">
    <i-modal
      :closable="false"
      :mask-closable="false"
      :title="modalTitle"
      footer-hide
      v-model="show"
      width="1200"
    >
      <div class="modal-content mt30 flex column">
        <!-- 基础信息部分 -->
        <div class="section-container">
          <div class="section-title">基础信息</div>
          <div class="info-form">
            <i-form ref="formRef" :model="formData" :rules="formRules" :label-width="100">
              <i-row>
                <i-col span="20">
                  <i-form-item label="ID">
                    <span>{{ formData.id || 'B00001' }}</span>
                  </i-form-item>
                  <i-form-item label="名称" prop="name">
                    <i-input v-model="formData.name" placeholder="请输入名称" :maxlength="50" :disabled="isView" />
                  </i-form-item>
                  <i-form-item label="说明" prop="description">
                    <i-input
                      v-model="formData.description"
                      type="textarea"
                      :maxlength="200"
                      :rows="4"
                      show-word-limit
                      placeholder="请输入说明（最多200字）"
                      :disabled="isView"
                    />
                  </i-form-item>
                  <i-form-item label="状态" prop="status">
                    <i-select v-model="formData.status" style="width: 200px" :disabled="isView">
                      <i-option value="active">启用</i-option>
                      <i-option value="inactive">停用</i-option>
                    </i-select>
                  </i-form-item>
                </i-col>
              </i-row>
            </i-form>
          </div>
        </div>

        <!-- 规则配置部分 -->
        <div class="section-container">
          <div class="section-title">
            规则配置
            <i-spin v-if="updateLoading" size="small" style="margin-left: 10px;"></i-spin>
            <span v-if="updateLoading" style="margin-left: 5px; color: #2d8cf0; font-size: 12px;">参数更新中...</span>
          </div>
          <div class="rule-config-layout">
            <!-- 左侧面板 -->
            <div class="left-panel">
              <left-rule-panel
                :rule-list="editableSystemParams"
                :disabled="isView || updateLoading"
                @change="handleLeftRuleChange"
              />
            </div>
            <!-- 右侧面板 -->
            <div class="right-panel">
              <rule-configurator
                ref="ruleConfigurator"
                v-model="formData.rules"
                :disabled="isView"
                :expression="ruleList.expression"
                :left-options="nonEditableSystemParamsForRule"
                :right-options="editableSystemParamsForRule"
                @change="handleRuleChange"
              />
            </div>
          </div>
        </div>

        <!-- 成员列表��分 -->
        <div class="section-container">
          <div class="section-title">成员列表</div>
          <div class="filter-box">
            <div class="filter-box-left">
              <i-input
                v-model="searchQuery"
                placeholder="搜索成员（手机号/Person ID）"
                style="width: 250px"
              />
              <div class="action-buttons">
                <i-button type="primary" @click="handleSearch">搜索</i-button>
                <i-button type="primary" @click="handleSearchReset">重置</i-button>
              </div>
            </div>
          </div>
          <div class="table-container">
            <i-table :columns="memberColumns" :data="memberList" border />
            <div class="pagination-container">
              <i-page
                :total="totalMembers"
                :current="currentPage"
                :page-size="pageSize"
                :page-size-opts="[10, 20, 30, 50]"
                show-total
                show-sizer
                @on-change="handlePageChange"
                @on-page-size-change="handlePageSizeChange"
              />
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <i-button @click="handleCancel" class="mr30">取消</i-button>
          <i-button v-if="!isView" type="primary" :loading="saveLoading" @click="handleSave">保存</i-button>
        </div>
      </div>
    </i-modal>

    <!-- 移除成员确认弹窗 -->
    <i-modal v-model="removeModal" :closable="false" title="确认">
      <p>确定要移除该成员吗？</p>
      <div slot="footer">
        <i-button @click="removeModal = false">取消</i-button>
        <i-button type="primary" @click="confirmRemove">确定</i-button>
      </div>
    </i-modal>
  </div>
</template>

<style lang="less" scoped>
.modal-content {
  width: 100%;
  padding: 0 20px;
}

.section-container {
  margin-bottom: 20px;
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #17233d;
}

.info-form {
  border: 1px solid #e8eaec;
  padding: 20px;
  border-radius: 4px;
  background: #fff;
}

.rule-config-layout {
  display: flex;
  gap: 20px;
  border: 1px solid #e8eaec;
  padding: 20px;
  border-radius: 4px;
  background: #fff;
  min-height: 200px;
}

.left-panel {
  width: 35%;
  flex-shrink: 0;
}

.right-panel {
  flex-grow: 1;
  border-left: 1px solid #e8eaec;
  padding-left: 20px;
}

.config-placeholder {
  color: #999;
  text-align: center;
  padding: 60px 0;
  font-size: 14px;
}

.filter-box {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-direction: row;
  justify-content: space-between;

  .filter-box-left {
    display: flex;
    flex-direction: row;
  }

  .action-buttons {
    margin-left: 15px;

    button {
      margin-right: 10px;
    }
  }
}

.table-container {
  max-height: 300px;
  overflow-y: auto;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #e8eaec;
  margin-top: 20px;
}

.mr30 {
  margin-right: 30px;
}

.mt30 {
  margin-top: 30px;
}

.flex {
  display: flex;

  &.column {
    flex-direction: column;
  }
}
</style>
