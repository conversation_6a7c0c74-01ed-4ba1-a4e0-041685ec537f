import fetch from '@/utils/network/fetch'

// 获取指定月份未发布产品门店报告
const getUnpublishProductStoreReport = (query = {}) => {
  return fetch('/api/v1/reports/product_stores', { data: query })
}

// 获取活动/产品/抽奖报告
const getCountReport = (query = {}) => {
  return fetch('/api/v1/reports/counts', { data: query })
}

// 获取门店活动报告
const getSportsSessionReports = (query = {}) => {
  return fetch('/api/v1/reports/sessions', { data: query })
}

// 获取门店积分兑换报告
const getOrderReports = (query = {}) => {
  return fetch('/api/v1/reports/orders', { data: query })
}

export default {
  getUnpublishProductStoreReport,
  getCountReport,
  getSportsSessionReports,
  getOrderReports
}
