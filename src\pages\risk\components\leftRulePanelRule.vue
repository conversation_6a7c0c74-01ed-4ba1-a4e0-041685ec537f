<template>
  <div class="left-rule-panel">
    <div class="panel-header">
      <h3>规则属性配置</h3>
    </div>
    <div class="panel-content">
      <left-rule-item
        v-for="(rule, index) in ruleList"
        :key="rule.key"
        :rule="rule"
        :rule-index="index"
        :disabled="disabled"
        @update-rule="handleItemChange"
      />
    </div>
  </div>
</template>

<script>
import LeftRuleItemRule from './leftRuleItemRule.vue';

export default {
  name: 'LeftRulePanelRule',
  components: {
    LeftRuleItem: LeftRuleItemRule
  },
  props: {
    ruleList: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: <PERSON>olean,
      default: false,
      observer: function (newVal) {
        console.log('Disabled state changed:', newVal);
      }
    }
  },
  data() {
    return {};
  },
  methods: {
    handleItemChange(payload) {
      this.$emit('change', payload);
    }
  }
};
</script>

<style lang="less" scoped>
.left-rule-panel {
  border: 1px solid #dcdee2;
  border-radius: 4px;
  background-color: #fff;

  .panel-header {
    padding: 10px 16px;
    background: #f8f8f9;
    border-bottom: 1px solid #dcdee2;

    h3 {
      margin: 0;
      font-weight: normal;
      font-size: 14px;
    }
  }

  .panel-content {
    // 设置一个最大高度，并让其内容可滚动
    // 这个高度可以根据实际UI效果进行调整
    max-height: 450px;
    overflow-y: auto;
    padding: 0;
  }
}
</style>
