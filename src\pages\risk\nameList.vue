<template>
  <div class="risk-name">
    <i-row>
      <i-table :columns="nameColumns" :data="nameList" border class="risk-name-table-box" />
    </i-row>
    <risk-modal
      v-if="modalTitle"
      :title="modalTitle"
      :id="currentId"
      :type="modalType"
      :cancel="handleModalCancel"
    />
  </div>
</template>

<script>
import RiskModal from './profile/nameListModal.vue'

export default {
  name: 'RiskName',
  components: {
    RiskModal
  },
  data() {
    return {
      modalTitle: '',
      modalType: 'view',
      currentId: '',
      nameList: [
        {
          id: '1',
          name: '全局白名单',
          count: 0,
          updateTime: '',
          type: 'WHITE'
        },
        {
          id: '2',
          name: '全局黑名单',
          count: 0,
          updateTime: '',
          type: 'BLACK'
        }
      ],
      nameColumns: [
        {
          title: '名称',
          key: 'name',
          className: 'th-class th-class-name text'
        },
        {
          title: '人数',
          key: 'count',
          className: 'th-class th-class-name text'
        },
        {
          title: '最后更新时间',
          key: 'updateTime',
          className: 'th-class th-class-name text'
        },
        {
          title: '操作',
          key: 'action',
          className: 'th-class th-class-name',
          width: 190,
          render: (h, params) => {
            const operations = []
            operations.push(h('i-button', {
              class: 'group-operation-btn ml5 mr5 mt5 mb5',
              props: {
                type: 'primary',
                ghost: true
              },
              on: {
                click: () => {
                  this.handleEdit(params)
                }
              }
            }, '编辑'))

            operations.push(h('i-button', {
              class: 'group-operation-btn ml5 mr5 mt5 mb5',
              props: {
                type: 'primary',
                ghost: true
              },
              on: {
                click: () => {
                  this.handleView(params)
                }
              }
            }, '查看'))

            return h('div', operations)
          }
        }
      ]
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    // 获取表格数据
    async queryTableData() {
      try {
        const req = {
          biz_type: 'BBB'
        }
        const resp = await this.$api.getNameListTable(req)

        if (resp.status === 'success') {
          // 初始化默认值
          let whiteData = resp.data.find(d => d.status === false)
          let blackData = resp.data.find(d => d.status === true)

          // 更新白名单
          this.$set(this.nameList[0], 'count', whiteData ? whiteData.num : 0)
          this.$set(this.nameList[0], 'updateTime', whiteData ? '暂无' : '暂无')

          // 更新黑名单
          this.$set(this.nameList[1], 'count', blackData ? blackData.num : 0)
          this.$set(this.nameList[1], 'updateTime', blackData ? '暂无' : '暂无')
        }
      } catch (error) {
        this.$Message.error('获取数据失败')
      }
    },

    // 编辑按钮点击事件
    handleEdit(params) {
      this.currentId = params.row.id
      this.modalType = 'edit'
      this.modalTitle = `编辑${params.row.name}`
    },

    // 查看按钮点击事件
    handleView(params) {
      this.currentId = params.row.id
      this.modalType = 'view'
      this.modalTitle = `查看${params.row.name}`
    },

    // 弹窗取消事件
    handleModalCancel() {
      this.modalTitle = ''
      this.currentId = ''
      this.modalType = 'view'
      this.queryTableData()
    }
  }
}
</script>

<style lang="less" scoped>
.risk-name {
  &-table-box {
    min-height: 200px;
    background: #fff;
  }
}

/deep/ .th-class-name.text {
  font-size: 14px !important;
}

/deep/ .group-operation-btn {
  color: rgba(54, 67, 186, 1);
  background-color: rgba(0,0,0,0);
  border: none;
  font-size: 14px !important;

  &:active, &:focus, &:hover {
    background-color: rgba(0,0,0,0);
    border: none;
    box-shadow: none;
  }
}

/deep/ .query {
  margin-right: 68px;
}

/deep/ .ivu-btn-ghost {
  &.ivu-btn-primary {
    color: rgba(54, 67, 186, 1);
    background-color: transparent;
    border: none;
  }
}
</style>
