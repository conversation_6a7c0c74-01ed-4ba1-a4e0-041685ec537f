import fetch from '@/utils/network/fetch'

// 搜索用户
const searchCustomer = (query = {}) => {
  return fetch('/api/v1/memberships', { data: query })
}

// 获取用户信息详情
const getCustomerDetail = id => {
  return fetch(`/api/v1/memberships/${id}`)
}

// 更新用户信息
const updateCustomerInfo = (data = {}) => {
  return fetch(`/api/v1/memberships/${data.dktPersonId}`, {
    method: 'put',
    data: JSON.stringify(data)
  })
}

// 分页查询用户购买记录
const searchPurchases = (query = {}) => {
  return fetch('/api/v1/memberships/purchase_transactions', { data: query })
}

// 分页查询用户积分变更
const searchPoints = (query = {}) => {
  return fetch('/api/v1/memberships/point_transactions', { data: query })
}

// 更新用户积分
const updatePoints = (body = {}) => {
  return fetch('/api/v1/memberships/point_transactions', {
    method: 'put',
    data: JSON.stringify(body)
  })
}

// 更新用户运动偏好
const updateFavoriteCategories = (body = {}) => {
  return fetch('/api/v1/memberships/categories', {
    method: 'put',
    data: JSON.stringify(body)
  })
}

// 搜索优惠券
const searhCoupons = (query = {}) => {
  return fetch('/api/v1/memberships/coupons', { data: query })
}

// 获取用户积分兑换订单记录
const searchOrders = (query = {}) => {
  return fetch('/api/v1/memberships/orders', { data: query })
}

const removeBlackList = id => {
  return fetch(`/api/v1/memberships/${id}/blacklist`, { method: 'delete' })
}

// 发送验证码
const sendVerifyCode = (query = {}) => {
  console.log('>>>>huanjing', process.env)
  return fetch(`/customerscn/v1/sms_validation/new_sms_code_creation`, { data: query, requestBaseUrl: 'AZ_HOST' })
}
// 注销用户
const revokeMember = (personId, query = {}) => {
  return fetch(`/api/v2/memberships/delete/${personId}`, { method: 'put', data: JSON.stringify(query) })
}

// 修改手机号
const modifyPhoneNum = (query = {}) => {
  return fetch(`/api/v2/memberships/phone_number`, { method: 'put', data: JSON.stringify(query) })
}


// 注销原因列表
const deleteReasonList = (query = {}) => {
  return fetch(`/api/v2/memberships/delete_reason`, { method: 'get' })
}

const queryAccountSummary = (dktPersonId, query = {} ) => {
  return fetch(`/api/v2/memberships/account_summary/${dktPersonId}`, { method: 'get', data: query })
}




export default {
  searchCustomer,
  getCustomerDetail,
  updateCustomerInfo,
  searchPurchases,
  searchPoints,
  searhCoupons,
  searchOrders,
  updatePoints,
  removeBlackList,
  sendVerifyCode,
  revokeMember,
  modifyPhoneNum,
  updateFavoriteCategories,
  deleteReasonList,
  queryAccountSummary
}
