<template>
	<div class="container">
		<i-modal :closable="false" :title="title" footer-hide v-model="show" width="700">
			<div class="modal-content mt30 flex column">
				<i-form :label-width="90" label-position="right">
					<i-input v-model="id" v-show="false" />
					<i-form-item class="modal-item" label="名称">
						<i-input placeholder="请输入名称" v-model="rules.name.value" />
						<div class="warn-text" v-show="rules.name.error">{{rules.name.text}}</div>
					</i-form-item>
					<i-form-item class="modal-item" label="运动分类" v-if="this.id == ''">
						<i-select class="mr15" v-model="rules.categoryId.value">
							<i-option :key="item.id" :value="item.id" v-for="item in categoryList">{{item.name}}</i-option>
						</i-select>
						<div class="warn-text" v-show="rules.categoryId.error">{{rules.name.text}}</div>
					</i-form-item>
					<i-form-item class="modal-item" label="运动图片" v-if="this.id == ''">
						<i-upload :before-upload="beforeUpload" action name="file" type="drag">
							<div class="modal-item-upload-content" style="padding:30px">
								<i-icon class="blue-action" size="52" type="ios-cloud-upload" />
								<p>点击或拖动文件在这里上传</p>
								<p>{{this.rules.file.value ? this.rules.file.value.name : ''}}</p>
							</div>
							<span>图片建议: 尺寸: 700X400 (安全区域为700x260); 大小: 不大于500K; 格式: JPG;</span>
						</i-upload>
						<div class="warn-text" v-show="rules.file.error">{{rules.name.text}}</div>
					</i-form-item>
					<img :src="url" alt class="cards-img" />
				</i-form>
				<div class="modal-items mt30 flex row justify-center">
					<i-button @click="cancelClick" class="mr30">取消</i-button>
					<i-button @click="certainClick" type="primary">确认</i-button>
				</div>
			</div>
		</i-modal>
	</div>
</template>

<script>
import { checkFill, judgeFill } from '@/utils/util'

export default {
  name: 'gallery_modal',
  props: {
    title: '',
    id: '',
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      show: true,
      categoryList: [], // 运动分类
      rules: {
        name: {
          value: null,
          error: false,
          text: '请输入正确名称'
        },
        categoryId: {
          value: null,
          error: false,
          text: '请选择运动分类'
        },
        file: {
          value: null,
          error: false,
          text: '请上传图片'
        }
      },
      url: ''
    }
  },
  mounted() {
    this.getCategories()
    if (this.id) {
      this.getGalleryDetail(this.id)
    }
  },
  methods: {
    // 上传文件之前的钩子
    beforeUpload(file) {
      this.rules.file.value = file
      return false
    },
    // 确定
    async certainClick() {
      let res
      if (this.id) {
        res = await this.$api.updateGalleryName(this.id, this.rules.name.value)
        if (res.status === 'success') {
          this.certain && this.certain()
        }
      } else {
        checkFill(this.rules)
        let judge = judgeFill(this.rules)
        if (judge) {
          let objc = {
            loading: true,
            isForm: true,
            method: 'post',
            data: this.handleFormData()
          }
          res = await this.$api.createGallery(objc)
          if (res.status === 'success') {
            this.certain && this.certain()
          }
        }
      }
    },
    // 取消
    cancelClick() {
      this.show = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    },
    // 网络
    async getCategories() {
      let categoryRes = await this.$api.getSportsCategories()
      this.categoryList = categoryRes.data
    },
    // 获取图片详情
    async getGalleryDetail(id) {
      let resp = await this.$api.getGalleryById(id)
      if (resp['status'] == 'success') {
        let gallery = resp.data
        this.rules.name.value = gallery.name
        this.rules.categoryId.value = gallery.categoryId
        this.url = gallery.url
      }
    },
    // 处理表单数据
    handleFormData(file) {
      let params = new FormData()
      params.append('file', this.rules.file.value, this.rules.file.value.name)
      params.append('name', this.rules.name.value)
      params.append('categoryId', this.rules.categoryId.value)
      return params
    }
  }
}
</script>

<style lang="less" scoped>
.modal-content {
  width: 100%;
  .modal-items {
    width: 100%;
    padding: 20px;
    &-title {
      padding-bottom: 10px;
    }
    &-upload-content {
      padding: 20px 0;
    }
  }
  .cards-img {
    width: 18vw;
    height: 8vw;
  }
}
</style>
