<template>
  <div class="container">
    <i-modal
      :closable="false"
      :mask-closable="false"
      :title="title"
      footer-hide v-model="show" width="1030">
      <div class="modal-content mt30 flex column">
        <!-- 基础信息部分 -->
        <div class="section-container">
          <div class="section-title">基础信息</div>
          <div class="info-box">
            <div class="info-item">
              <span class="label">ID：</span>
              <span class="value">{{ basicInfo.id }}</span>
            </div>
            <div class="info-item">
              <span class="label">名称：</span>
              <span class="value">{{ basicInfo.name }}</span>
            </div>
          </div>
        </div>

        <!-- 成员列表部分 -->
        <div class="section-container">
          <div class="section-title">成员列表</div>
          <div class="filter-box">
            <div class="filter-box-left">
              <i-select v-model="searchType" style="width: 100px; margin-right: 10px;">
                <i-option label="手机" value="mobile"></i-option>
                <i-option label="卡号" value="card_number"></i-option>
                <i-option label="Person ID" value="person_id"></i-option>
              </i-select>
              <i-input
                v-model="searchQuery"
                placeholder="手机/卡号/person ID"
                style="width: 200px"
              ></i-input>
              <div class="action-buttons">
                <i-button type="primary" @click="handleSearch">搜索</i-button>
                <i-button type="primary" @click="handleSearchReset">重置</i-button>
              </div>
            </div>
            <div v-if="type==='edit'" class="action-buttons">
              <i-button type="primary" @click="handleAdd" v-has="['AUD_GLOBAL_LIST_WRITE']">添加</i-button>
              <i-button type="primary" @click="handleImport" v-has="['AUD_GLOBAL_LIST_IMPORT']" >导入</i-button>
            </div>
          </div>
          <div class="table-container">
            <i-table :columns="memberColumns" :data="memberList" border></i-table>
            <div class="pagination-container">
              <i-page
                :total="totalMembers"
                :current="currentPage"
                :page-size="pageSize"
                :page-size-opts="[10, 20, 30, 50]"
                show-total
                show-sizer
                @on-change="handlePageChange"
                @on-page-size-change="handlePageSizeChange"
              />
            </div>
          </div>
        </div>
<!--        <div class="section-container table-container">-->
<!--          <div class="section-title">操作日志</div>-->
<!--          <div class="log-container">-->
<!--            <div v-for="(log, index) in operationLogs" :key="index" class="log-item">-->
<!--              {{ log.content }}-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
        <div class="modal-footer">
          <i-button @click="handleCancel" class="mr30">关闭</i-button>
        </div>
      </div>
    </i-modal>

    <!-- 移除确认弹窗 -->
    <i-modal v-model="removeModal" :closable="false" title="确认">
      <p>是否移出名单?</p>
      <div slot="footer">
        <i-button @click="removeModal = false">取消</i-button>
        <i-button type="primary" @click="confirmRemove">确定</i-button>
      </div>
    </i-modal>

    <!-- Excel上传弹窗 -->
    <upload-batch-modal
      :show.sync="uploadModal"
      title="批量导入"
      :bizTip="biz_type"
      :status="!!title.includes('黑名单')"
      @cancel="handleUploadCancel"
      @success="handleUploadSuccess"
    />

    <!-- 添加成员弹窗 -->
    <i-modal v-model="addModal" :closable="false" title="添加成员">
      <div class="add-form">
        <i-select v-model="addForm.type" style="width: 100px; margin-bottom: 10px;">
          <i-option label="手机" value="mobile"></i-option>
          <i-option label="卡号" value="card_number"></i-option>
          <i-option label="Person ID" value="person_id"></i-option>
        </i-select>

        <i-input v-model="addForm.identifier" placeholder="请输入您选择的（手机/卡号/person ID）"></i-input>
        <div style="position: relative;">
          <i-input
            v-model="addForm.reason"
            :maxlength="200"
            placeholder="原因（必填）"
            class="mt10"
          />
          <span style="position: absolute; bottom: -5px; right: 10px; color: #999; font-size: 12px;">
            {{ addForm.reason.length }} / 200
          </span>
        </div>
      </div>
      <div slot="footer">
        <i-button @click="addModal = false">取消</i-button>
        <i-button type="primary" @click="confirmAdd">确定</i-button>
      </div>
    </i-modal>
  </div>
</template>

<script>
import UploadBatchModal from './uploadBatchModal.vue'

export default {
  name: 'RiskModal',
  components: {
    UploadBatchModal
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'view' // 'view' 或 'edit'
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      show: true,
      searchQuery: '',
      searchType: 'mobile',
      basicInfo: { id: '', name: '', },
      memberColumns: [
        {
          title: 'Person ID',
          key: 'person_id',
          width: 100
        },
        {
          title: '卡号',
          key: 'card_number',
          width: 130
        },
        {
          title: '手机',
          key: 'mobile',
          width: 120
        },
        {
          title: '加入时间',
          key: 'created_time',
          width: 160
        },
        {
          title: '操作者',
          key: 'operator',
          width: 80
        },
        {
          title: '原因',
          key: 'reason',
          width: 210
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          render: (h, params) => {
            const operations = []

            operations.push(h('i-button', {
              props: {
                type: 'primary',
                ghost: true
              },
              style: {
                marginRight: '10px'
              },
              on: {
                click: () => this.handleViewMember(params.row)
              }
            }, '查看'))
            if (this.type === 'edit') {
              operations.push(h('i-button', {
                props: {
                  type: 'error',
                  ghost: true
                },
                on: {
                  click: () => this.handleRemoveMember(params.row)
                },
                directives: [{
                  name: 'has',
                  value: ['AUD_GLOBAL_LIST_WRITE'] // 这里填你要的权限码
                }]
              }, '移出'))
            }

            return h('div', {
              style: {
                display: 'flex',
                alignItems: 'center'
              }
            }, operations)
          }
        }
      ],
      memberList: [],
      operationLogs: [],
      removeModal: false,
      addModal: false,
      currentRow: null,
      addForm: {
        identifier: '',
        reason: '',
        type: 'phone'
      },
      currentPage: 1,
      pageSize: 10,
      totalMembers: 0,
      uploadModal: false,
      uploadFile: null,
      biz_type: 'BBB',
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    async initData(size = 10, page = 1) {
      try {
        const data = {
          is_blacklist: !!this.title.includes('黑名单'),
          biz_type: this.biz_type, size, page
        }
        const memberListResp = await this.$api.getRiskMemberList(data)

        this.basicInfo = {
          id: 'W0001/B0001',
          name: this.title
        }
        this.totalMembers = memberListResp.data.total // 总数据条数
        this.memberList = memberListResp.data.result
        this.operationLogs = [ //暂时不用
          { content: '2024-03-15 12:00:00 张三 添加了成员 186****2323' },
        ]
      } catch (error) {
        this.$message.error('获取数据失败')
      }
    },

    handleAdd() {
      this.addModal = true
      this.addForm = {
        identifier: '',
        reason: '',
        type: 'phone'
      }
    },

    handleImport() {
      this.uploadModal = true;
    },

    handleViewMember(row) {
      this.$router.push({
        path: '/customerSearch',
        query: { phone: row.mobile }
      });
    },

    handleRemoveMember(row) {
      this.currentRow = row
      this.removeModal = true
    },

    async confirmRemove() {
      try {
        await this.$api.deleteRowData(
          this.biz_type,
          !!this.title.includes('黑名单'),
          this.currentRow.person_id
        )
        this.$message.success('移出成功')
        this.removeModal = false
        this.initData(this.pageSize, 1) // 刷新列表
      } catch (error) {
        this.$message.error('移出失败')
      }
    },

    async confirmAdd() {
      if (!this.addForm.identifier || !this.addForm.reason) {
        this.$message.warning('请填写完整信息')
        return
      }
      const postData = {
        [this.addForm.type]: this.addForm.identifier,
        reason: this.addForm.reason,
      }
      try {
        const allData = await this.$api.postRowData(postData)
        const postListData = [{
          "person_id": allData.data.person_id,
          "card_number": allData.data.card_number,
          "mobile": allData.data.mobile,
          "reason": this.addForm.reason
        }]
        const resPostData = await this.$api.postTableData(postListData, this.biz_type, !!this.title.includes('黑名单'))
        this.$message.success('添加成功')
        this.addModal = false
        this.initData(this.pageSize, 1) // 刷新列表
      } catch (error) {
        this.$message.error('会员不存在')
      }
    },

    handleCancel() {
      this.show = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    },

    async handleSearch() {
      if (!this.searchQuery) {
        this.$message.warning('请输入搜索内容')
        return
      }
      try {
        const data = {
          is_blacklist: !!this.title.includes('黑名单'),
          biz_type: this.biz_type,
          size: this.pageSize,
          page: this.currentPage,
          [this.searchType]: this.searchQuery,
        }
        const memberListResp = await this.$api.getRiskMemberList(data)
        this.memberList = memberListResp.data.result
        this.totalMembers = memberListResp.data.total
        this.currentPage = 1
        this.$message.success('搜索成功')
      } catch (error) {
        this.$message.error('搜索失败，请重试')
      }
    },

    handleSearchReset() {
      this.searchQuery = '';
      // this.showSearchInput = false;
      // this.$nextTick(() => {
      //   this.showSearchInput = true;
      // });
      this.initData();
    },

    handlePageChange(page) {
      this.currentPage = page;
      this.initData(this.pageSize, page); // 重新获取数据
    },

    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.initData(size, 1); // 重新获取数据
    },

    handleUploadCancel() {
      this.uploadModal = false;
    },

    handleUploadSuccess(data) {
      this.uploadModal = false;
      this.initData(this.pageSize, 1); // 刷新列表
    },

  }
}
</script>

<style lang="less" scoped>
.modal-content {
  width: 100%;
  padding: 0 20px;
}

.section-container {
  margin-bottom: 20px;
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #17233d;
}

.info-box {
  border: 1px solid #e8eaec;
  padding: 15px;
  border-radius: 4px;
}

.info-item {
  margin-bottom: 10px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    color: #515a6e;
    margin-right: 8px;
  }

  .value {
    color: #17233d;
  }
}

.filter-box {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-direction: row;
  justify-content: space-between;
  .filter-box-left {
    display: flex;
    flex-direction: row;
  }
  .action-buttons {
    margin-left: 15px;

    button {
      margin-right: 10px;
    }
  }
}

.table-container {
  max-height: 300px;
  overflow-y: auto;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  padding: 8px;
  border-bottom: 1px solid #e8eaec;
  color: #515a6e;

  &:last-child {
    border-bottom: none;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #e8eaec;
  margin-top: 20px;
}

.operation-buttons {
  display: flex;
  align-items: center;
}

.group-operation-btn {
  color: rgba(54, 67, 186, 1);
  background-color: rgba(0,0,0,0);
  border: none;
  font-size: 14px !important;

  &:active, &:focus, &:hover {
    background-color: rgba(0,0,0,0);
    border: none;
    box-shadow: none;
  }
}

.add-form {
  padding: 10px 0;

  .mt10 {
    margin-top: 10px;
  }
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.drag-upload-wrap {
  width: 400px;
  margin: 0 auto;
  text-align: center;
}

.upload-wrap {
  margin-top: 20px;
  text-align: right;
  padding-right: 20px;

  .upload-btn {
    width: 75px;
  }
}
</style>
