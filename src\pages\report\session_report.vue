<template>
  <div class="session_report">
    <div class="mt20 mb30 row item-center justify-between">
      <div>
        <i-date-picker :value="times" :options="options" format="yyyy-MM-dd" type="daterange" placement="bottom-end" 
          placeholder="请选择时间区间" style="width: 200px" @on-change="changeTimeRange"></i-date-picker>
        <i-button @click="handleRefresh">确定</i-button>
      </div>
      <div>
        <i-button icon="md-download" @click="handleSessionBookExport">导出报名</i-button>
        <i-button icon="md-download" @click="handleSessionRedeemExport">导出签到</i-button>
      </div>
    </div>
    <div>
      <i-table v-show="false" ref="sessions"></i-table>
      <v-chart :options="sessionLines" class="line_charts" />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import ECharts from 'vue-echarts/components/ECharts'
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/bar'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/title'
import 'echarts/lib/component/tooltip'

export default {
  name: "session_report",
  components: {
    'v-chart': ECharts
  },
  data() {
    return {
      times: [],
      options: {
        disabledDate (date) {
          return date && date.valueOf() > Date.now() - 86400000;
        }
      },
			sessionLines: {
        title: { text: '门店活动统计' },
				tooltip: { trigger: 'axis' },
				legend: { data:['报名数量', '签到数量'] },
        grid: { left: '3%', bottom: '3%', containLabel: true },
				xAxis: { type: 'category', boundaryGap: false, data: [] },
				yAxis: { type: 'value', minInterval: 1 },
				series: [
          { name: '报名数量', type: 'line', data: [], symbol: 'none', smooth: true },
          { name: '签到数量', type: 'line', data: [], symbol: 'none', smooth: true }
        ]
			}
    }
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  watch: {
    // 监控门店切换, 刷新数据
    currentStore(newVal, oldVal) {
      this.currentStore.dktStoreNumber && this.handleRefresh()
    }
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 初始数据
    loadData() {
      let yesterday = new Date()
      yesterday.setDate(yesterday.getDate()-1)
      let end = yesterday.format('yyyy-MM-dd')
      yesterday.setDate(yesterday.getDate()-30)
      let start = yesterday.format('yyyy-MM-dd')
      this.times = [start, end]
      this.handleRefresh()
    },
    changeTimeRange(values) {
      this.times = values
    },
    // 刷新活动报告
    async handleRefresh() {
      if (this.currentStore.dktStoreNumber 
        && this.times && this.times[0] && this.times[1]) {
        let days = (new Date(this.times[1]).getTime() - new Date(this.times[0]).getTime()) / (1000 * 60 * 60 * 24)
        if (days > 30) {
          this.$message.warning({ content: '统计请勿超过30天', closable: true, duration: 3 })
          return
        }
        let resp = await this.$api.getCountReport({
          'category': 'SESSION', 
          'dktStoreNumber': this.currentStore.dktStoreNumber, 
          'startTime': this.times[0],
          'endTime': this.times[1]
        })
        if (resp.status === 'success') {
          let xAxis = [], yAxis0 = [], yAxis1 = []
          resp.data.forEach((item) => {
            if (item.status == 'SESSION_BOOK') {
              xAxis.push(item.reportDate)
              yAxis0.push(item.count)
            } else if (item.status == 'SESSION_REDEEM') {
              yAxis1.push(item.count)
            }
          })
          this.sessionLines.xAxis.data = xAxis
          this.sessionLines.series[0].data = yAxis0
          this.sessionLines.series[1].data = yAxis1
        }
      }
    },

    // 导出活动报名报表
    handleSessionBookExport() {
      this.exportSessionReport('SESSION_BOOK')
    },
    // 导出活动签到报表
    handleSessionRedeemExport() {
      this.exportSessionReport('SESSION_REDEEM')
    },
    async exportSessionReport(status) {
      if (this.currentStore.dktStoreNumber 
        && this.times && this.times[0] && this.times[1]) {
        let exportColumns = [
          { title: '活动名称', key: 'sports_name' }, 
          { title: '活动场次', key: 'session_times' }, 
          { title: '门店名称', key: 'store_name' }, 
          { title: '门店号', key: 'store_number' }, 
          { title: '门店城市', key: 'store_city' }, 
          { title: '门店地址', key: 'store_address' }, 
          { title: '会员卡号', key: 'card_no' }, 
          { title: '报名时间', key: 'book_time' },
          { title: '签到时间', key: 'redeem_time' },
        ]
        let exportData = []
        let resp = await this.$api.getSportsSessionReports({
          'status': status,
          'dktStoreNumber': this.currentStore.dktStoreNumber,
          'startTime': this.times[0],
          'endTime': this.times[1]
        })
        if (resp.status === 'success') {
          resp.data.forEach((item) => {
            exportData.push({
              'sports_name': item.sports.name,
              'session_times': new Date(item.sports.sessions[0].startTime).format('yyyy-MM-dd hh:mm') + '至' + new Date(item.sports.sessions[0].endTime).format('yyyy-MM-dd hh:mm'),
              'store_name': item.sports.store.name,
              'store_number': '\t' + item.sports.store.dktStoreNumber,
              'store_city': item.sports.store.city,
              'store_address': item.sports.store.address,
              'card_no': '\t' + item.dktCardNo,
              'book_time': new Date(item.bookTime).format('yyyy-MM-dd hh:mm'),
              'redeem_time': new Date(item.redeemTime).format('yyyy-MM-dd hh:mm'),
            })
          })
          let name = (status == 'SESSION_BOOK') ? '报名' : '签到'
          this.$refs.sessions.exportCsv({
            quoted: true,
            filename: `${this.currentStore.name}_${this.times[0]}至${this.times[1]}活动${name}报告`,
            columns: exportColumns.filter((col, index) => status == 'SESSION_BOOK' ? index != 8 : index != 7),
            data: exportData
          });
        }
      }
    }
  }
};
</script>
<style lang="less" scoped>
.session_report {
	.line_charts {
		width: 80%;
		height: 500px;
	}
}
</style>