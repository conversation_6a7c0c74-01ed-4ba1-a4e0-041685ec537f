<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
import { getObjectByUrlParams } from "./utils/util"

export default {
  name: 'App',
  created() {
    if (location.search) {
      if (location.search.split('?')[1]) {
        const query = getObjectByUrlParams(location.search.split('?')[1])
        if (query.code) {
          location.href = `${location.origin}${location.pathname}#/${location.search}`
        }
      }
    }
  }
}
</script>

<style lang="less">
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  color: #000;
}
</style>
