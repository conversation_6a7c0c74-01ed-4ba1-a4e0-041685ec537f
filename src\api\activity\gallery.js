/**
 * 集团图库
 */
import fetch from '@/utils/network/fetch'

const getGalleries = (query = {}) => {
  return fetch('/api/v1/sports_galleries', { data: query })
}

const getGalleryById = id => {
  return fetch(`/api/v1/sports_galleries/${id}`)
}

const createGallery = (data = {}) => {
  return fetch('/api/v1/sports_galleries', data)
}

const updateGalleryName = (id, name) => {
  return fetch(`/api/v1/sports_galleries/${id}?name=${name}`, { method: 'put' })
}

const deleteGallery = id => {
  return fetch(`/api/v1/sports_galleries/${id}`, { method: 'delete' })
}

export default {
  getGalleries,
  getGalleryById,
  createGallery,
  updateGalleryName,
  deleteGallery
}
