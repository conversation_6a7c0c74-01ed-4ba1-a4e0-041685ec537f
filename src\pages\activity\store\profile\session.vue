<template>
  <div class="container">
    <i-modal footer-hide width="1000px" :closable="false" :title="title" v-model="showDialog">
      <i-table border :columns="activityColumns" :data="activityList" />

      <div class="flex row item-center justify-between mt30">
        <div>
          <i-select class="dropdown mr15" v-model="sessionId" placeholder="场次" clearable @on-change="changeSession">
            <i-option :value="s.id" v-for="s in sessions" :key="s.id">
              {{s.value}}
            </i-option>
          </i-select>
        </div>
        <div>
          <template v-if="selectedSession.id">
            <i-button type="warning" shape="circle" @click="cancelSession" v-if="selectedSession.active && !selectedSession.closed && !selectedSession.groupCancelled">取消活动场次</i-button>
            <i-button type="warning" shape="circle" v-else-if="selectedSession.groupCancelled" :disabled="selectedSession.groupCancelled">活动已取消</i-button>
            <i-button type="warning" shape="circle" v-else-if="selectedSession.closed" :disabled="selectedSession.closed">活动已结束</i-button>
          </template>
        </div>
      </div>

      <i-row class="mt30">
        <i-col span="6">活动名额: {{totalStock}}</i-col>
        <i-col span="6">已报名人数: {{bookedStock}}</i-col>
        <i-col span="6">已签到人数: {{redeemCount}}</i-col>
        <i-col span="6">等待队列: {{waitCount}}</i-col>
      </i-row>

      <i-table border :columns="sessionMemberColumns" :data="sessionMemberList" class="mt30" />

      <div class="mt30 flex row justify-center">
        <i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
        <i-button type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
      </div>
    </i-modal>

    <c-modal v-if="showCancelDialog" :title="cancelTitle" :certain="modalCertain" :cancel="modalCancel" :cancel-form="cancelForm" />
  </div>
</template>

<script>
import cfg from '../../config'
import { deepClone } from '@/utils/util'
import Modal from './cancel'

export default {
  name: 'session_stats_modal',
  components: {
    'c-modal': Modal
  },
  props: {
    title: '',
    opFrom: '',
    activityId: '',
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      showDialog: true,
      
      typeList: cfg.typeList,         // 活动类型
      targetList: cfg.targetList,     // 面向人群
      levelList: cfg.levelList,       // 运动等级
      paymentList: cfg.paymentList,   // 报名方式
      statusList: cfg.statusList,     // 状态

			activityList: [],         // 门店活动列表
			activityColumns: [
        { title: '主图', className: 'th-class text-center', width: 160, render: (h, params) => { return this.showRowImage(h, params.row.gallery.url) }},
        { title: '标题', key: 'name', className: 'th-class' },
        { title: '分类', key: 'categoryName', className: 'th-class', render: (h, params) => { return h('span', params.row.category.name) }},
        { title: '类型', key: 'type', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.type, this.typeList)) }},
        { title: '面向人群',  key: 'target', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.target, this.targetList)) }},
        { title: '运动等级',  key: 'level', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.level, this.levelList)) }},
        { title: '报名方式',  key: 'paymentType', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.paymentType, this.paymentList)) }},
        { title: '状态',  key: 'status', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.status, this.statusList)) }}
      ],
      
      totalStock: 0,
      bookedStock: 0,
      redeemCount: 0,
      waitCount: 0,
      sessions: [],

      sessionId: '',
      selectedSession: {
        id: '',
        startTime: '',
        endTime: '',
        remainingStock: 0,
        totalStock: 0,
        active: '',
        closed: '',
      },

      sessionMemberList: [],
      sessionMemberColumns: [
        { title: '客户名称', key: 'dktName', className: 'th-class', width: 160 },
        { title: '会员卡号', key: 'dktCardNo', className: 'th-class' },
        { title: '报名日期', key: 'bookTime', className: 'th-class', render: (h, params) => { return h('span', new Date(params.row.bookTime).format('yyyy-MM-dd hh:mm:ss')) }},
        { title: '状态', key: 'status', className: 'th-class' }
      ],

      showCancelDialog: false,
      cancelTitle: '',
      cancelForm: {
        id: '',
        name: '',
        value: ''
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 初始化数据
    async loadData() {
      this.getActivityDetail()
    },

    async getActivityDetail() {
      let resp = await this.$api.getActivityById(this.activityId, true)
      let activity = resp.data
      
      this.sessions = []
      activity['sessions'].forEach((value) => {
        let session = {}
        session['id'] = value.id
        session['value'] = value.day + " " + value.times[0] + "-" + value.times[1]
        this.sessions.push(session)
      })

      this.totalStock = activity.totalStock
      this.activityList = []
      this.activityList.push(activity)
    },

    // 渲染表格图片
    showRowImage(h, url, weight, height) {
      return h('div', { class: 'span-pic-box' }, [ h('img', { attrs: { src: url, class: 'table-img mt5', width: '105px', height: '60px' } }) ])
    },

    // 选择Session
    async changeSession(id) {
      for (let i=0; i< this.activityList[0].sessions.length; i++) {
        let s = this.activityList[0].sessions[i]
        if (s.id == this.sessionId) {
          this.selectedSession = s
          break
        }
      }
      if (this.sessionId > 0) {
        let resp = await this.$api.getSessionMembershipList(this.sessionId)
        if (resp.status == 'success') {
          this.sessionMemberList = resp.data.list
          this.redeemCount = resp.data.redeemCount
          this.waitCount = resp.data.waitCount
          this.bookedStock = resp.data.list.length
        }
      }
    },

    // 取消活动
    cancelSession() {
      if (this.selectedSession.id) {
        this.cancelTitle = '活动场次取消'
        let session
        for(let i=0; i<this.sessions.length; i++) {
          let s = this.sessions[i]
          if (s.id == this.selectedSession.id) {
            session = s
            break
          }
        }
        this.cancelForm = session
        this.cancelForm['name'] = this.activityList[0].name
        this.showCancelDialog = true
      } else {
        this.$modal.warning({
          'title': '提示',
          'content': '请选择要取消的场次'
        })
      }
    },

    certainClick() {
      this.certain && this.certain()
    },
    cancelClick() {
      this.showDialog = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    },
    
    modalCertain() {
      this.getActivityDetail()
      this.selectedSession = ''
      this.sessionId = ''
      this.showCancelDialog = false
    },
    modalCancel() {
      this.showCancelDialog = false
    },
  }
}
</script>
<style lang="less" scoped>
.dropdown {
  width: 200px;
}

.modal-content {
  width: 100%;
  .modal-item {
    width: 500px;
  }
}

.span-pic-box {
  display: block;
  height: 80px;
  .table-img {
    width: 80px;
    height: 60px;
    margin-top: 10px;
  }
}
.mt5 {
  margin-top: 5px;
}
</style>