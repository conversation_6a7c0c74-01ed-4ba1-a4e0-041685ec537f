/*
 * 网络接口配置
 */

// 接口地址
const BASE_URL = process.env.API_ROOT

// custom host
const AZ_HOST = process.env.AZ_HOST
const X_API_KEY = process.env.X_API_KEY

// api portal
const API_PORTAL = process.env.API_PORTAL

// HTTP CODE CONF
const ERROR_CONF = {
  SYSTEM_ERR: '500',
  SYSTEM_ERR_MSG: '系统错误',

  PERMISSION_ERR: '401',
  PERMISSION_ERR_MSG: '您暂无登录无权限'
}

// REQUEST CODE CONF
const REQ_ERROR_CONF = {
  PARAMS_ERR: '-1',
  PARAMS_ERR_MSG: '参数错误'
}

// 登录页面
const LOGIN_PAGE_PATH = 'https://idpdecathlon-pp.decathlon.com.cn/as/authorization.oauth2?client_id=MEMBERSHIP_CN_BACK&response_type=code&scope=openid%20profile&redirect_uri=https://membership.pp.dktapp.cloud/backoffice/'
// const LOGIN_PAGE_PATH = 'https://idpdecathlon.decathlon.com.cn/as/authorization.oauth2?client_id=MEMBERSHIP_CN_BACK&response_type=code&scope=openid%20profile&redirect_uri=https://membership.dktapp.cloud/backoffice/'

export default {
  BASE_URL,
  AZ_HOST,
  X_API_KEY,
  API_PORTAL,
  ERROR_CONF,
  REQ_ERROR_CONF,
  LOGIN_PAGE_PATH
}
