html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

// color
.blue-action {
  color: #3399ff;
}

// layout
.row {
  display: flex;
}
.row-reverse {
  display: flex;
  flex-direction: row-reverse;
}
.column {
  display: flex;
  flex-direction: column;
}
.item-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}

// 关于表格
th,
td {
  font-size: 12px;
}
.th-class {
  text-align: center !important;
  min-width: 150px;
}
.th-class span {
  font-size: 10px;
  font-weight: normal;
}
.td-class {
  text-align: center;
  vertical-align: middle !important;
}
.vgt-responsive {
  border-radius: 6px;
  border: 1px solid #eee;
}
.vgt-table {
  border: none;
}
.vgt-table thead th {
  color: #333;
  background: #f8f8f8;
}
.vgt-table.bordered td,
.vgt-table.bordered th {
  border: 1px solid #eee;
}
.vgt-wrap__actions-footer {
  border: none !important;
}
.container .span-pic-box[data-v-e44b7abe] {
  height: auto !important;
  line-height: 0;
}

// width
.w90 {
  width: 90%;
}

// 对齐方式
.pull-right {
  float: right;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.mr5 {
  margin-right: 5px;
}
.mr10 {
  margin-right: 10px;
}
.mr15 {
  margin-right: 15px;
}
.mr30 {
  margin-right: 30px;
}
.mr50 {
  margin-right: 50px;
}
.ml5 {
  margin-left: 5px;
}
.ml10 {
  margin-left: 10px;
}
.ml15 {
  margin-left: 15px;
}
.ml30 {
  margin-left: 30px;
}
.mt5 {
  margin-top: 5px;
}
.mt10 {
  margin-top: 10px;
}
.mt15 {
  margin-top: 15px;
}
.mt20 {
  margin-top: 20px;
}
.mt30 {
  margin-top: 30px;
}
.mt60 {
  margin-top: 60px;
}
.mt80 {
  margin-top: 80px;
}
.mb5 {
  margin-bottom: 5px;
}
.mb15 {
  margin-bottom: 15px;
}
.mb30 {
  margin-bottom: 30px;
}

// font
.f12 {
  font-size: 12px;
}
.f14 {
  font-size: 14px;
}
.f16 {
  font-size: 16px;
}
.f18 {
  font-size: 18px;
}
.f20 {
  font-size: 20px;
}
.f22 {
  font-size: 22px;
}
.f24 {
  font-size: 24px;
}
.f26 {
  font-size: 26px;
}
.f28 {
  font-size: 28px;
}
.pt0 {
  padding-top: 0;
}
.warn-text {
  padding-top: 20px;
  color: red;
}
.color-red {
  color: red;
}
// shadow
.shadow {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2), 0 2px 2px rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.12);
}

textarea.ivu-input {
  font-size: 12px !important;
}
// 菜单设置
.ivu-menu {
  font-size: 12px !important;
}
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
  padding-left: 50px;
  font-size: 12px !important;
}
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title {
  font-size: 12px !important;
}

.confirm-msg {
  color: blue;
  font-weight: bold;
  font-size: 14px;
}
