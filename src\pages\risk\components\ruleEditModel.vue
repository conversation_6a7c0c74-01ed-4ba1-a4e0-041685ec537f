<template>
  <div class="section-container">
    <div class="section-title">
      <span>规则控制</span>
    </div>
    <div class="rule-content">
      <div class="rule-header">
        <div class="rule-title">
          <span class="rule-range">分数范围0-100之间</span>
        </div>
        <i-button v-if="!isView" type="primary" @click="handleRuleAdd">添加规则</i-button>
      </div>
      <div class="rule-table-container">
        <i-table :columns="ruleColumns" :data="ruleList" border>
          <template slot-scope="{ row, index }" slot="action">
            <i-button type="primary" ghost @click="handleRuleView(row)" style="margin-right: 8px;">查看</i-button>
            <i-button v-if="!isView" type="error" ghost @click="handleRuleRemove(row, index)" style="margin-right: 8px;">移出</i-button>
            <i-button v-if="!isView" type="primary" ghost @click="handleRuleScoreEdit(row, index)">编辑分数</i-button>
          </template>
        </i-table>
        <div class="rule-summary">
          <span>合计分数：{{ ruleTotalScore }} (合计)</span>
        </div>
      </div>
    </div>

    <!-- 添加规则弹窗 -->
    <i-modal v-model="ruleAddModal" :closable="false" title="添加规则" width="500">
      <div class="rule-add-form">
        <!-- 搜索区域 -->
        <div class="rule-form-section">
          <div class="rule-form-label">规则搜索</div>
          <div class="search-container">
            <i-input
              v-model="ruleSearchQuery"
              style="width: 200px; margin-right: 10px;"
              placeholder="请输入规则名称"
            />
            <i-button type="primary" @click="handleRuleSearch" style="margin-right: 10px;">查询</i-button>
            <i-button @click="handleRuleSearchReset">重置</i-button>
          </div>
        </div>

        <!-- 规则选择区域 -->
        <div class="rule-form-section">
          <div class="rule-form-label">请选择规则</div>
          <i-select
            v-model="ruleAddForm.selectedRule"
            style="width: 100%; margin-bottom: 10px;"
            placeholder="请选择规则"
            :loading="ruleOptionsLoading"
          >
            <i-option
              v-for="item in ruleOptions"
              :key="item.rule_id"
              :value="item.rule_id"
              :label="item.rule_name"
            />
          </i-select>
        </div>
      </div>
      <div slot="footer">
        <i-button @click="ruleAddModal = false">取消</i-button>
        <i-button type="primary" @click="handleRuleAddConfirm">添加</i-button>
      </div>
    </i-modal>

    <!-- 编辑规则分数弹窗 -->
    <i-modal v-model="ruleScoreEditModal" :closable="false" title="编辑分数" width="400">
      <div class="rule-score-edit-form">
        <div class="rule-form-section">
          <div class="rule-form-label">请输入分数</div>
          <i-input
            v-model="ruleScoreEditForm.score"
            :max="100"
            :min="0"
            style="width: 100%"
          />
        </div>
      </div>
      <div slot="footer">
        <i-button @click="ruleScoreEditModal = false">取消</i-button>
        <i-button type="primary" @click="handleRuleScoreEditConfirm">确定</i-button>
      </div>
    </i-modal>

    <!-- 移除规则确认弹窗 -->
    <i-modal v-model="ruleRemoveModal" :closable="false" title="移除规则" width="400">
      <div class="rule-remove-confirm">
        <p>确定要移除该规则吗？</p>
      </div>
      <div slot="footer">
        <i-button @click="ruleRemoveModal = false">取消</i-button>
        <i-button type="primary" @click="handleRuleRemoveConfirm">确定</i-button>
      </div>
    </i-modal>
  </div>
</template>

<script>
export default {
  name: 'RuleEditModel',
  props: {
    type: {
      type: String,
      default: 'view' // view, edit, create
    },
    scene_id: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      ruleList: [],
      ruleColumns: [
        {
          title: 'ID',
          key: 'rule_id',
          width: 70
        },
        {
          title: '规则名称',
          key: 'rule_name',
          width: 190
        },
        {
          title: '分数变化*',
          key: 'point',
          width: 120,
          render: (h, params) => {
            return h('span', params.row.point ? `${params.row.point}` : '0')
          }
        },
        {
          title: '说明',
          key: 'rule_desc',
          width: 310
        },
        {
          title: '操作',
          slot: 'action',
          width: 260
        }
      ],
      ruleOptions: [],
      ruleOptionsLoading: false,
      ruleAddModal: false,
      ruleAddForm: {
        selectedRule: null
      },
      ruleScoreEditModal: false,
      ruleScoreEditForm: {
        score: 0,
        ruleId: null,
        index: -1
      },
      ruleRemoveModal: false,
      ruleRemoveForm: {
        ruleId: null,
        index: -1
      },
      ruleSearchQuery: ''
    }
  },
  computed: {
    isView() {
      return this.type === 'view'
    },
    ruleTotalScore() {
      return this.ruleList.reduce((total, rule) => {
        const score = parseInt(rule.point) || 0;
        return total + score;
      }, 0);
    }
  },
  mounted() {
  },
  methods: {
    // 设置规则列表数据（由父组件调用）
    setRuleList(ruleList) {
      this.ruleList = ruleList || [];
    },

    async loadRuleList() {
      // 触发父组件重新获取数据
      this.$emit('refresh-data');
    },

    async handleRuleAdd() {
      this.ruleAddForm.selectedRule = null;
      this.ruleAddModal = true;
      await this.loadRuleOptions();
    },

    async loadRuleOptions() {
      this.ruleOptionsLoading = true;
      try {
        const resp = await this.$api.queryAllRules()
        if (resp.status === 'success') {
          this.ruleOptions = resp.data;
        }
      } catch (error) {
        this.$message.error('获取规则选项失败');
      } finally {
        this.ruleOptionsLoading = false;
      }
    },

    async handleRuleAddConfirm() {
      if (!this.ruleAddForm.selectedRule) {
        this.$message.warning('请选择规则');
        return;
      }

      try {
        const params = {
          rule_id: this.ruleAddForm.selectedRule,
          scene_id: this.scene_id
        };
        const resp = await this.$api.bindRules(params);

        if (resp.status === 'success') {
          this.$message.success('添加规则成功');
          this.ruleAddModal = false;
          await this.loadRuleList();
        }
      } catch (error) {
        this.$message.error('添加规则失败');
      }
    },

    async handleRuleRemove(row, index) {
      this.ruleRemoveForm = {
        ruleId: row.rule_id,
        index: index
      };
      this.ruleRemoveModal = true;
    },

    async handleRuleRemoveConfirm() {
      try {
        const params = {
          rule_id: this.ruleRemoveForm.ruleId, // 修正：使用正确的规则ID
          scene_id: this.scene_id
        };
        const resp = await this.$api.deleteRules(params);
        if (resp.status === 'success') {
          this.ruleRemoveModal = false;
          this.$message.success('移出成功');
          // 重新加载规则列表
          await this.loadRuleList();
        }
      } catch (error) {
        this.$message.error('移出失败');
      }
    },

    handleRuleScoreEdit(row, index) {
      this.ruleScoreEditForm = {
        score: parseInt(row.point) || 0,
        ruleId: row.rule_id,
        index: index
      };
      this.ruleScoreEditModal = true;
    },

    async handleRuleScoreEditConfirm() {
      try {
        const params = {
          rule_id: this.ruleScoreEditForm.ruleId, // 修正：使用正确的规则ID
          scene_id: this.scene_id,
          point: this.ruleScoreEditForm.score // 修正：使用正确的字段名
        };
        const resp = await this.$api.changeRulesPoint(params);

        if (resp.status === 'success') {
          this.ruleScoreEditModal = false;
          this.$message.success('修改成功');
          // 重新加载规则列表
          await this.loadRuleList();
        }
      } catch (error) {
        this.$message.error('修改分数失败');
      }
    },

    handleRuleView(row) {
      this.$message.info(`查看规则: ${row.rule_name}`);
    },

    async handleRuleSearch() {
      if (!this.ruleSearchQuery) {
        this.$message.warning('请输入规则名称');
        return;
      }

      this.ruleOptionsLoading = true;
      try {
        const resp = await this.$api.queryRulesByName({ name: this.ruleSearchQuery });
        if (resp.status === 'success') {
          this.ruleOptions = resp.data;
        } else {
          this.$message.warning('未找到相关规则');
        }
      } catch (error) {
        this.$message.error('查询规则失败');
      } finally {
        this.ruleOptionsLoading = false;
      }
    },

    handleRuleSearchReset() {
      this.ruleSearchQuery = '';
      this.loadRuleOptions();
    }
  }
}
</script>

<style lang="less" scoped>
.section-container {
  margin-bottom: 20px;
  width: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #17233d;
}

.rule-content {
  border: 1px solid #e8eaec;
  padding: 20px;
  border-radius: 4px;
  background: #fff;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rule-title {
  display: flex;
  align-items: center;
  gap: 20px;

  .rule-range {
    color: #666;
    font-size: 14px;
  }
}

.rule-table-container {
  position: relative;
  margin-top: 15px;
}

.rule-summary {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  background: #f8f9fa;
  font-weight: bold;
}

.rule-add-form,
.rule-score-edit-form {
  padding: 10px 0;
}

.rule-form-section {
  .rule-form-label {
    margin-bottom: 8px;
    font-weight: bold;
    color: #17233d;
  }
}

.search-container {
  display: flex;
  align-items: center;

  i-button {
    margin-right: 10px;
  }
}

.rule-remove-confirm {
  padding: 20px 0;
  text-align: center;

  p {
    font-size: 14px;
    color: #17233d;
  }
}
</style>
