/**
 * 公共API
 */

import fetch from '@/utils/network/fetch'

// 获取token
const getToken = (code, objc = {}) => {
  return fetch(`/api/v1/oauth/token?code=${code}`, objc)
}

// 获取权限下门店列表
const getStoreList = (objc = {}) => {
  return fetch('/api/v1/stores/authorized', objc)
}

const getAllStoreList = (objc = {}) => {
  return fetch('/api/v1/stores', objc)
}

// logout
const goLogout = (objc = {}) => {
  return fetch('/api/v1/oauth/logout', objc)
}

export default {
  getToken,
  goLogout,
  getStoreList,
  getAllStoreList
}
