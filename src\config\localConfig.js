/*
 * 本地存储配置
 */

const TOKEN = '_token_'                 // token

const ROUTES = '_routes'                // 动态路由存储

const USER_INFO = '_userInfo_'          // 用户信息包括 个人信息 

const PERMISSIONS = '_permissions_'     // 权限列表

const STORE = '_store_'                 // 门店

const STORE_LIST = "_store_list_"       // 门店列表

const TOKENFLAG = "_token_flag_"		// 暂时记录请求token

const PAGE_INFO = "_page_info_"		    // 页面信息


export default {
    TOKEN,
    ROUTES,
    USER_INFO,
    PERMISSIONS,
    STORE,
    STORE_LIST,
    PAGE_INFO,
    TOKENFLAG
}

