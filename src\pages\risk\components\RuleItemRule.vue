<template>
  <div class="rule-item" :class="{ 'rule-item-child': level > 0 }">
    <div class="rule-content">
      <!-- 关系选择 -->
      <i-select
        v-if="showRelation"
        v-model="localRule.relation.id"
        style="width: 60px; margin-right: 10px"
        :disabled="disabled"
        @on-change="handleRelationChange"
      >
        <i-option
          v-for="item in relations"
          :key="item.id"
          :value="item.id"
          :label="item.name"
        />
      </i-select>

      <!-- 左侧：不可编辑参数选择 -->
      <i-select
        v-model="localRule.leftParam.id"
        style="width: 150px; margin-right: 10px"
        :disabled="disabled"
        placeholder="选择左侧参数"
        @on-change="handleLeftParamChange"
      >
        <i-option
          v-for="item in leftOptions"
          :key="item.key"
          :value="item.key"
          :label="item.name"
        />
      </i-select>

      <!-- 判断条件选择 -->
      <i-select
        v-model="localRule.judge.id"
        style="width: 90px; margin-right: 10px"
        :disabled="disabled"
        placeholder="选择条件"
        @on-change="handleJudgeChange"
      >
        <i-option
          v-for="item in judges"
          :key="item.id"
          :value="item.id"
          :label="item.name"
        />
      </i-select>

      <!-- 右侧：可编辑参数选择 -->
      <i-select
        v-model="localRule.rightParam.id"
        style="width: 150px; margin-right: 10px"
        :disabled="disabled"
        placeholder="选择右侧参数"
        @on-change="handleRightParamChange"
      >
        <i-option
          v-for="item in rightOptions"
          :key="item.key"
          :value="item.key"
          :label="item.name"
        />
      </i-select>

      <!-- 操作按钮 -->
      <div class="rule-actions">
        <i-button
          v-if="canAddSameLevel"
          type="primary"
          icon="md-add"
          size="small"
          :disabled="disabled"
          @click="handleAddSameLevel"
        />
        <i-button
          v-if="canAddChild"
          type="primary"
          icon="md-git-branch"
          size="small"
          :disabled="disabled"
          @click="handleAddChild"
        />
        <i-button
          v-if="canDelete"
          type="error"
          icon="md-remove"
          size="small"
          :disabled="disabled"
          @click="handleDelete"
        />
      </div>
    </div>

    <!-- 子规则 -->
    <div v-if="localRule.children && localRule.children.length" class="rule-children">
      <rule-item
        v-for="(child, index) in localRule.children"
        :key="`child-${level}-${index}-${child._uid || Math.random()}`"
        :value="child"
        :level="level + 1"
        :disabled="disabled"
        :show-relation="index > 0"
        :rule-index="index"
        :rule-list-options="ruleListOptions"
        :total-rules-in-level="localRule.children.length"
        @change="handleChildChange(index, $event)"
        @add-same-level="handleChildAddSameLevel(index)"
        @add-child="handleChildAddChild(index)"
        @delete="handleChildDelete(index)"
      />
    </div>
  </div>
</template>

<script>
import { baseTypes, judges, selectTypes, relations } from '../constants/ruleOptions'

export default {
  name: 'RuleItemRule',
  props: {
    value: {
      type: Object,
      required: true
    },
    ruleListOptions: {
      type: Array,
      default: () => []
    },
    leftOptions: {
      type: Array,
      default: () => []
    },
    rightOptions: {
      type: Array,
      default: () => []
    },
    level: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showRelation: {
      type: Boolean,
      default: false
    },
    ruleIndex: {
      type: Number,
      default: 0
    },
    totalRulesInLevel: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      judges,
      relations,
      localRule: {
        _uid: null,
        leftParam: { id: null, name: '' },
        judge: { id: null, name: '' },
        rightParam: { id: null, name: '' },
        relation: { id: 'and', name: '且' },
        children: []
      }
    }
  },
  computed: {
    displayValue() {
      // 现在不需要显示值，因为我们有左右两个选择器
      return '';
    },
    canDelete() {
      // 1. Is this rule the last one in its own level?
      const isLastInLevel = this.ruleIndex === this.totalRulesInLevel - 1;

      // If it's not the last one, it can never be deleted.
      if (!isLastInLevel) {
          console.log('不允许删除：不是最后一个规则', {
            ruleIndex: this.ruleIndex,
            totalRules: this.totalRulesInLevel
          })
          return false;
      }

      // 2. If it IS the last one, we have further checks.
      // It's a top-level rule (a parent)
      if (this.level === 0) {
          // It can be deleted only if there is more than one parent rule.
          const canDelete = this.totalRulesInLevel > 1
          console.log('顶层规则删除检查', {
            totalRules: this.totalRulesInLevel,
            canDelete
          })
          return canDelete;
      }
      // It's a child or grandchild rule (level > 0)
      else {
          console.log('子规则可以删除，因为是最后一个', {
            level: this.level,
            ruleIndex: this.ruleIndex
          })
          return true;
      }
    },
    canAddSameLevel() {
      return this.level < 3 // 最多3层
    },
    canAddChild() {
      return this.level < 2 && (!this.localRule.children || this.localRule.children.length === 0)
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.localRule = {
            _uid: newVal._uid || Date.now() + Math.random(),
            baseType: newVal.baseType || { id: null, name: '' },
            judge: newVal.judge || { id: null, name: '' },
            relation: newVal.relation || { id: 'and', name: '且' },
            children: newVal.children || []
          }
          // 添加值变化的日志
          console.log('RuleItem value changed ----')
          console.log('New Value:', JSON.stringify(newVal, null, 2))
          console.log('Local Rule after update:', JSON.stringify(this.localRule, null, 2))
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    console.log('初始化规则 ----', {
      ruleIndex: this.ruleIndex,
      rule: this.localRule,
      totalRules: this.totalRulesInLevel
    })
  },
  methods: {
    createEmptyRule() {
      return {
        _uid: Date.now() + Math.random(),
        baseType: { id: null, name: '' },
        judge: { id: null, name: '' },
        relation: { id: 'and', name: '且' },
        children: []
      }
    },
    handleRelationChange(id) {
      this.localRule.relation = this.relations.find(item => item.id === id) || { id: 'and', name: '且' }
      console.log('Relation Changed ----')
      console.log('New Relation:', JSON.stringify(this.localRule.relation, null, 2))
      this.emitChange()
    },
    handleBaseTypeChange(id) {
      const selectedOption = this.ruleListOptions.find(item => item.key === id)
      this.localRule.baseType = {
        id: selectedOption.key,
        name: selectedOption.baseTypeDisplay || selectedOption.key
      }
      console.log('BaseType Changed ----')
      console.log('New BaseType:', JSON.stringify(this.localRule.baseType, null, 2))
      this.emitChange()
    },
    handleJudgeChange(id) {
      this.localRule.judge = this.judges.find(item => item.id === id) || { id: null, name: '' }
      console.log('Judge Changed ----')
      console.log('New Judge:', JSON.stringify(this.localRule.judge, null, 2))
      this.emitChange()
    },
    emitChange() {
      console.log('Emitting Change ----')
      console.log('Current Rule State:', JSON.stringify(this.localRule, null, 2))
      this.$emit('change', { ...this.localRule })
    },
    handleAddSameLevel() {
      this.$emit('add-same-level')
    },
    handleAddChild() {
      this.$emit('add-child')
    },
    handleDelete() {
      this.$emit('delete')
    },
    handleChildChange(index, data) {
      if (this.localRule.children[index]) {
        this.localRule.children[index] = { ...data }
        this.emitChange()
      }
    },
    handleChildAddSameLevel(index) {
      if (this.localRule.children.length < 5) {
        this.localRule.children.splice(index + 1, 0, this.createEmptyRule())
        this.emitChange()
      }
    },
    handleChildAddChild(index) {
      if (!this.localRule.children[index].children) {
        this.localRule.children[index].children = []
      }
      this.localRule.children[index].children.push(this.createEmptyRule())
      this.emitChange()
    },
    handleChildDelete(index) {
      this.localRule.children.splice(index, 1)
      this.emitChange()
    }
  }
}
</script>

<style lang="less" scoped>
.rule-item {
  margin-bottom: 10px;

  .rule-content {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .rule-children {
    margin-left: 20px;
    padding-left: 20px;
    border-left: 1px dashed #e8eaec;
  }

  .rule-actions {
    display: flex;
    gap: 5px;
    margin-left: auto; /* 将按钮推到最右侧 */
  }

  &.rule-item-child {
    margin-left: 20px;
  }
}
</style>
