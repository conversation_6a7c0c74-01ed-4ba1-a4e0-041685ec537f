<script>
import { DatePicker } from 'iview'
import ModelModal from './profile/modelViewModal.vue'

export default {
  name: 'RiskModel',
  components: {
    DatePicker,
    ModelModal
  },
  data() {
    return {
      basicForm: {
        sceneType: '',
        scene: '',
        description: '',
        isBlock: false,
        status: '草稿'
      },
      whiteListSearch: '',
      blackListSearch: '',
      whiteList: [
       ],
      blackList: [],
      showModal: false,
      modalTitle: '',
      biz_type: '', // 改为空字符串，动态设置
      modalType: 'view',
      scene_id: null, // 添加scene_id字段
      modelList: [],
      modelColumns: [
        {
          title: 'ID',
          key: 'scene_id',
          width: 80,
          className: 'th-class th-class-name text'
        },
        {
          title: '场景大类',
          key: 'classification',
          width: 120,
          className: 'th-class th-class-name text'
        },
        {
          title: '场景',
          key: 'scene_name',
          width: 150,
          className: 'th-class th-class-name text'
        },
        {
          title: '说明',
          key: 'scene_desc',
          className: 'th-class th-class-name text'
        },
        {
          title: '是否拦截',
          key: 'intercept_status',
          width: 100,
          className: 'th-class th-class-name text',
          render: (h, params) => {
            return h('span', params.row.intercept_status ? '是' : '否')
          }
        },
        {
          title: '状态',
          key: 'enable_status',
          width: 100,
          className: 'th-class th-class-name text',
          render: (h, params) => {
            return h('span', params.row.enable_status ? '启用' : '停用')
          }
        },
        {
          title: '最后更新时间',
          key: 'update_time',
          width: 150,
          className: 'th-class th-class-name text'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          className: 'th-class th-class-name',
          width: 150,
          render: (h, params) => {
            return h('div', {
              style: {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }
            }, [
              h('i-button', {
                class: 'group-operation-btn',
                props: {
                  type: 'primary',
                  ghost: true
                },
                style: {
                  marginRight: '10px'
                },
                on: {
                  click: () => {
                    this.handleEdit(params)
                  }
                }
              }, '编辑'),
              h('i-button', {
                class: 'group-operation-btn',
                props: {
                  type: 'primary',
                  ghost: true
                },
                on: {
                  click: () => {
                    this.handleView(params)
                  }
                }
              }, '查看')
            ])
          }
        }
      ]
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    // async initData() {
    //   await Promise.all([
    //   ])
    // },

    // 获取表格数据
    async queryTableData() {
      try {
        const resp = await this.$api.queryAllScene()
        // const resp = {
        //   status: 'success',
        //   data: {
        //     items: [
        //       {
        //         scene_id: 1,
        //         classification: '风险控制',
        //         scene_name: '积分商城兑换实物商品',
        //         scene_desc: '用户在积分商城兑换实物商品时的风险评估',
        //         intercept_status: false,
        //         enable_status: true,
        //         create_by: 'admin',
        //         updateTime: '2025/1/1'
        //       },
        //       {
        //         scene_id: 2,
        //         classification: '燃值使用',
        //         scene_name: '积分抵现',
        //         scene_desc: '用户使用积分抵现时的风险评估',
        //         intercept_status: true,
        //         enable_status: false,
        //         create_by: 'admin',
        //         updateTime: '2025/1/1'
        //       }
        //     ]
        //   }
        // }

        if (resp.status === 'success') {
          this.modelList = resp.data
        }
      } catch (error) {
        this.$Message.error('获取数据失败')
      }
    },

    // 创建按钮点击事件
    handleCreate() {
      this.scene_id = null // 创建时清空scene_id
      this.biz_type = '' // 创建时清空biz_type
      this.modalType = 'create'
      this.modalTitle = '创建场景'
      this.showModal = true
    },

    // 编辑按钮点击事件
    handleEdit(params) {
      this.modalType = 'edit'
      this.modalTitle = '编辑场景'
      this.scene_id = params.row.scene_id // 传递scene_id
      this.biz_type = params.row.scene_name // 动态设置biz_type为scene_name
      this.showModal = true
    },

    // 查看按钮点击事件
    handleView(params) {
      this.modalType = 'view'
      this.modalTitle = '查看场景'
      this.scene_id = params.row.scene_id // 传递scene_id
      this.biz_type = params.row.scene_name // 动态设置biz_type为scene_name
      this.showModal = true
    },

    // 弹窗取消事件
    handleModalCancel() {
      this.showModal = false
      this.modalTitle = ''
      this.modalType = 'view'
      this.queryTableData() // 关闭弹窗后刷新数据
    },

    // 保存成功事件
    handleModalSaveSuccess() {
      this.queryTableData() // 刷新表格数据
    }
  }
}
</script>

<template>
  <div class="risk-model">
    <i-row class="mb20">
      <i-col :span="24" style="text-align: right;">
        <i-button type="primary" @click="handleCreate">创建</i-button>
      </i-col>
    </i-row>
    <i-row>
      <i-table :columns="modelColumns" :data="modelList" border class="risk-model-table-box"></i-table>
    </i-row>
    <model-modal
      v-if="showModal"
      v-model="showModal"
      :title="modalTitle"
      :biz_type="biz_type"
      :type="modalType"
      :scene_id="scene_id"
      @cancel="handleModalCancel"
      @save-success="handleModalSaveSuccess"
    ></model-modal>
  </div>
</template>

<style scoped lang="less">
.risk-model-page {
  height: 100%;
  width: 100%;
}

.section-box {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #17233d;
}

.form-content {
  max-width: 600px;
}

.form-actions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e8eaec;
}

.list-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}
.list-header-title {
  font-size: 20px;
}

.mt20 {
  margin-top: 20px;
}

/deep/ .ivu-btn-ghost {
  &.ivu-btn-primary {
    color: rgba(54, 67, 186, 1);
    background-color: transparent;
    border: none;
  }

  &.ivu-btn-error {
    color: #ed4014;
    background-color: transparent;
    border: none;
  }
}

.risk-model {
  padding: 20px;

  &-table-box {
    min-height: 200px;
    background: #fff;
  }
}

.mb20 {
  margin-bottom: 20px;
}

/deep/ .th-class-name.text {
  font-size: 14px !important;
}

/deep/ .group-operation-btn {
  color: rgba(54, 67, 186, 1);
  background-color: rgba(0,0,0,0);
  border: none;
  font-size: 14px !important;
  padding: 0 4px;

  &:active, &:focus, &:hover {
    background-color: rgba(0,0,0,0);
    border: none;
    box-shadow: none;
  }
}
</style>
