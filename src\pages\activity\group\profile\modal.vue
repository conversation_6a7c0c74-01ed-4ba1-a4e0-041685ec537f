<template>
  <div class="container">
    <i-modal footer-hide fullscreen :closable="false" :title="title" v-model="showDialog">
      <div class="modal-content-group-box modal-content mt30 flex column item-center">
        <i-form :model="template">
          <i-input v-model="template.id" v-show="false" />
          <i-row style="min-width:1100px">
            <i-col span="12">
              <i-form-item label="标题" class="modal-item text-left">
                <i-poptip trigger="focus" content="字数限制10个" placement="top-start">
                  <i-input class="search mr15" v-model="template.name" :maxlength="10" clearable/>
                </i-poptip>
              </i-form-item>
              <i-form-item label="运动分类" class="modal-item text-left">
                <i-select class="search mr15 text-left" v-model="template.categoryId" 
                  placeholder="请选择运动分类" clearable @on-change="changeCategory">
                  <i-option :value="item.id" v-for="item in categoryList" :key="item.id" class="text-left"> 
                    {{item.name}}
                  </i-option>
                </i-select>
              </i-form-item>
              <i-form-item label="类型" class="modal-item">
                <i-select class="search mr15" v-model="template.type" placeholder="请选择类型" @on-change="changeType" clearable>
                  <i-option v-for="item in typeList" :value="item.value" :key="item.value">
                    {{item.label}}
                  </i-option>
                </i-select>
              </i-form-item>
              <i-form-item label="面向人群" class="modal-item">
                <i-select class="search mr15" v-model="template.target" placeholder="请选择面向人群" clearable>
                  <i-option v-for="item in targetListCopy" :value="item.value" :key="item.value">
                    {{item.label}}
                  </i-option>
                </i-select>
              </i-form-item>
              <i-form-item label="运动等级" class="modal-item">
                <i-select class="search mr15" v-model="template.level" placeholder="请选择运动等级" clearable>
                  <i-option v-for="item in levelList" :value="item.value" :key="item.value">
                    {{item.label}}
                  </i-option>
                </i-select>
              </i-form-item>
              <i-form-item label="普通精选">
                <i-switch v-model="template['isStar']" size="large" @on-change="changeStar" style="margin-left:20px">
                  <span slot="open">是</span>
                  <span slot="close">否</span>
                </i-switch>
              </i-form-item>
              <template v-if="nationalFlag && template['isStar']">
                <i-form-item label="全国精选">
                  <i-switch v-model="template['isNationalStar']" size="large" style="margin-left:20px">
                    <span slot="open">是</span>
                    <span slot="close">否</span>
                  </i-switch>
                </i-form-item>
              </template>
              <i-form-item label="门店启用">
                <span class="ml30 confirm-msg">{{template.usedCount}}</span>次
              </i-form-item>
            </i-col>
            <i-col span="12">
              <i-form-item label="主图"  class="modal-item">
                <i-input v-model="template.galleryId" v-show="false" />
                <template v-if="template.galleryUrl">
                  <img :src="template.galleryUrl" width="320" height="180">
                </template>
                <i-button  icon="md-image" size="large" @click="popupGalleryChoose">选择图片</i-button>
              </i-form-item>
              <i-form-item label="详情"  class="modal-item">
                <i-input v-model="template.description" type="textarea" :rows="rows" placeholder="请输入活动详情..." clearable/>
              </i-form-item>
            </i-col>
          </i-row>
          <div class="mt30 flex row justify-center">
            <i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
            <i-button type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
          </div>
        </i-form>
      </div>
    </i-modal>
    <g-modal v-if="showGalleryDialog" :close="galleryClose" />
  </div>
</template>
<script>
import cfg from '../../config'
import { checkFill, judgeFill } from '@/utils/util'
import Modal from '../../gallery/popup'

export default {
  name: 'national_template_modal',
  components: {
    'g-modal': Modal
  },
  props: {
    title: '',
    opFrom: '',
    activityId: '',
    
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      showDialog: true,
      categoryList: [],               // 分类列表

      template: {
        id: '',               // ID
        name: '',             // 标题
        categoryId: '',       // 分类 
        type: '',             // 类型
        target: '',           // 面向人群
        level: '',            // 等级
        description: '',      // 描述
        galleryId: '',        // 图库图片ID
        galleryUrl: '',       // 图库图片URL
        active: true,         // 状态
        isTemplate: true,     // 是否是模板
      },

      typeList: cfg.typeList,         // 活动类型
      targetList: cfg.targetList,     // 面向人群
      levelList: cfg.levelList,       // 运动等级
      statusList: cfg.statusList,     // 状态

      targetListCopy: [],             // 面向人群列表副本
      
      activityRules: {
        name: { value: null, error: false, text: '请输入标题' },
        categoryId: { value: null, error: false, text: '请选择分类' },
        type: { value: null, error: false, text: '请选择类型' },
        target: { value: null, error: false, text: '请选择面向人群' },
        level: { value: null, error: false, text: '请选择运动等级' },
        description: { value: null, error: false, text: '请填写活动详情' },
        status: { value: null, error: false, text: '请填写活动状态' },
        galleryId: { value: null, error: false, text: '请选择活动主图' }
      },

      rows: 7,
      nationalFlag: false,

      selectedGallery: '',
      showGalleryDialog: false
    }
  },
  mounted() {
    this.getCategories()
    if (this.activityId) {
      this.getTemplateDetail(this.activityId)
    } else {
      this.targetListCopy = [...this.targetList]
      this.processNationalStar()
    }
  },
  methods: {
    // 获取模板详情
    async getTemplateDetail(id) {
      let resp = await this.$api.getTemplateById(id)
      if (resp['status'] == 'success') {
        this.template = resp.data
        this.template['categoryId'] = this.template.category.id
        this.template['galleryId'] = this.template.gallery.id
        this.template['galleryUrl'] = this.template.gallery.url
        this.template['isStar'] = this.template.isStar ? this.template.isStar : false
        this.template['isNationalStar'] = this.template.isNationalStar ? this.template.isNationalStar : false
        if (this.template.type == 'PARENT_CHILD') {
          this.targetListCopy = this.targetList.filter((target) => target.value != "ADULT" && target.value != "FULL")
        }
        this.processNationalStar(this.template.id)
      }
    },
    // 处理全国精选展示
    async processNationalStar(id) {
      let resp = await this.$api.countNationalStarTemplate(this.template.id)
      this.nationalFlag = !(resp.data > 0)
      this.rows = this.nationalFlag && this.template['isStar'] ? 8 : 6;
    },
    // 获取分类列表
    async getCategories() {
      let resp = await this.$api.getSportsCategories()
      this.categoryList = resp.data
    },
    changeCategory(option) {
      if ((!this.selectedGallery && option != this.template.category.id) 
        || (this.selectedGallery && this.selectedGallery.categoryId != this.template.categoryId)) 
      {
        this.$message.warning({ content: '运动分类与模板主图分类不一致', closable: true, duration: 3 })
        this.template.categoryId = this.template.category.id
      }
    },
    // 改变类型(亲子类型, 人群只有儿童和青少年)
    changeType(option) {
      this.template.target = ''
      if (option === 'PARENT_CHILD') {
        this.targetListCopy = this.targetList.filter((target) => target.value != "ADULT" && target.value != "FULL")
      } else {
        this.targetListCopy = [...this.targetList]
      }
    },
    changeStar(value) {
      if (this.nationalFlag && value) {
        this.rows = 8
      } else {
        this.rows = 6
        this.template['isNationalStar'] = false
      }
    },
    // 选择图片框
    popupGalleryChoose() {
      this.showGalleryDialog = true
    },
    // 图片框关闭回调事件
    galleryClose(item) {
      this.selectedGallery = item
      if (item) {
        this.template['galleryId'] = item.id
        this.template['galleryUrl'] = item.url
      }
      this.showGalleryDialog = false
    },
    // 新建/更新门店活动
    async certainClick() {
      /* checkFill(this.rules)
      let judge = judgeFill(this.rules)
      if (judge) { */
        this.template['category'] = { id: this.template.categoryId  }
        this.template['gallery'] = { id: this.template.galleryId }
        this.template['isTemplate'] = true
        this.template['active'] = true
        if (this.template.name.length > 10) {
          this.$message.warning({ content: '活动名称须10字以内', closable: true, duration: 3 })
          return
        }
        if (this.template.galleryId) {
          if ((this.selectedGallery && this.template.category.id != this.selectedGallery.categoryId)) {
            this.$message.warning({ content: '运动分类与模板主图分类不一致', closable: true, duration: 3 })
            return
          }
        } else {
          this.$message.warning({ content: '请选择模板主图', closable: true, duration: 3 })
          return
        }
        let resp = await this.$api.createOrUpdateTemplate(this.template)
        if (resp.status === 'success') {
          this.certain && this.certain()
        }
      // }
    },
    cancelClick() {
      this.showDialog = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
  }
}
</script>
<style lang="less" scoped>
.modal-content-group-box {
  width: 100vw;
  .modal-item {
    width: 90%;
    text-align: right!important;
    label {
      width:30px!important;
    }
  }
}
</style>