import Mock from 'mockjs'

const roles = [
  {
    title: 'IT Admin',
    align: 'center',
    permissions: [
      {
        id: '1',
        title: 'ACCOUNT_MANAGEMENT_W'
      },
      {
        id: '2',
        title: 'CC_MANAGEMENT'
      }
    ]
  },
  {
    title: 'DB Admin',
    align: 'center',
    permissions: [
      {
        id: '1',
        title: 'ACCOUNT_MANAGEMENT_W'
      },
      {
        id: '2',
        title: 'CC_MANAGEMENT'
      }
    ]
  },
  {
    title: 'IT Manager',
    align: 'center',
    permissions: [
      {
        id: '3',
        title: 'ACCOUNT_MANAGEMENT_R'
      },
      {
        id: '4',
        title: 'FOLLOWER_MANAGEMENT'
      }
    ]
  },
  {
    title: 'DB Manager',
    align: 'center',
    permissions: [
      {
        id: '5',
        title: 'QR_CODE_MANAGEMENT'
      },
      {
        id: '6',
        title: 'AUTO_REPLY_MANAGEMENT_W'
      }
    ]
  },
  {
    title: 'BC Manager',
    align: 'center',
    permissions: [
      {
        id: '5',
        title: 'QR_CODE_MANAGEMENT'
      },
      {
        id: '6',
        title: 'AUTO_REPLY_MANAGEMENT_W'
      }
    ]
  },
  {
    title: 'DA Manager',
    align: 'center',
    permissions: [
      {
        id: '5',
        title: 'QR_CODE_MANAGEMENT'
      },
      {
        id: '6',
        title: 'AUTO_REPLY_MANAGEMENT_W'
      }
    ]
  },
  {
    title: 'BF Manager',
    align: 'center',
    permissions: [
      {
        id: '5',
        title: 'QR_CODE_MANAGEMENT'
      },
      {
        id: '6',
        title: 'AUTO_REPLY_MANAGEMENT_W'
      },
      {
        id: '7',
        title: 'BRAND_CAMPAIGN_MANAGEMENT'
      }
    ]
  }
]

Mock.mock('/roles', 'get', roles)