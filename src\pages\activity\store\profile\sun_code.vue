<template>
	<div class="qrcode-modal">
		<i-modal footer-hide width="600px" title="太阳码" v-model="showDialog" @on-cancel="cancelClick">
			<div v-if="imageUrl" class="row item-center justify-center">
				<img :src="imageUrl" class="qrcode" alt="太阳码">
			</div>
      <div v-else class="modal-content mt30 column">
        <i-form :model="qrCodeForm" label-position="right" :label-width="80">
					<i-form-item label="名称" class="modal-item">
						<i-input v-model="qrCodeForm.name" placeholder="名称" :clearable="true" />
					</i-form-item>
					<i-form-item label="跳转" class="modal-item">
						<i-input v-model="qrCodeForm.path" placeholder="跳转URL" :clearable="true" />
					</i-form-item>
					<div class="mt30 flex row justify-center">
						<i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
						<i-button type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px" v-if="imageUrl == ''">生成</i-button>
					</div>
				</i-form>
      </div>
    </i-modal>
  </div>
</template>
<script>
export default {
  name: 'session_cancel_modal',
  props: {
		id: '',
    url: '',
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
			showDialog: true,
			imageUrl: '',
			qrCodeForm: {
				name: '',		// 名称
				path: ''		// 跳转URL
			}
    }
	},
	mounted() {
		this.imageUrl = this.url
	},
  methods: {
		async certainClick() {
			let resp = await this.$api.previewSunCode(this.id, this.qrCodeForm)
			if (resp.status == 'success') {
				this.imageUrl = resp.data
			}
		},
    cancelClick() {
      this.cancel && this.cancel()
    }
  }
}
</script>
<style lang="less" scoped>
.qrcode-modal {
	.modal-content {
		width: 100%;
		.modal-item {
			width: 500px;
		}
	}
}
.qrcode {
	width: 200px;
	height: 200px;
	margin: 20px 0
}
</style>