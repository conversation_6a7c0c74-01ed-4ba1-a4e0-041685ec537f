import fetch from '@/utils/network/fetch'

// 添加产品到模板库
const createTemplateProduct = (body = {}) => {
  return fetch('/api/v1/products/national', { method: 'post', data: body })
}

// 禁用模板库产品
const disableTemplateProduct = id => {
  return fetch(`/api/v1/products/national/${id}`, { method: 'delete' })
}

// 搜索产品
const searchNationalProduct = (query = {}) => {
  return fetch('/api/v1/products/national', { data: query })
}

export default {
  createTemplateProduct,
  disableTemplateProduct,
  searchNationalProduct
}
