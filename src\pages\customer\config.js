// 性别类型
const genderList = [{ value: 1, label: '男' }, { value: 2, label: '女' }]
// 积分操作类型
const pointOpList = [{ value: '+', label: '增加' }, { value: '-', label: '减少' }]

const changeReasonList = ['系统故障补积分', '新会员或者忘记会员号补积分', '多倍积分退换货补积分差', '团购补录', '其他']

const purchaseStatusList = [
  { value: 'finished', label: '已完成' },
  { value: 'canceled', label: '已取消' },
  { value: 'authorized', label: '待确认' }
]

const digitalPurchaseStatusList = [
  { value: 'finished', label: '已完成' },
  { value: 'canceled', label: '已取消' },
  { value: 'authorized', label: '已支付' },
  { value: 'shipped', label: '已发货' },
  { value: 'partialship', label: '部分发货' }
]

// 注销会员原因
const memberRevokeReasonList = [
  { value: 'RECEIVED_TOO_MANY_NOTIFICATIONS', label: '收到过多迪卡侬消息通知' },
  { value: 'HAS_DUPLICATE_ACCOUNT', label: '拥有两个账号' },
  { value: 'UNSATISFIED', label: '用户不满意' },
  { value: 'LEGAL_REASON', label: '法律原因' },
  { value: 'OTHER', label: '其他' },
]

// 字段名称转换
const convert = function(value, list) {
  let result = value
  for (let i = 0; i < list.length; i++) {
    let item = list[i]
    if (item.value === value) {
      result = item.label
      break
    }
  }
  return result
}

export default {
  genderList,
  pointOpList,
  changeReasonList,
  purchaseStatusList,
  digitalPurchaseStatusList,
  memberRevokeReasonList,
  convert
}
