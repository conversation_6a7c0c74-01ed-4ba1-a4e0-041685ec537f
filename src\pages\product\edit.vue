<template>
  <div class="container">
    <i-modal footer-hide width="1000" :closable="false" title="编辑产品信息" v-model="showDialog">
      <i-table border :columns="productColumns" :data="productList" />
      <div class="item-center justify-between mt20">
        <i-form>
          <i-row>
            <i-col :span=12>
              <i-form-item label="名称" class="modal-item">
                <i-input v-model="title" class="text" placeholder="请输入产品名称" :maxlength="10" :disabled="disabled" />
              </i-form-item>
            </i-col>
            <i-col :span=12>
              <i-form-item label="描述" class="modal-item">
                <i-input v-model="description" class="text" type="textarea" :autosize="{minRows:3,maxRows:8}" placeholder="请输入产品描述" :maxlength="255" :disabled="disabled" />
              </i-form-item>
            </i-col>
          </i-row>
        </i-form>
      </div>
      <div class="mt30 flex row justify-center">
        <i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
        <i-button v-if="!disabled" type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
      </div>
    </i-modal>
  </div>
</template>
<script>
export default {
  name: 'edit_product_modal',
  props: {
    id: '',
    from: '',
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      showDialog: true,

      productList: [],     // DKT产品列表
      productColumns: [
        { title: 'Model Code', key: 'modelCode', className: 'th-class' },
        { title: 'Item Code', key: 'itemCode', className: 'th-class' },
        { title: '名称', key: 'title', className: 'th-class' },
        { title: '描述', key: 'description', className: 'th-class' },
        { title: '图片', className: 'th-class', fixed: 'right', width: 160, render: (h, params) => { return this.showRowImage(h, params.row.imageUrls) }},
      ],
      title: '',
      description: '',
      disabled: '',
    }
  },
  mounted() {
    if (this.id) {
      this.getProductDetail(this.id)
    }
  },
  methods: {
    // 获取产品详情
    async getProductDetail(id) {
      let resp
      if (this.from == 'template') {
        resp = await this.$api.searchNationalProduct({ id: id })
      } else {
        resp = await this.$api.searchProduct({ id: id })
      }
      if (resp['status'] == 'success') {
        let item = resp.data.items[0]
        this.productList = []
        this.productList.push(item.productInfo)
        this.title = item.productInfo.title
        this.description = item.productInfo.description
        this.disabled = item.status == 'PUBLISHED' && !item.isTemplate
      }
    },

    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'span-pic-box' }, [h('img', { attrs: { src: imageUrls[0], class: 'table-img mt5', width:'70px', height: '70px' } })])
    },

    async certainClick() {
      if (this.title.length > 10) {
        this.$message.warning({ content: '请控制商品名称在10字以内', closable: true, duration: 3 })
        return
      }
      let resp = await this.$api.updateProduct(this.id, JSON.stringify({title: this.title, description: this.description}))
			if (resp.status == 'success') {
					this.certain && this.certain()
			}
    },
    cancelClick() {
      this.showDialog = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
  }
}
</script>
<style lang="less" scoped>
.text {
  width: 90%;
}
.mt5 {
  margin-top: 5px;
}
</style>
