// 活动类型
const typeList = [
    { value: 'PARENT_CHILD', label: '亲子' },
    { value: 'SINGLE', label: '单人' }
]

// 面向人群
const targetList = [
    { value: 'CHILD', label: '儿童(3-13岁)' },
    { value: 'YOUNG', label: '青少年(14-18岁)' },
    { value: 'ADULT', label: '成人(18岁+)' },
    { value: 'FULL', label: '全年龄段' }
]

// 运动等级
const levelList = [
    { value: 'JUNIOR', label: '初级' },
    { value: 'MIDDLE', label: '中级' },
    { value: 'SENIOR', label: '高级' },
]

// 报名方式
const paymentList = [
    { value: 'FREE', label: '免费' },
    { value: 'POINT', label: '积分兑换' },
]

// 状态
const statusList = [
    { value: 'UNPUBLISHED', label: '未发布' },
    { value: 'PUBLISHED', label: '已发布' },
]

// 字段名称转换
const convert = function(value, list) {
  let result = value
  for (let i = 0; i < list.length; i++) {
    let item = list[i]
    if (item.value === value) {
      result = item.label
      break;
    }
  }
  return result
}

export default {
    typeList,
    targetList,
    levelList,
    paymentList,
    statusList,
    convert
}