<template>
	<div class="repo">
		<i-modal :mask-closable="false" @on-cancel="cancelClick" footer-hide title="产品库搜索: 限时优选" v-model="show" width="1000">
			<div class="header mt20 mb30 row item-center justify-center">
				<i-input
					:autofocus="true"
					@on-search="searchTemplateProduct"
					class="mr15"
					enter-button
					placeholder="精确查询(Model Code)"
					search
					v-model="modelCode"
				/>
			</div>
			<div v-if="productList.length>0">
				<div class="mt20">
					<i-table
						:columns="productColumns"
						:data="productList"
						@on-current-change="selectProduct"
						highlight-row
						ref="currentRowTable"
					/>
				</div>
				<div class="row item-center justify-between mt20" v-if="product">
					<i-form :model="product">
						<i-row class="content-area">
							<i-col :span="12">
								<i-form-item class="modal-item" label="名称">
									<i-input :maxlength="10" class="text" placeholder="请输入产品名称" v-model="product.title" />
								</i-form-item>
							</i-col>
							<i-col :span="12">
								<i-form-item class="modal-item" label="描述">
									<i-input
										:autosize="{minRows:3,maxRows:8}"
										:maxlength="255"
										class="text"
										placeholder="请输入产品描述"
										type="textarea"
										v-model="product.description"
									/>
								</i-form-item>
							</i-col>
						</i-row>
						<i-row class="content-area">
							<i-col :span="12">
								<i-form-item class="modal-item" label="库存">
									<i-input class="text mr15" placeholder="燃值兑换库存" v-model="product.stock" />
								</i-form-item>
							</i-col>
							<i-col :span="12">
								<i-form-item class="modal-item" label="价格">
									<i-input class="text mr15" placeholder="燃值兑换价格" v-model="product.points" />
								</i-form-item>
							</i-col>
						</i-row>
					</i-form>
					<div>
						<i-button @click="clearSelected">清除选择</i-button>
					</div>
				</div>
				<div class="row item-center justify-between mt60">
					<div>1. 启用前请确认此商品的店铺零售库存;</div>
					<div>2. 确认后请将燃值库存对应数量的商品转移下架.</div>
				</div>
			</div>
			<div class="mt30 flex row justify-center" v-if="product">
				<i-button @click="cancelClick" class="mr30" shape="circle" size="large" style="width: 100px">取消</i-button>
				<i-button @click="certainClick" shape="circle" size="large" style="width: 100px" type="primary">确认</i-button>
			</div>
		</i-modal>
	</div>
</template>
<script>
import { isPositive } from '@/utils/util'
import { mapGetters } from 'vuex'
export default {
  name: 'batch_onsale_modal',
  props: {
    opFrom: {
      onSale: false,
      type: '',
      batchNumber: '',
      currentSize: ''
    },
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      show: true,
      modelCode: '',
      productList: [],

      productColumns: [
        {
          title: '图片',
          key: 'imageUrls',
          className: 'th-class',
          render: (h, params) => {
            return this.showRowImage(h, params.row.imageUrls)
          }
        },
        {
          title: 'Model Code',
          key: 'modelCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.modelCode)
          }
        },
        {
          title: 'Item Code',
          key: 'itemCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.itemCode)
          }
        },
        {
          title: '名称',
          key: 'title',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.title)
          }
        },
        {
          title: '描述',
          key: 'description',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.description)
          }
        },
        {
          title: '价格',
          key: 'price',
          className: 'th-class',
          fixed: 'right',
          width: 80,
          render: (h, params) => {
            return h('span', params.row.price)
          }
        }
      ],

      product: null
    }
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  methods: {
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'span-pic-box mt5' }, [
        h('img', {
          attrs: {
            src: imageUrls[0],
            class: 'table-img mt5',
            width: '70px',
            height: '70px'
          }
        })
      ])
    },

    // 搜索促销产品/模板库产品
    async searchTemplateProduct() {
      if (this.modelCode) {
        this.productList = []
        let resp = await this.$api.searchModel({
          modelCode: this.modelCode,
          onSale: true
        })
        resp.data.forEach(value => {
          this.productList.push(value)
        })
      } else {
        this.$modal.warning({ title: '提示', content: '请输入Model Code' })
      }
    },

    // 表格行选中事件
    selectProduct(currentRow, oldRow) {
      this.product = currentRow
    },
    // 清除表格行单选
    clearSelected() {
      this.$refs.currentRowTable.clearCurrentRow()
      this.product = null
    },

    // 确定
    async certainClick() {
      let productCreate = {}
      productCreate.productInfo = { ...this.product }
      productCreate['nationalProductId'] = this.product['nationalProductId']
      productCreate['stock'] = this.product['stock']
      productCreate['points'] = this.product['points']
      productCreate['batchNumber'] = this.opFrom.batchNumber
      productCreate['dktStoreNumber'] = this.currentStore.dktStoreNumber
      if (this.opFrom.currentSize >= 5) {
        this.$message.warning({
          content: '当月促销产品最多5个',
          closable: true,
          duration: 3
        })
        return
      }
      if (productCreate.productInfo.title.length > 10) {
        this.$message.warning({
          content: '请控制商品名称在10字以内',
          closable: true,
          duration: 3
        })
        return
      }
      if (!isPositive(productCreate.stock)) {
        this.$message.warning({
          content: '库存必须是正数',
          closable: true,
          duration: 3
        })
        return
      } else if (productCreate.stock > 1500) {
        this.$message.warning({
          content: '库存不得大于1500',
          closable: true,
          duration: 3
        })
        return
      }
      if (!isPositive(productCreate.points)) {
        this.$message.warning({
          content: '兑换燃值必须是正数',
          closable: true,
          duration: 3
        })
        return
      }
      let resp = await this.$api.createOnSaleProduct(
        JSON.stringify(productCreate)
      )
      if (resp['status'] == 'success') {
        this.certain && this.certain()
      }
    },
    // 取消
    cancelClick() {
      this.show = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
  }
}
</script>

<style lang="less" scoped>
.content-area {
  width: 860px;
}
.modal-item {
  width: 420px;
  .text {
    width: 360px;
  }
}
.mt5 {
  margin-top: 5px !important;
}
</style>