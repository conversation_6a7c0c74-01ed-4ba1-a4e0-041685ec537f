<template>
  <div class="login-wrap">
    <div class="left-wrap">
      <img class="logo" src="../assets/login-dkt-logo.png" alt="">
      <div class="welcome-text">欢迎来到</div>
      <div class="welcome-text">迪卡侬会员管理后台</div>
      <div class="login-btn" @click="goLogin">登录</div>
    </div>
    <div class="right-wrap">
      <img class="bg" src="../assets/image/mb-login-bg.png" alt="">
    </div>
  </div>
</template>

<script>
import apiConfig from '@/config/apiConfig'

export default {
  name: 'Login',
  methods: {
    goLogin() {
      window.location.href = apiConfig.LOGIN_PAGE_PATH
    }
  }
}
</script>

<style scope lang="less">
html, body{
  width: 100%; height: 100%;
}
.login-wrap {
  display: flex; flex-direction: row;
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  .left-wrap{
    width: 432px; height: 100%; background: #FFFFFF;
    display: flex; flex-direction: column; align-items: center;
    .logo{
      margin: 124px 0 132px;
      width: 271px; height: 40px;
      display: block;
    }
    .welcome-text{
      width: 290px;
      font-size: 32px; line-height: 48px; color: #101010; font-weight: bold;
      text-align: start;
    }
    .login-btn{
      margin-top: 64px;
      width: 304px; height: 64px;
      background: #3643BA;
      font-size: 24px; line-height: 64px; color: #FFFFFF; text-align: center; font-weight: bold;
    }
  }
  .right-wrap{
    flex:1; height: 100%; 
    .bg{
      width: 100%; height: 100%; object-fit: cover;
    }
  }
}
</style>