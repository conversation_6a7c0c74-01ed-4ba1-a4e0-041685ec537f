# backfrontend

> 描述

````
项目基于vue-cli && iview搭建
````

> 安装

````
安装依赖: npm install 

开发环境: npm run dev

说明文档: npm run doc

unit测试: npm run unit

e2e测试: npm run e2e
````

> branch

````
PP: develop
PR: master
````

> 框架结构

```
.                                  //
├── .DS_Store                      //
├── .babelrc                       // bable配置
├── .editorconfig                  //
├── .gitignore                     //
├── .postcssrc.js                  // postcss配置
├── build                          // webpack等配置
│   ├── build.js                   //
│   ├── check-versions.js          //
│   ├── logo.png                   //
│   ├── utils.js                   //
│   ├── vue-loader.conf.js         //
│   ├── webpack.base.conf.js       //
│   ├── webpack.dev.conf.js        //
│   ├── webpack.prod.conf.js       //
│   └── webpack.test.conf.js       //
├── config                         // 环境配置
│   ├── dev.env.js                 // jsdoc配置
│   ├── doc.conf.js                //
│   ├── index.js                   //
│   ├── prod.env.js                //
│   └── test.env.js                //
├── doc                            // npm run doc生成此文档
├── index.html                     //
├── package-lock.json              //
├── package.json                   // 依赖
├── plugins                        // 插件
│   └── jsdoc-vue.js               //
├── src                            // 项目
│   ├── .DS_Store                  //
│   ├── App.vue                    //
│   ├── api                        // 网络接口
│   │   ├── boutique               //
│   │   │   ├── boutique.js        //
│   │   │   └── index.js           //
│   │   └── index.js               //
│   ├── assets                     // 资源 - webpack不会做压缩等处理
│   │   ├── icon                   //
│   │   │   └── .gitkeep           //
│   │   ├── image                  //
│   │   │   └── .gitkeep           //
│   │   └── logo.png               //
│   ├── components                 // 组件
│   │   └── HelloWorld.vue         //
│   ├── config                     // 项目公共配置
│   │   ├── apiConfig.js           //
│   │   ├── basisConfig.js         //
│   │   ├── iviewConfig.js         //
│   │   ├── localConfig.js         //
│   │   ├── menuConfig.js          //
│   │   └── sessionConfig.js       //
│   ├── css                        // 公共样式
│   │   ├── layout                 // 布局
│   │   │   ├── common.less        //
│   │   │   └── index.less         //
│   │   └── themes                 // 主题
│   │       ├── common.less        //
│   │       └── index.less         //
│   ├── directives                 // 全局指令
│   │   └── index.js               //
│   ├── i18n                       // 国际化
│   │   ├── en-us                  //
│   │   │   └── index.js           //
│   │   ├── index.js               //
│   │   └── zh-cn                  //
│   │       └── index.js           //
│   ├── layouts                    // 主layout
│   ├── main.js                    // 项目入口
│   ├── mixins                     // 混合器
│   │   └── page.js                //
│   ├── mock                       // mock数据
│   ├── pages                      // 页面
│   ├── plugins                    // 插件
│   │   ├── i18n.js                //
│   │   └── vuex.js                //
│   ├── router                     // 路由
│   │   ├── _util.js               //
│   │   ├── autoRoutes.js          //
│   │   ├── index.js               //
│   │   └── staticRoutes.js        //
│   ├── store                      // vuex
│   │   ├── getters.js             //
│   │   ├── index.js               //
│   │   └── modules                //
│   │       └── app.js             //
│   └── utils                      // 公共工具
│       ├── network                // 网络请求封装
│       │   ├── ajax.js            //
│       │   ├── config.js          //
│       │   └── fetch.js           //
│       └── util.js                // 常用工具方法
├── static                         // 静态资源 - webpack不会做压缩等处理
│   └── .gitkeep                   //
└── test                           // 测试
    ├── .DS_Store                  //
    ├── e2e                        // e2e test
    │   ├── custom-assertions      //
    │   │   └── elementCount.js    //
    │   ├── nightwatch.conf.js     //
    │   ├── runner.js              //
    │   └── specs                  //
    │       └── test.js            // 
    └── unit                       // unit test
        ├── .DS_Store              //
        ├── .eslintrc              //
        ├── index.js               //
        ├── karma.conf.js          // karma配置
        └── specs                  //
            └── HelloWorld.spec.j  //
```