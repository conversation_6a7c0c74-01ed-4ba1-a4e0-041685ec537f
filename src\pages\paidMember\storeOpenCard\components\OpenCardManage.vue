<template>
  <div class="manage-wrap">
    <template v-if="hasOpenCardAuth">
      <div class="open-card-wrap">
        <i-row>
          <i-table class="template-table-box text-center" border :columns="templateColumns" :data="templateList" />
        </i-row>
        <TemplateSearchModal :orderNum="orderNum" @getOpenCardList="getOpenCardList" @cancel="hideTemplateModal" v-if="templateModalVisible"></TemplateSearchModal>
      </div>
    </template>
    <div v-else class="no-auth">门店暂未开通该服务</div>
    <EditCardGiftGroup ref="editCardGiftGroup" :cardGiftGroupShow="cardGiftGroupShow" :orderNum="orderNum" @cancel="handleCardGiftCancel" @refreshOpenCardList="refreshOpenCardList"></EditCardGiftGroup>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import TemplateSearchModal from "./TemplateSearchModal.vue"
import EditCardGiftGroup from './EditCardGiftGroup.vue'
import paidMemberApi from '@/api/paidMember/index'
import { isPositive } from '@/utils/util'
import Template from '../../../product/store/profile/template.vue';

export default {
  components: {
		TemplateSearchModal,
    Template,
    EditCardGiftGroup,
	},
  data() {
    return {
	    hasOpenCardAuth: false,
      templateModalVisible: false,
      cardGiftGroupShow: false, // 开卡礼单组 编辑弹窗
      orderNum: '',
			templateColumns: [
        { title: '序号', key: 'order_number', className: 'th-class pro-img-box'},
        { title: '图片', width: 100, key: 'imageUrls', className: 'th-class pro-img-box', render: (h, params) => { return this.showRowImage(h, params.row.attributes.image_urls, params.row.renewal_excluded) }},
        // { title: 'Model Code', key: 'model_code', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.model_code) }},
        // { title: 'Item Code', key: 'item_code', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.item_code) }},
        { title: 'Model 数量', key: 'model_code_count', className: 'th-class', render: (h, params) => { return h('span', params.row.model_code_count) }},
        { title: 'Item 数量', key: 'item_code_count', className: 'th-class', render: (h, params) => { return h('span', params.row.item_code_count) }},
        { title: '名称', key: 'title', className: 'th-class', render: (h, params) => { return h('span', (params.row.attributes && params.row.attributes.title ) || '暂未添加商品') }},
        { title: '简称', key: 'short_name', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.short_name) }},
        { title: '描述', width: 160, key: 'description', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.description ) }},
        { title: '价格', key: 'price', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.price ) }},
        { title: '剩余库存',width:60, key: 'remaining_stock', className: 'th-class', render: (h, params) => { return h('span', params.row.remaining_stock ) }},
        { title: '操作',  key: 'action', className: 'th-class', fixed: 'right', width: 190, render: (h, params) => { return this.createOpBtn(h, params.row) }}
      ],
			templateList: [
			],
      // 表格操作按钮
      opBtnList: [
        { code: 'edit', text: '编辑' },
        { code: 'disable', text: '下架', type: 'warning' }
      ],
    };
  },
  created(){
    this.currentStore.dktStoreNumber && this.getOpenCardList();
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  watch: {
    // 监控门店切换, 刷新数据
    currentStore(newVal, oldVal) {
      let isChanged = newVal.dktStoreNumber !== oldVal.dktStoreNumber;
      this.currentStore.dktStoreNumber && isChanged && this.getOpenCardList();
    }
  },
  methods: {
    // 渲染表格图片
    showRowImage(h, imageUrls, renewal_excluded) {
      let generateBtn = [
        h('img', {
          attrs: {
            src: imageUrls[0],
            class: 'table-img mt5',
            width: '45px',
            height: '45px'
          }
        })
      ];
      let cla = 'tables-pic-box ';
      if( renewal_excluded ) {
        generateBtn.push(
          h('div', {
            attrs: {
              class: 'renewal-tag',
            }
          })
        )
        cla = cla + 'tag-wrap'
      }
      return h('div', { class: cla }, generateBtn)
    },
    refreshOpenCardList() {
      this.getOpenCardList();
    },
    async getOpenCardList() {
      this.templateList = [];
      let params = {
        store_number: this.currentStore.dktStoreNumber,
      }
      let resp = await paidMemberApi.getOpenCardManageList(params);
      this.templateList = resp.data;
      let hadOpenCardAuth = resp && resp.code && resp.code !== '4001';
      this.hasOpenCardAuth = hadOpenCardAuth;
    },
    returnDefaultTitle(h, row) {
      let operations = []
      let text = row.catalogue_id ? row.attributes.title : '暂未添加商品'
      operations.push(
        h('span', text)
      )
      return h('div', operations)
    },
    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      //  row.catalogue_id 判断当前行是否有添加商品
      if(row.catalogue_id) {
        this.opBtnList.forEach((value) => {
          operations.push(
            h('i-button', { 
              class: 'ml5 mr5', 
              props: { type: value.type },
              on: { click: () => { 
                this.orderNum = row.order_number;
                this.rowOperation(value.code, row) 
              } } 
              },
              value.text)
          )
        })
      } else {
        operations.push(
          h('i-button', { 
            class: 'ml5 mr5', 
            on: { click: () => { 
              this.orderNum = row.order_number;
              this.showTemplateSearchModal();
            } } 
            },
            '从模板库添加商品')
        )
      }
      return h('div', operations)
    },
    showTemplateSearchModal() {
      this.templateModalVisible = true;
    },
    hideTemplateModal() {
      this.templateModalVisible = false;
    },
    // 表格项操作
    rowOperation(code, row) {
      switch(code) {
        case 'disable':
          this.$modal.confirm({
					  'title': '商品下架',
					  'content': `您即将下架该商品的所有item，请确认？`,
					  'okText': '确定',
					  'cancelText': '取消',
					  'onOk': () => {
						  this.removeGiftSlotItem(row)
					  }
          })
          break
        case 'edit':
          // 调用接口 查询
          this.cardGiftGroupShow = true;
          // 查询当前行的开卡礼卡槽产品
          this.$refs.editCardGiftGroup.getProductList({
            orderNum: row.order_number,
            needInitList: true
          });
          break
      }
    },
    handleCardGiftCancel() {
      this.cardGiftGroupShow = false;
    },
    cancelChangeStock() {
      this.stock = '';
    },
    // 修改库存
    async saveBatchProduct(row) {
      let params = {
        catalogue_id: row.catalogue_id,
        stock: this.stock,
        store_number: this.currentStore.dktStoreNumber,
      }
      // 库存校验逻辑
      if(this.stock < 0){
        this.$message.warning({
          content: '库存数量不得小于0',
          closable: true,
          duration: 3
        })
        this.stock = '';
        return
      } else if (!isPositive(this.stock) && this.stock !== '0') {
        this.$message.warning({
          content: '库存必须是正数',
          closable: true,
          duration: 3
        })
        this.stock = '';
        return
      } else if (this.stock > 3000) {
        this.$message.warning({
          content: '库存不可超过3000',
          closable: true,
          duration: 3
        })
        this.stock = '';
        return
      }

      let resp = await paidMemberApi.updateProductStock(params);
      if(resp.status === 'success') {
        this.$message.success({
          content: '库存修改成功!',
          closable: true,
          duration: 3
        })
        this.stock = '';
        this.getOpenCardList();
      }
    },
    // 下架当前开卡礼卡槽
    async removeGiftSlotItem(row) {
      let params = {
        benefit_type_id: 8,
        catalogue_status: 'HALTSALE',
        store_number: this.currentStore.dktStoreNumber,
        order_number: row.order_number,
      }
      let resp = await paidMemberApi.removeOpenCardGift(params);
      if (resp.status === 'success') {
        this.$message.success({
          content: '下架成功!',
          closable: true,
          duration: 3
        })
        this.getOpenCardList();
      }
    },
  }
};
</script>

<style lang="less" scoped>
	.manage-wrap{
		height: 100%;
		.no-auth{
			width: 100%; height: 100%; background:#FFFFFF; line-height: 300px; text-align: center;
		}
	}
  .template-table-box{
    /deep/.ivu-table-cell{
      display: flex;
      justify-content: center;
      padding: 10px;
    }
    /deep/.tables-pic-box{
      position: relative;
      width: 45px;
      height: 45px;
      &.tag-wrap{
        box-sizing: content-box;
        padding: 20px 27px 0 0;
      }
      .renewal-tag{
        position: absolute; right: 0; top: 0;
        width: 39px; height: 39px; 
        background: url('../../../../assets/renewtag.png');
        background-size: 39px 39px;
      }
    }
  }

</style>