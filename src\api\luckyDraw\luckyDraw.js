import fetch from '@/utils/network/fetch'

// 获取所有订单
const luckyDrawList = params => {
  return fetch(`/api/v2/orders?pageNo=${params.no}&pageSize=${params.size}&status=${params.status}`)
}

// // 更新订单
const updateOrder = (params = {}) => {
  return fetch(`/api/v2/orders/${params.id}`, { method: 'put', data: JSON.stringify(params) })
}
// 获取订单详情
const luckyDrawDetail = id => {
  return fetch(`/api/v2/orders/${id}`)
}
export default {
  luckyDrawList,
  updateOrder,
  luckyDrawDetail
}
