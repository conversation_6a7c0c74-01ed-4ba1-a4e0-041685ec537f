<template>
	<i-row class="order-detail">
		<i-row class="row item-center">
			<i-col :span="12">
				<h3>订单信息</h3>
			</i-col>
		</i-row>
		<i-row class="mt10 mb20">
			<i-col class="top-info name">
				<label>姓名：</label>
				<span>{{orderDetail.name || '--'}}</span>
			</i-col>
			<i-col class="top-info">
				<label>手机号：</label>
				<span>{{orderDetail.mobile || '--'}}</span>
			</i-col>
			<i-col class="top-info">
				<label>会员卡号：</label>
				<span>{{orderDetail.card_number || '--'}}</span>
			</i-col>
			<i-col class="top-info city">
				<label>开卡城市：</label>
				<span>{{ orderDetail.city || '--' }}</span>
			</i-col>
      <i-col class="top-info">
				<label>开卡门店：</label>
				<span>{{orderDetail.store_number || '--'}}</span>
			</i-col>
		</i-row>
		<i-row class="mb20" >
      <!-- <i-col class="mt10">
				<label>支付金额：</label>
				<span>¥{{orderDetail.price || ''}}</span>
			</i-col> -->
			<i-col class="mt10" v-for="item in curStatusBar">
				<label>{{item.text}}：</label>
				<span>{{(item.params && orderDetail[item.params]) || '--'}}</span>
			</i-col>
		</i-row>
		
    <i-row>
			<h3 class="mt20">权益信息</h3>
		</i-row>
    <i-row class="mt10 mb20" >
			<i-col class="mt10">
				<label>开卡礼状态：</label>
				<span>{{openCardStatus[orderDetail.product_status] || '--'}}</span>
			</i-col>
      <i-col class="mt10">
				<label>特权礼券领取数量：</label>
				<span>{{orderDetail.coupon_exchange_num || '--'}}</span>
			</i-col>
      <i-col class="mt10">
				<label>活动参与次数：</label>
				<span>{{orderDetail.activity_num || '--'}}</span>
			</i-col>
      <i-col class="mt10">
				<label>拼图任务完成次数：</label>
				<span>{{orderDetail.task_num || '--'}}</span>
			</i-col>
      <i-col class="mt10">
				<label>自行车安全检查：</label>
				<span>{{orderDetail.bike_check ? '已预约' : '未预约'}}</span>
			</i-col>
      <i-col class="mt10">
				<label>羽毛球穿线服务：</label>
				<span>{{orderDetail.racket_stringing ? '已预约' : '未预约'}}</span>
			</i-col>
		</i-row>
		<i-row class="bottom-btn">
      <i-button type="primary" class="refund-btn" v-has="['PAID_MEMBER_ORDER_REFUND']" v-if="orderDetail.status && orderDetail.status === 'PAID'" @click="showRefundModal" >退卡并退款</i-button>
      <i-button type="primary" class="refund-btn btn-disabled" v-has="['PAID_MEMBER_ORDER_REFUND']" v-if="orderDetail.status && orderDetail.status === 'REFUNDED'" >已发起退款</i-button>
      <i-button class="return-btn" @click="goBack" >返回</i-button>
		</i-row>
	</i-row>
</template>

<script>
import paidMemberApi from '@/api/paidMember/index'

export default {
  name: 'orderDetail',

  data() {
    return {
      statusType: {
        PAID: '已支付',
        REFUNDED: '已退款',
        EXPIRED: '已过期',
      },
      orderDetail: {},
      curStatusBar: [],
      orderStatusBar: {
        //已到账
        REFUND_SUCCESS: [
          { text: '订单状态', params: 'orderFormatStatus' },
          { text: '支付金额', params: 'formatPrice' },
          { text: '支付时间', params: 'purchase_time' },
          { text: '发起退卡时间', params: 'refund_time' },
          { text: '退款到账时间', params: 'refund_valid_time' },
        ], 
        //暂未到账
        REFUND_WAITING: [
          { text: '订单状态', params: 'orderFormatStatus' },
          { text: '支付金额', params: 'formatPrice' },
          { text: '支付时间', params: 'purchase_time' },
          { text: '发起退卡时间', params: 'refund_time' },
        ], 
        //成功支付
        PAID_SUCCESS: [
          { text: '订单状态', params: 'orderFormatStatus' },
          { text: '支付金额', params: 'formatPrice' },
          { text: '支付时间', params: 'purchase_time' },
        ], 
        //退款失败
        REFUND_ERROR: [
          { text: '订单状态', params: 'orderFormatStatus' },
          { text: '支付金额', params: 'formatPrice' },
          { text: '支付时间', params: 'purchase_time' },
          { text: '发起退卡时间', params: 'refund_time' },
        ], 
        //人工操作
        MANUAL_REFUND_BY_STORE: [
          { text: '订单状态', params: 'orderFormatStatus' },
          { text: '支付金额', params: 'formatPrice' },
          { text: '支付时间', params: 'purchase_time' },
        ],
        //已过期
        EXPIRED: [
          { text: '订单状态', params: 'orderFormatStatus' },
          { text: '支付金额', params: 'formatPrice' },
          { text: '支付时间', params: 'purchase_time' },
          { text: '过期时间', params: 'expire_time' },
        ], 
      },
      // 开卡礼状态 取product_status
      openCardStatus: {
        EFFECTIVE: '待兑换',
        VALID: '待核销',
        RECEIVED: '已核销',
        CONFIRMED: '已核销',
        INVALID: '已过期',
        CANCELLED: '已取消'
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.getOrderDetail()
    },
    async getOrderDetail() {
      const id = this.$route.params.id
      let params = {
        paid_member_id: id
      }
      let resp = await paidMemberApi.getOrderDetail(params);
      if (resp.status === 'success') {
        this.orderDetail = resp.data;
        let { order_status = 'REFUND_SUCCESS', status, order_message, price } = this.orderDetail;
        let suffixStatus = order_message ? '/'+order_message : '';
        this.orderDetail.orderFormatStatus = this.statusType[status] + suffixStatus;
        let fixPrice =  (price == 0 || price) ? price.toFixed(1) : '';
        this.orderDetail.formatPrice =  '¥' + fixPrice;
        this.curStatusBar = this.orderStatusBar[order_status];
      }
    },
    goBack() {
      this.$route.params.lastSearch = 1;
      this.$router.back();
    },
    showRefundModal() {
      this.$modal.confirm({
        'title': '确认退卡并退款？',
        'content': `确定后将终止当前用户的会员身份 <br/>并发起退款`,
        'okText': '确定',
        'cancelText': '取消',
        'onOk': () => {
          this.goRefundRequest()
        }
      })
    },
    async goRefundRequest() {
      const { campaign_id, card_number, external_id, paid_member_id, store_number } = this.orderDetail;
      
      let params = {
        campaign_id,
        card_number,
        external_id,
        paid_member_id,
        store_number 
      }
      let resp = await paidMemberApi.handleOrderRefund(params);
      if (resp.status === 'success') {
        this.getOrderDetail()
      }
    },
  }
}
</script>

<style lang="less" scoped>
.order-detail {
  height: 100%;
  .btn-disabled{
    opacity: 0.38;
    cursor: not-allowed;
  }
  .top-info{
    width: 200px; float: left;
    &.name{ width: 240px; }
    &.city, &.store{ width: 120px; }
  }
  .bottom-btn{
    width: 100%;
    padding-top: 24px; 
    position: absolute; bottom: 0; height: 80px;
    .refund-btn{
      margin-right: 20px;
    }
    .return-btn{
      width: 92px;
    }
  }

  span {
    color: #515a6e;
  }
  
}

</style>