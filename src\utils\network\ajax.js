/**
 * axios实例
 **/

import axios from 'axios'
import config from './config.js'
import comConfig from '@/config/apiConfig.js'
import { local, session } from '../util.js'
import router from '@/router'
import apiConfig from '@/config/apiConfig'

// 创建实例
const ajax = axios.create(config)

/* 请求拦截器 */
// 请求之前的操作
ajax.interceptors.request.use(
  config => {
    // 设置请求头(区分环境)
    if (process.env.NODE_ENV === 'development') {
      // config.headers['Authorization'] = 'Basic bWVtYmVyc2hpcDpwYXNz'
      config.headers['Authorization'] = local.getStorage('TOKEN')
    } else {
      config.headers['Authorization'] = local.getStorage('TOKEN')
    }

    if (config.requestBaseUrl) {
      config.baseURL = comConfig[config.requestBaseUrl];
      if(config.requestBaseUrl === 'AZ_HOST') {
        config.headers['x-api-key'] = comConfig.X_API_KEY[config.requestBaseUrl]
      }
    }

    return config
  },
  /*错误操作*/
  err => {
    return Promise.reject(err)
  }
)

// 请求之后的操作
ajax.interceptors.response.use(
  res => {
    // 如果 response 中存在token
    let expiration = 2 * 60 - 1
    if (res.headers.authorization) {
      local.setStorage('TOKEN', res.headers.authorization, expiration)
    }
    if (res.headers.Authorization) {
      local.setStorage('TOKEN', res.headers.Authorization, expiration)
    }
    return res
  },
  err => {
    let code = err.response.status
    if (code === 401 || code === 403) {
      // 控制用户可以刷新2次
      let totalRefreshTimes = 2
      let tokenRefreshTimes = session.getStorage('TOKEN_REFRESH_TIMES') || 0
      if (tokenRefreshTimes < totalRefreshTimes) {
        tokenRefreshTimes += 1
        session.setStorage('TOKEN_REFRESH_TIMES', tokenRefreshTimes)
        // 跳转到三方登录页面
        // window.location.href = apiConfig.LOGIN_PAGE_PATH
        // 新的登录页面
        router.replace({ path: '/login' })

      } else {
        // 跳转ERROR页面
        router.replace({ path: '/error' })
      }
    }
    return Promise.reject(err)
  }
)

export default ajax
