<template>
  <div class="template_repo template-repo-box">
    <i-row class="mt20 mb30">
      <i-col span="22">
        <i-col span="6" class="mr30 text-center">
          <i-input v-model="searchForm.item_code" class="" :autofocus="true" search placeholder="精确查询(Item Code)" clearable />
        </i-col>
        <i-col span="4" class="mr10" style="margin-left:-20px">
          <i-select v-model="searchForm.is_delete" placeholder="产品状态" clearable>
            <i-option v-for="l in typeList" :key="l.val" :value="l.val" >{{l.label}}</i-option>
          </i-select>
        </i-col>
        <i-col span="5">
          <i-date-picker :open="open" :type="type" @on-change="changePicker" @on-clear="closePicker" @on-ok="closePicker" :format="format" style="margin-top:-1px">
            <i-input v-model="displayDate" placeholder="发布时间" class="text mr15" clearable @on-focus="openPicker" @on-blur="closePicker">
              <i-select v-model="type" slot="prepend" style="width:50px" @on-change="changeType">
                <i-option value="month">月</i-option>
                <i-option value="date">日</i-option>
              </i-select>
            </i-input>
          </i-date-picker>
        </i-col>
        <i-col span="6">
          <i-button class="mr15" @click="searchClick">查询</i-button>
          <i-button class="mr15" @click="resetClick">重置</i-button>
        </i-col>
      </i-col>
      <i-col span="2" class="text-right">
        <i-button type="primary" icon="md-add" @click="popupNationalCreate" >添加</i-button>
      </i-col>
    </i-row>

    <i-row>
      <i-table class="text-center" border :columns="templateColumns" :data="templateList" />
    </i-row>
    <i-row class="mt15">
      <i-col class="text-right">
        <i-page :total="page.total" show-total 
          :current="page.no"
          :page-size="page.size" show-sizer 
          :page-size-opts="pageSizeOpts"
          @on-change="changePageNo" @on-page-size-change="changePageSize" />
      </i-col>
    </i-row>
    <!-- 添加弹窗 -->
    <t-modal v-if="showDialog" :title="title" :certain="modalCertain" :cancel="modalCancel"/>
    <!-- 编辑弹窗 -->
    <e-modal v-if="showEditDialog" :certain="modalCertain" :cancel="modalCancel" :id="id" :from="from"/>
  </div>
</template>

<script>
import paidMemberApi from '@/api/paidMember/index'
import cfg from '../config'
import TemplateModal from './profile/modal'
import EditProductModal from '../edit'
import { pageSizeOpts, checkPermissions, checkFill, judgeFill } from '@/utils/util'

export default {
  name: 'template_repo',
  components: {
    't-modal': TemplateModal,
    'e-modal': EditProductModal
  },
  data() {
    return {
      levelList: cfg.levelList.filter((l) => l.code != '0'),
      typeList: [
        { label: '全部', val: '' },
        { label: '已发布', val: 'false' },
        { label: '已禁用', val: 'true' },
      ],
      // 分页相关配置
      pageSizeOpts: pageSizeOpts,
      page: {
        no: 1,
        size: 10,
        total: 0
      },
      btnClickable: true,
      type: 'month',
      format: 'yyyy-MM',
      open: false,
      displayDate: '',
      searchForm: {
        is_delete: '',       // 产品类型
        item_code: '',
        type_id: 8,
        page_num: 1,
        page_size: 10,
        // title: '',
        publish_month: '',
        publish_date: ''
      },
      searchBackup: {},
      templateList: [],     // 产品模板列表
      
      title: '',            // 对话框标题
      showDialog: false,

      // 表格操作按钮
      opBtnList: [
        { code: 'edit', text: '编辑'},
      ],
      disableBtn: { code: 'disable', text: '禁用', type: 'warning' },
      enableBtn:  { code: 'enable', text: '取消禁用', type: 'warning' },
      templateColumns: [
        { title: '模板id', key: 'template_id', className: 'th-class pro-img-box'},
        { title: '图片', key: 'image_urls', className: 'th-class pro-img-box', render: (h, params) => { return this.showRowImage(h, params.row.attributes.image_urls) }},
        { title: 'Dsm Code', key: 'dsm_code', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.dsm_code) }},
        { title: 'Model Code', key: 'model_code', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.model_code) }},
        { title: 'Item Code', key: 'item_code', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.item_code) }},
        { title: '名称', key: 'title', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.title) }},
        { title: '简称', key: 'short_name', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.short_name) }},
        { title: '颜色', key: 'color', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.color) }},
        { title: '尺码', key: 'size', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.size) }},
        { title: '描述', key: 'description', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.description) }},
        // { title: '类型', key: 'type', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.type, cfg.levelList)) }},
        { title: '燃值', key: 'point_value', className: 'th-class'},
        { title: '价格', key: 'price', className: 'th-class',  render: (h, params) => { return h('span', params.row.attributes.price) }},
        { title: '状态', key: 'is_delete', className: 'th-class', render: (h, params) => { return this.getStatusText(h, params.row.is_delete)  }},
        { title: '操作',  key: 'action', className: 'th-class', fixed: 'right', width: 190, render: (h, params) => { return this.createOpBtn(h, params.row) }}
      ],

      from: '',
      showEditDialog: false,
      id: '',
    }
  },
  created() {
    this.searchBackup = JSON.parse(JSON.stringify(this.searchForm));
    this.searchNationalProduct()
  },
  mounted() {
    this.checkBtnClickable();
  },
  methods: {
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'table-pic-box' }, [h('img', { attrs: { src: imageUrls[0], class: 'table-img proImg' } })])
    },
    // 获取状态
    getStatusText(h, status) {
      return h('span', status ? '已禁用' : '已发布' )
    },

    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      let newBtnList = [];
      newBtnList = this.opBtnList.concat( row.is_delete ? this.enableBtn : this.disableBtn) 
      newBtnList.forEach((value) => {
        operations.push(
          h('i-button', { 
            class: 'ml5 mr5', 
            props: { type: value.type },
            directives: [
              {
                name: 'has',
                value: value.permissions
              } 
            ],
            on: { click: () => { this.rowOperation(value.code, row) } } },
            value.text)
        )
      })
      return h('div', operations)
    },
    openPicker() {
      this.open = true
    },
    changeType(type) {
      switch(this.type) {
        case 'month':
          this.format = 'yyyy-MM'
          break
        case 'date':
          this.format = 'yyyy-MM-dd'
          break
      }
      this.displayDate = ''
    },
    changePicker(date) {
      this.displayDate = date
      switch(this.type) {
        case 'month':
          this.searchForm.publish_month = date
          this.searchForm.publish_date = ''
          break
        case 'date':
          this.searchForm.publish_month = ''
          this.searchForm.publish_date = date
          break
      }
      this.open = false
    },
    // 关闭日期选择
    closePicker() {
      this.open = false
    },
    // 查询
    searchClick() {
      this.page.no = 1;
      this.searchNationalProduct()
    },
    // 重置搜索条件
    resetClick() {
      this.searchForm = JSON.parse(JSON.stringify(this.searchBackup));
      this.displayDate = ''
      this.searchNationalProduct()
    },
    // 搜索模板库产品
    async searchNationalProduct() {
       this.searchForm['page_num'] = this.page.no
      this.searchForm['page_size'] = this.page.size
      let resp = await paidMemberApi.getTplManageList(this.searchForm);
      if (resp && resp.status === 'success') {
        this.templateList = resp.data.items
        this.page.total = resp.data.total
      }
    },

    // 分页操作
    changePageNo(no) {
      this.page.no = no
      this.searchNationalProduct()
    },
    changePageSize(size) {
      this.page.size = size
      this.searchNationalProduct()
    },

    // 表格项操作
    rowOperation(code, row) {
      switch(code) {
        case 'disable':
          if( !this.btnClickable ) {
            this.$message.warning({ content: '暂无操作权限', closable: true, duration: 3 })
          } else {
            this.$modal.confirm({
              'title': '模板库产品禁用',
              // 'content': `您即将禁用模板库产品<span class='confirm-msg'>${row.productInfo.title}</span>`,
              'content': `确定禁用该商品吗？`,
              'okText': '确定',
              'cancelText': '取消',
              'onOk': () => {
                this.disableNationalProduct(row.template_id)
              }
            })
          }
          break
        case 'enable':
          if( !this.btnClickable ) {
            this.$message.warning({ content: '暂无操作权限', closable: true, duration: 3 })
          } else {
            this.$modal.confirm({
              'title': '模板库产品启用',
              // 'content': `您即将禁用模板库产品<span class='confirm-msg'>${row.productInfo.title}</span>`,
              'content': `确定启用该商品吗？`,
              'okText': '确定',
              'cancelText': '取消',
              'onOk': () => {
                this.enableNationalProduct(row.template_id)
              }
            })
          }
          break
        case 'edit':
          if( !this.btnClickable ) {
            this.$message.warning({ content: '暂无操作权限', closable: true, duration: 3 })
          } else {
            this.editProduct(row.template_id)
          }
          break
      }
    },

    // 禁用模板库产品
    async disableNationalProduct(id) {
      await paidMemberApi.disableModal(id)
      this.searchNationalProduct()
    },
    // 启用模板库产品
    async enableNationalProduct(id) {
      await paidMemberApi.enableModal(id, {is_delete: false})
      this.searchNationalProduct()
    },
    checkBtnClickable() {
      let unClickableAuth = ['PAID_MEMBER_PRODUCT_TEMPLATE_VIEW'];
      let hasUnClickAuth = checkPermissions(unClickableAuth);
      this.btnClickable = !hasUnClickAuth;
    },
    // 添加模版库产品
    popupNationalCreate() {
      if( !this.btnClickable ) {
        this.$message.warning({ content: '暂无操作权限', closable: true, duration: 3 })
        return;
      }
      this.title = '添加模版库产品'
      this.showDialog = true
    },
    // 编辑模版库产品
    editProduct(id) {
      this.showEditDialog = true
      this.from = 'template'
      this.id = id
    },
    modalCertain() {
      this.closeDialog()
      this.searchNationalProduct()
    },
    modalCancel() {
      this.closeDialog()
    },
    closeDialog() {
      if (this.id) {
        this.showEditDialog = false
      } else {
        this.showDialog = false
      }
      this.id = ''
    }
  }
}
</script>

<style lang="less">
.template-repo-box {
  .pro-img-box {
    .table-pic-box {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      padding:10px 0;
      height: auto;
      overflow: hidden;
      text-align: center;
      box-sizing: border-box;
      .proImg {
        width: 2.5vw!important;
        height: 2.5vw!important;
      }
    }
  }
}
.ml5 {
  margin-left: 5px;
}
.mr5 {
  margin-right: 5px;
}
</style>
