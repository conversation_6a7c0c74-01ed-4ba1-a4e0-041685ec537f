<template>
  <div class="container">
    <i-modal v-model="show" :closable="false" :title="title" :width="showEditTable ? 900 : 500">
      <!-- Excel上传区域 -->
      <template v-if="!showEditTable">
        <i-upload
          class="drag-upload-wrap"
          :before-upload="beforeUpload"
          action=""
          name="file"
          type="drag"
          accept=".xlsx,.xls"
        >
          <div style="padding: 30px">
            <i-icon size="52" type="ios-cloud-upload" style="color: #3399ff" />
            <p>点击或拖动Excel文件在这里上传</p>
            <p>{{ uploadFile ? uploadFile.name : "" }}</p>
          </div>
        </i-upload>
        <div class="upload-wrap">
          <i-button type="primary" class="upload-btn" @click="handleUpload">上传</i-button>
        </div>
      </template>

      <!-- 数据编辑表格 -->
      <template v-else>
        <div class="table-container">
          <i-table :columns="columns" :data="editableData" border>
            <template v-slot:person_id="{ row }">
              <i-input :value="row.person_id" @input="(value) => handleInputChange(row, 'person_id', value)" placeholder="请输入"></i-input>
            </template>
            <template v-slot:card_number="{ row }">
              <i-input :value="row.card_number" @input="(value) => handleInputChange(row, 'card_number', value)" placeholder="请输入"></i-input>
            </template>
            <template v-slot:mobile="{ row }">
              <i-input :value="row.mobile" @input="(value) => handleInputChange(row, 'mobile', value)" placeholder="请输入"></i-input>
            </template>
            <template v-slot:reason="{ row }">
                <i-input :value="row.reason" :maxlength="200" @input="(value) => handleInputChange(row, 'reason', value)" placeholder="请输入"></i-input>
            </template>
          </i-table>
        </div>
        <div class="submit-wrap">
          <i-button type="primary" @click="handleSubmit">提交</i-button>
        </div>
      </template>
    </i-modal>
  </div>
</template>

<script>
export default {
  name: 'UploadBatchModal',
  props: {
    title: {
      type: String,
      default: '批量导入'
    },
    show: {
      type: Boolean,
      default: false
    },
    bizTip: {
      type: String,
      default: ''
    },
    status: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      uploadFile: null,
      showEditTable: false,
      columns: [
        {
          title: 'Person ID',
          key: 'person_id',
          slot: 'person_id',
          width: 200
        },
        {
          title: '卡号',
          key: 'card_number',
          slot: 'card_number',
          width: 200
        },
        {
          title: '手机号',
          key: 'mobile',
          slot: 'mobile',
          width: 200
        },
        {
          title: '原因',
          key: 'reason',
          slot: 'reason'
        }
      ],
      editableData: []
    }
  },
  methods: {
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.ms-excel' ||
                      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      if (!isExcel) {
        this.$message.error('只能上传Excel文件！');
        return false;
      }
      this.uploadFile = file;
      return false;
    },

    handleFormData() {
      const formData = new FormData();
      formData.append('file', this.uploadFile, this.uploadFile.name);
      return formData;
    },

    async handleUpload() {
      if (!this.uploadFile) {
        this.$message.warning('请先选择上传文件！');
        return;
      }
      try {
        const objc = {
          isForm: true,
          method: 'post',
          data: this.handleFormData()
        }
        let resp;
        try {
          resp = await this.$api.postTableQuery(objc);
        } catch (apiError) {
          this.$message.error('上传失败：服务器错误');
          return;
        }
        if (!resp) {
          this.$message.error('上传失败：未收到服务器响应');
          return;
        }
        if (resp.data) {
          this.$message.success('解析成功！');
          this.editableData = Array.isArray(resp.data) ? resp.data : [resp.data];
          this.showEditTable = true;
        } else {
          this.$Message.error('上传失败：数据格式错误');
        }
      } catch (error) {
        console.error('整体处理错误:', error)
        this.$message.error('上传处理失败，请重试');
      }
    },

    async handleSubmit() {
      const invalidRow = this.editableData.find(row => !row.reason || row.reason.trim() === '');
      if (invalidRow) {
        this.$message.error('请填写所有【原因】字段');
        return;
      }

      try {
        const resp = await this.$api.postTableData(this.editableData, this.bizTip, this.status);
        this.$message.success('提交成功！');
        this.$emit('success', this.editableData);
        this.resetAndClose();
      } catch (error) {
        console.error('Submit error:', error);
        this.$message.error('提交失败');
      }
    },

    resetAndClose() {
      this.uploadFile = null;
      this.showEditTable = false;
      this.editableData = [];
      this.$emit('update:show', false);
      this.$emit('cancel');
    },

    handleInputChange(row, field, value) {
      const index = this.editableData.findIndex(item => item.person_id === row.person_id);
      if (index > -1) {
        const updatedRow = { ...row, [field]: value };
        this.editableData.splice(index, 1, updatedRow);
        // console.log('当前所有数据:', JSON.stringify(this.editableData));
      }
    }
  },
  watch: {
    show(val) {
      if (!val) {
        this.resetAndClose();
      }
    }
  }
}
</script>

<style lang="less" scoped>
.drag-upload-wrap {
  width: 400px;
  margin: 0 auto;
  text-align: center;
}

.upload-wrap {
  margin-top: 20px;
  text-align: right;
  padding-right: 20px;

  .upload-btn {
    width: 75px;
  }
}

.table-container {
  margin: 16px 0;
  /deep/ .ivu-table-wrapper {
    overflow: visible;
  }
  /deep/ .ivu-table {
    &:before, &:after {
      display: none;
    }
  }
}

.submit-wrap {
  margin-top: 20px;
  text-align: right;
  padding-right: 20px;
}
</style>
