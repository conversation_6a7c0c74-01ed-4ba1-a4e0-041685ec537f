<template>
  <div class="container">
    <i-modal
      :closable="false"
      :mask-closable="false"
      :title="title"
      footer-hide
      v-model="show"
      width="1030"
    >
      <div class="modal-content mt30 flex column">
        <!-- 成员列表部分 -->
        <div class="section-container">
          <div class="section-title">{{ viewStatus ? '黑名单列表' : '白名单列表' }}</div>
          <div class="filter-box">
            <div class="filter-box-left">
              <i-select v-model="searchType" style="width: 100px; margin-right: 10px;">
                <i-option label="手机" value="mobile"></i-option>
                <i-option label="卡号" value="cardNo"></i-option>
                <i-option label="Person ID" value="personId"></i-option>
              </i-select>
              <i-input v-model="searchQuery" placeholder="手机/卡号/person ID" style="width: 200px" />
              <div class="action-buttons">
                <i-button type="primary" @click="handleSearch">搜索</i-button>
                <i-button type="primary" @click="handleReset">重置</i-button>
              </div>
            </div>
            <div class="action-buttons">
              <i-button v-if="type !== 'view'" type="primary" @click="handleAdd">添加</i-button>
              <i-button v-if="type !== 'view'" type="primary" @click="handleImport">导入</i-button>
            </div>
          </div>
          <div class="table-container">
            <i-table :columns="memberColumns" :data="memberList" border></i-table>
            <div class="pagination-container">
              <i-page
                :total="totalMembers"
                :current="currentPage"
                :page-size="pageSize"
                :page-size-opts="[10, 20, 30, 50]"
                show-total
                show-sizer
                @on-change="handlePageChange"
                @on-page-size-change="handlePageSizeChange"
              />
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <i-button @click="handleCancel" class="mr30">关闭</i-button>
        </div>
      </div>
    </i-modal>

    <!-- 移除确认弹窗 -->
    <i-modal v-model="removeModal" :closable="false" title="确认">
      <p>是否移出名单?</p>
      <div slot="footer">
        <i-button @click="removeModal = false">取消</i-button>
        <i-button type="primary" @click="confirmRemove">确定</i-button>
      </div>
    </i-modal>

    <!-- Excel上传弹窗 -->
    <upload-batch-modal
      :show.sync="uploadModal"
      title="批量导入"
      :bizTip="biz_type"
      :status="viewStatus"
      @cancel="handleUploadCancel"
      @success="handleUploadSuccess"
    />

    <!-- 添加成员弹窗 -->
    <i-modal v-model="addModal" :closable="false" title="添加成员">
      <div class="add-form">
        <i-select v-model="addForm.type" style="width: 100px; margin-bottom: 10px;">
          <i-option label="手机" value="mobile"></i-option>
          <i-option label="卡号" value="cardNo"></i-option>
          <i-option label="Person ID" value="personId"></i-option>
        </i-select>

        <i-input v-model="addForm.identifier" placeholder="请输入您选择的（手机/卡号/person ID）"></i-input>
        <div style="position: relative;">
          <i-input
            v-model="addForm.reason"
            :maxlength="200"
            placeholder="原因（必填）"
            class="mt10"
          />
          <span style="position: absolute; bottom: -5px; right: 10px; color: #999; font-size: 12px;">
            {{ addForm.reason.length }} / 200
          </span>
        </div>
      </div>
      <div slot="footer">
        <i-button @click="addModal = false">取消</i-button>
        <i-button type="primary" @click="confirmAdd">确定</i-button>
      </div>
    </i-modal>
  </div>
</template>

<script>
import UploadBatchModal from "./uploadBatchModal.vue";

export default {
  name: "ViewNamelist",
  components: {
    UploadBatchModal
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    biz_type: { // 从 modelModal 传递过来的模型 ID
      type: String,
      default: ''
    },
    viewStatus: { // true: 黑名单, false: 白名单
      type: Boolean,
      default: false
    },
    type: { // 添加 type 属性来判断是否为查看模式
      type: String,
      default: 'view' // view, edit, create
    }
  },
  data() {
    return {
      searchQuery: '',
      searchType: 'mobile', // 默认为手机
      memberColumns: [
        {
          title: 'Person ID',
          key: 'person_id',
          width: 100
        },
        {
          title: '卡号',
          key: 'card_number',
          width: 130
        },
        {
          title: '手机',
          key: 'mobile',
          width: 120
        },
        {
          title: '加入时间',
          key: 'created_time',
          width: 160
        },
        {
          title: '操作者',
          key: 'operator',
          width: 80
        },
        {
          title: '原因',
          key: 'reason',
          width: 210
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          render: (h, params) => {
            const operations = []
            operations.push(h('i-button', {
              props: {
                type: 'primary',
                ghost: true
              },
              style: {
                marginRight: '10px'
              },
              on: {
                click: () => this.handleViewMember(params.row)
              }
            }, '查看'))

            // 根据 type 属性判断是否禁用移出按钮
            const isRemoveDisabled = this.type === 'view';

            operations.push(h('i-button', {
              props: {
                type: 'error',
                ghost: true,
                disabled: isRemoveDisabled // 禁用状态
              },
              on: {
                click: () => this.handleRemoveMember(params.row)
              }
            }, '移出'))

            return h('div', {
              style: {
                display: 'flex',
                alignItems: 'center'
              }
            }, operations)
          }
        }
      ],
      memberList: [],
      removeModal: false,
      addModal: false,
      currentRow: null,
      addForm: {
        identifier: '',
        reason: '',
        type: 'mobile' // 默认为手机
      },
      currentPage: 1,
      pageSize: 10,
      totalMembers: 0,
      uploadModal: false,
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.initData();
      }
    }
  },
  methods: {
    async initData(size = 10, page = 1) {
      try {
        const data = {
          is_blacklist: this.viewStatus, // true表示是黑名单，false表示白名单
          biz_type: this.biz_type, // 父组件传过来的scene_name
          size: size || this.pageSize,
          page: page || this.currentPage
        };

        // 调用 getRiskMemberList API 获取黑白名单数据
        const memberListResp = await this.$api.getRiskMemberList(data);

        if (memberListResp.status === 'success') {
          this.totalMembers = memberListResp.data.total;
          this.memberList = memberListResp.data.result;
          // 更新当前页码和页面大小
          this.currentPage = page;
          this.pageSize = size;
        } else {
          this.$message.error('获取名单数据失败');
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败');
      }
    },

    handleAdd() {
      this.addModal = true;
      this.addForm = {
        identifier: '',
        reason: '',
        type: 'mobile'
      };
    },

    handleImport() {
      this.uploadModal = true;
    },

    handleViewMember(row) {
      // TODO: 实现查看成员详情功能
      // this.$router.push({ path: '/customerSearch', query: { phone: row.phone } });
    },

    handleRemoveMember(row) {
      this.currentRow = row;
      this.removeModal = true;
    },

    async confirmRemove() {
      try {
        // 使用 deleteRowData API 移除成员，参数顺序：biz_type, is_blacklist, person_id
        await this.$api.deleteRowData(
          this.biz_type,
          this.viewStatus,
          this.currentRow.personId
        );
        this.$Message.success('移出成功');
        this.removeModal = false;
        this.initData(this.pageSize, 1); // 刷新列表
      } catch (error) {
        this.$message.error('移出失败');
      }
    },

    async confirmAdd() {
      if (!this.addForm.identifier || !this.addForm.reason) {
        this.$message.warning('请填写完整信息');
        return;
      }

      const postData = {
        [this.addForm.type]: this.addForm.identifier,
        reason: this.addForm.reason,
      };

      try {
        // 第一步：通过 postRowData 验证和获取用户信息
        const allData = await this.$api.postRowData(postData);

        // 第二步：构建要添加到名单的数据
        const postListData = [{
          "person_id": allData.data.person_id,
          "card_number": allData.data.card_number,
          "mobile": allData.data.mobile,
          "reason": this.addForm.reason
        }];

        // 第三步：调用 postTableData 将数据添加到黑白名单
        await this.$api.postTableData(postListData, this.biz_type, this.viewStatus);

        this.$message.success('添加成功');
        this.addModal = false;
        this.initData(this.pageSize, 1); // 刷新列表
      } catch (error) {
        this.$message.error('添加失败');
      }
    },

    handleCancel() {
      this.$emit('update:show', false);
      this.$emit('cancel');
    },

    async handleSearch() {
      if (!this.searchQuery) {
        this.$message.warning('请输入搜索内容');
        return;
      }

      try {
        const data = {
          is_blacklist: this.viewStatus,
          biz_type: this.biz_type,
          size: this.pageSize,
          page: 1, // 搜索通常从第一页开始
          [this.searchType]: this.searchQuery,
        };

        const memberListResp = await this.$api.getRiskMemberList(data);

        if (memberListResp.status === 'success') {
          this.memberList = memberListResp.data.result;
          this.totalMembers = memberListResp.data.total;
          this.currentPage = 1;
          this.$message.success('搜索成功');
        } else {
          this.$message.error('搜索失败');
        }
      } catch (error) {
        this.$message.error('搜索失败，请重试');
      }
    },

    handleReset() {
      this.searchQuery = '';
      this.searchType = 'mobile';
      this.initData(this.pageSize, 1); // 重置并刷新数据
    },

    handlePageChange(page) {
      this.currentPage = page;
      this.initData(this.pageSize, page);
    },

    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.initData(size, 1);
    },

    handleUploadCancel() {
      this.uploadModal = false;
    },

    handleUploadSuccess() {
      this.uploadModal = false;
      this.initData(this.pageSize, 1); // 刷新列表
    },
  },
};
</script>

<style lang="less" scoped>
.modal-content {
  width: 100%;
  padding: 0 20px;
}

.section-container {
  margin-bottom: 20px;
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #17233d;
}

.info-box {
  border: 1px solid #e8eaec;
  padding: 15px;
  border-radius: 4px;
}

.info-item {
  margin-bottom: 10px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    color: #515a6e;
    margin-right: 8px;
  }

  .value {
    color: #17233d;
  }
}

.filter-box {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-direction: row;
  justify-content: space-between;
  .filter-box-left {
    display: flex;
    flex-direction: row;
  }
  .action-buttons {
    margin-left: 15px;
    button {
      margin-right: 10px;
    }
}
}

.table-container {
  max-height: 300px;
  overflow-y: auto;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  padding: 8px;
  border-bottom: 1px solid #e8eaec;
  color: #515a6e;

  &:last-child {
    border-bottom: none;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #e8eaec;
  margin-top: 20px;
}

.operation-buttons {
  display: flex;
  align-items: center;
}

.group-operation-btn {
  color: rgba(54, 67, 186, 1);
  background-color: rgba(0,0,0,0);
  border: none;
  font-size: 14px !important;

  &:active, &:focus, &:hover {
    background-color: rgba(0,0,0,0);
    border: none;
    box-shadow: none;
  }
}

.add-form {
  padding: 10px 0;

  .mt10 {
    margin-top: 10px;
  }
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.drag-upload-wrap {
  width: 400px;
  margin: 0 auto;
  text-align: center;
}

.upload-wrap {
  margin-top: 20px;
  text-align: right;
  padding-right: 20px;

  .upload-btn {
    width: 75px;
  }
}
</style>
