// 状态
const statusList = [
	{ value: 'paid', label: '待领取' },
	{ value: 'redeemed', label: '已领取' },
	{ value: 'confirmed', label: '已确认' },
	{ value: 'expired', label: '已过期' },
]

// 字段名称转换
const convert = function(value, list) {
	let result = value
	for (let i = 0; i < list.length; i++) {
		let item = list[i]
		if (item.value === value) {
			result = item.label
			break;
		}
	}
	return result
}
  
export default {
	statusList,
	convert
}