<template>
  <div class="store">
    <i-modal :styles="{top: '20vh'}" :closable="false" :mask="false" :fullscreen="false" :mask-closable="false" title="温馨提示" footer-hide v-model="showModal" width="600">
      <div class="store-modal-content"> 
        <div class="store-modal-content-desc">活动发布功能目前已转至运动连小程序，复制下方链接至运动连后台发布。</div>
        <div class="store-modal-content-link">
          <a>{{otherBoLink}}</a>
        </div>
      </div>
    </i-modal>
  </div>
</template>

<script>
export default {
  name: 'activity_store',
  data() {
    return {
      showModal: true,
      otherBoLink: ''
    }
  },
  mounted() {
    this.checkCurrentEnv()
      .isPr(() => {
        this.otherBoLink = 'https://web-backoffice-sportlover.decathlon.com.cn/merchant-login'
      })
      .isPp(() => {
        this.otherBoLink = 'https://web-backoffice-sportlover.decathlon.com.cn/merchant-login'
      })
  },
  methods: {
    // 环境判断
    checkCurrentEnv() {

      const isPrd = process.env.API_ROOT !== 'https://membership.pp.dktapp.cloud'


      const chain = {
        isPr: () => {},
        isPp: () => {}
      }

      chain.isPr = (cb) => {
        isPrd && cb && cb()
        return chain
      }
      chain.isPp = (cb) => {
        !isPrd && cb && cb()
        return chain
      }
      
      return chain
    }
  }
}
</script>

<style lang="less">
.store-modal-content {
   font-size: 15px;
   padding: 20px 0;
   &-desc {
   }
   &-link {
     padding-top: 4px;
   }
}
</style>
