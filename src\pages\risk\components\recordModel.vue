<template>
  <div class="section-container">
    <div class="section-title">预评估记录</div>
    <div class="assessment-content">
      <div class="assessment-header">
        <div class="assessment-search">
          <i-input v-model="assessmentSearchQuery" placeholder="卡号" style="width: 200px" />
          <div class="assessment-actions">
            <i-button type="primary" @click="handleAssessmentSearch">搜索</i-button>
            <i-button type="primary" @click="handleAssessmentReset">重置</i-button>
          </div>
        </div>
      </div>
      <div class="assessment-table-container">
        <i-table :columns="assessmentColumns" :data="assessmentList" border>
          <template slot-scope="{ row }" slot="ruleList">
            <span>{{ row.ruleList }}
            </span>
          </template>
        </i-table>
        <div class="assessment-pagination">
          <i-page
            :total="assessmentPaginationTotal"
            :current="assessmentPaginationCurrent"
            :page-size="assessmentPaginationSize"
            :page-size-opts="[10, 20, 30, 50]"
            show-total
            show-sizer
            @on-change="handleAssessmentPaginationChange"
            @on-page-size-change="handleAssessmentPaginationSizeChange"
          />
        </div>
      </div>
    </div>
    <i-modal v-model="ruleDetailModal" title="规则详情" @on-ok="handleRuleDetailModalOk">
      <div v-html="currentRuleDetail"></div>
    </i-modal>
  </div>
</template>

<script>
export default {
  name: 'RecordModel',
  props: {
    scene_id: {
      type: [Number, String],
      default: null
    },
    type: {
      type: String,
      default: 'view' // view, edit, create
    }
  },
  data() {
    return {
      assessmentSearchQuery: '',
      assessmentPaginationCurrent: 1,
      assessmentPaginationSize: 10,
      assessmentPaginationTotal: 0,
      assessmentList: [],
      ruleDetailModal: false,
      currentRuleDetail: '',
      assessmentColumns: [
        {
          title: '时间',
          key: 'create_time',
          width: 180
        },
        {
          title: '卡号',
          key: 'object_id',
        },
        {
          title: '评估分数',
          key: 'total_score',
          width: 180
        },
        {
          title: '未通过规则',
          key: 'ruleDetail',
          width: 100,
          render: (h, params) => {
            return h('i-button', {
              props: {
                type: 'primary',
                ghost: true
              },
              on: {
                click: () => {
                  this.handleShowRuleDetail(params.row)
                }
              }
            }, '详情')
          }
        }
      ]
    }
  },
  mounted() {
    // 只有在查看或编辑模式下才加载数据，新建模式不需要加载
    if (this.type === 'view' || this.type === 'edit') {
      this.loadAssessmentList()
    }
  },
  methods: {
    async loadAssessmentList() {
      try {
        const params = {
          scene_id: this.scene_id,
          page: this.assessmentPaginationCurrent,
          size: this.assessmentPaginationSize
        };
        const resp = await this.$api.querySceneBlackList(params);

        if (resp.status === 'success') {
          this.assessmentList = resp.data.result;
          this.assessmentPaginationTotal = resp.data.total;
        }
      } catch (error) {
        this.$message.error('获取评估记录失败');
      }
    },

    handleAssessmentPaginationChange(page) {
      this.assessmentPaginationCurrent = page;
      this.loadAssessmentList();
    },

    handleAssessmentPaginationSizeChange(size) {
      this.assessmentPaginationSize = size;
      this.assessmentPaginationCurrent = 1;
      this.loadAssessmentList();
    },

    async handleAssessmentSearch() {
      if (!this.assessmentSearchQuery) {
        this.$message.warning('请输入卡号');
        return;
      }

      try {
        const params = {
          scene_id: this.scene_id,
          page: 1,
          size: this.assessmentPaginationSize,
          card_number: this.assessmentSearchQuery
        };
        const resp = await this.$api.querySceneBlackList(params);

        if (resp.status === 'success') {
          this.assessmentList = resp.data.result;
          this.assessmentPaginationTotal = resp.data.total;
          this.assessmentPaginationCurrent = 1;
          this.$message.success('搜索成功');
        }
      } catch (error) {
        this.$message.error('搜索失败');
      }
    },

    handleAssessmentReset() {
      this.assessmentSearchQuery = '';
      this.loadAssessmentList();
    },

    async handleShowRuleDetail(row) {
      try {
        const resp = await this.$api.querySceneBlackListDetail(row.black_list_id)

        if (resp.status === 'success' && resp.data && Array.isArray(resp.data) && resp.data.length > 0) {
          this.currentRuleDetail = resp.data.map((rule, index) => {
            // 使用固定宽度来对齐显示
            const ruleName = rule.rule_name || ''
            const ruleDesc = rule.rule_desc || ''
            return `<div style="display: flex; margin-bottom: 8px;">
                      <span style="display: inline-block; min-width: 120px;">命中规则：${ruleName}</span>
                      <span style="margin-left: 20px;">规则描述：${ruleDesc}</span>
                    </div>`
          }).join('')
        } else {
          this.currentRuleDetail = '暂无规则详情'
        }

        this.ruleDetailModal = true
      } catch (error) {
        this.$message.error('获取规则详情失败')
      }
    },

    handleRuleDetailModalOk() {
      this.ruleDetailModal = false;
    }
  }
}
</script>

<style lang="less" scoped>
.section-container {
  margin-bottom: 20px;
  width: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #17233d;
}

.assessment-content {
  border: 1px solid #e8eaec;
  padding: 20px;
  border-radius: 4px;
  background: #fff;
}

.assessment-header {
  margin-bottom: 15px;
}

.assessment-search {
  display: flex;
  align-items: center;
}

.assessment-actions {
  margin-left: 15px;
  button {
    margin-right: 10px;
  }
}

.assessment-table-container {
  position: relative;
  margin-top: 15px;
}

.assessment-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>
