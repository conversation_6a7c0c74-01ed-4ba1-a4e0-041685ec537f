import fetch from '@/utils/network/fetch'

// 获取 抽奖活动列表
const ldActivityList = params => {
  return fetch(`/api/v1/draw?pageNo=${params.pageNo}&pageSize=${params.pageSize}`)
}

// 获取 抽奖活动详情
const ldActivityDetail = id => {
  return fetch(`/api/v1/draw/${id}`)
}
// 发布 抽奖活动
const ldActivityPublish = (body = {}) => {
	return fetch('/api/v1/draw/save', { method: 'post', data: JSON.stringify(body) })
}

// 保存草稿 抽奖活动
const ldActivityDraftSave = (body = {}) => {
  return fetch('/api/v1/draw/draft', { method: 'post', data: JSON.stringify(body) })
}

// 删除 单个抽奖活动
const ldActivityDelete = id => {
  return fetch(`/api/v1/draw/${id}`, { method: 'delete' })
}

// 上传图片
const ldUploadImage = (url, data = {}) => {
	return fetch(url, data)
}

export default {
  ldActivityList,
  ldActivityDetail,
  ldActivityPublish,
  ldActivityDraftSave,
  ldActivityDelete,
  ldUploadImage,
}
