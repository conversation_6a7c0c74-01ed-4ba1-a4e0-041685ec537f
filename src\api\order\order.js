import fetch from '@/utils/network/fetch'

// 搜索订单
const searchOrder = (query = {}) => {
  return fetch('/api/v1/orders', { data: query })
}

// 核销订单
const redeemOrder = id => {
  return fetch(`/api/v1/orders/${id}/redeem`, { method: 'put' })
}

// 确认订单
const confirmOrder = (body = {}) => {
  return fetch('/api/v1/orders/confirm', { method: 'put', data: body })
}

// 待核销订单数量
const countPaidOrders = (query = {}) => {
  return fetch('/api/v1/orders/paidCounts', { data: query })
}

export default {
  searchOrder,
  redeemOrder,
  confirmOrder,
  countPaidOrders
}
