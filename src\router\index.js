import Vue from 'vue'
import VueRouter from 'vue-router'
import autoRoutes from './autoRoutes'
import staticRoutes from './staticRoutes'
import basisConfig from '@/config/basisConfig'
import apiConfig from '@/config/apiConfig'
import { local } from '@/utils/util'

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(VueRouter)

const router = new VueRouter({
  // mode: 'history',

  scrollBehavior: () => ({ y: 0 }),

  routes: basisConfig.autoRoute ? autoRoutes : staticRoutes
})

// 路由钩子
router.beforeEach((to, from, next) => {
  // 在网络层处理

  // if (!to.query.code) {
  //   if (to.meta.requiresAuth && !local.getStorage('TOKEN')) {
  //     // 跳转到三方登录
  //     window.location.href = apiConfig.LOGIN_PAGE_PATH
  //   }
  // }
  next()
})

router.afterEach((to, from) => {})

export default router
