<template>
  <div class="group groupActivities">
    <div class="header mt20 mb30">
        <div class="flex row item-center justify-between">
          <div>
            <i-input v-model="searchForm.name" class="search mr15" search placeholder="模糊查询(模板标题)"/>
            <i-button class="mr15" @click="searchTemplate">查询</i-button>
            <i-button class="mr15" @click="resetClick">重置</i-button>
          </div>
          <div>
            <i-button type="primary" icon="md-add" @click="popupCreate" v-has="['EVENT_TEMPLATE_UPDATE']"> 新建 </i-button>
          </div>
        </div>
        <div class="flex row item-center mt20">
          <i-select class="dropdown mr15" v-model="searchForm.categoryId" placeholder="运动分类" clearable>
            <i-option value="">全部</i-option>
            <i-option :value="item.id" v-for="item in categoryList" :key="item.id">
              {{item.name}}
            </i-option>
          </i-select>
          <i-select class="dropdown mr15" v-model="searchForm.type" placeholder="活动类型" clearable>
            <i-option value="">全部</i-option>
            <i-option v-for="item in typeList" :value="item.value" :key="item.value">
              {{item.label}}
            </i-option>
          </i-select>
          <i-select class="dropdown mr15" v-model="searchForm.target" placeholder="面向人群" clearable>
            <i-option value="">全部</i-option>
            <i-option v-for="item in targetList" :value="item.value" :key="item.value">
              {{item.label}}
            </i-option>
          </i-select>
          <i-select class="dropdown mr15" v-model="searchForm.level" placeholder="运动等级" clearable>
            <i-option value="">全部</i-option>
            <i-option v-for="item in levelList" :value="item.value" :key="item.value">
              {{item.label}}
            </i-option>
          </i-select>
        </div>
    </div>

    <i-table class="text-center" border :columns="templateColumns" :data="templateList" />
    <div class="table-footer">
      <div style="float: right;">
        <i-page :total="page.total" show-total
          :page-size="page.size" show-sizer
          :page-size-opts="pageSizeOpts"
          @on-change="changePageNo" @on-page-size-change="changePageSize" />
      </div>
    </div>

    <t-modal v-if="showDialog" :title="title" :certain="modalCertain" :cancel="modalCancel" :activity-id="id" :op-from="opFrom" />

    <s-modal v-if="showStoreDialog" :title="title" :certain="modalCertain" :cancel="modalCancel" :activity-id="id" :op-from="opFrom" />
  </div>
</template>

<script>
import cfg from '../config'
import TemplateModal from './profile/modal'
import ActivityModal from '../store/profile/modal'
import { pageSizeOpts, checkFill, judgeFill } from '@/utils/util'

export default {
  name: 'activity_gruop',
  components: {
    't-modal': TemplateModal,
    's-modal': ActivityModal
  },
  data() {
    return {
      page: { no: 1, size: 10, total: 0 },
      pageSizeOpts: pageSizeOpts,
      opFrom: '',

      templateList: [],         // 集团活动模板列表
      categoryList: [],         // 分类列表

      searchForm: {
        name: '',               // 标题
        categoryId: '',         // 分类
        type: '',               // 类型
        target: '',             // 面向人群
        level: '',              // 运动等级
        isTemplate: true,
        active: true
      },

      // 表格操作按钮
      opBtnList: [
        { code: 'store', text: '启用', permissions: ['EVENT_TEMPLATE_ENABLE'] },
        { code: 'edit', text: '编辑', permissions: ['EVENT_TEMPLATE_UPDATE'] },
        { code: 'disable', text: '禁用', type: 'warning', permissions: ['EVENT_TEMPLATE_UPDATE'] }
      ],

      templateColumns: [
        { title: '活动主图', className: 'th-class', render: (h, params) => { return this.showRowImage(h, params.row.gallery.url) }},
        { title: '活动标题', key: 'name', className: 'th-class' },
        { title: '运动分类', key: 'categoryName', className: 'th-class', width: 130, render: (h, params) => { return h('span', params.row.category.name) }},
        { title: '活动类型', key: 'type', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.type, this.typeList)) }},
        { title: '面向人群',  key: 'target', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.target, this.targetList)) }},
        { title: '运动等级',  key: 'level', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.level, this.levelList)) }},
        { title: '精选', key: 'isStar', className: 'th-class', render: (h, params) => { return h('span', params.row.isStar ? '是' : '否') } },
        { title: '全国精选', key: 'isNationalStar', className: 'th-class', render: (h, params) => { return h('span', params.row.isNationalStar ? '是' : '否') } },
        { title: '操作',  key: 'action', className: 'th-class opeartions-box', fixed: 'right', width: 255, render: (h, params) => { return this.createOpBtn(h, params.row) } }
      ],

      typeList: cfg.typeList,         // 活动类型
      targetList: cfg.targetList,     // 面向人群
      levelList: cfg.levelList,       // 运动等级
      paymentList: cfg.paymentList,   // 报名方式

      title: '',
      showDialog: false,

      id: '',
      rules: {
        name: { value: null, error: false, text: '请输入标题' },
        categoryId: { value: null, error: false, text: '请选择分类' },
        type: { value: null, error: false, text: '请选择类型' },
        target: { value: null, error: false, text: '请选择面向人群' },
        level: { value: null, error: false, text: '请选择运动等级' },
        paymentType: { value: null, error: false, text: '请选择报名方式' },
        description: { value: null, error: false, text: '请填写活动详情' },
        galleryId: { value: null, error: false, text: '请选择活动主图' }
      },

      showChooseDialog: false,
      selectedGallery: '',
      galleryList: [],

      showStoreDialog: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 初始数据
    loadData() {
      this.getCategories()
      this.searchTemplate()
    },
    // 获取分类列表
    async getCategories() {
      let resp = await this.$api.getSportsCategories()
      this.categoryList = resp.data
    },

    // 渲染表格图片
    showRowImage(h, url, weight, height) {
      return h('span', { class: 'span-pic-box' }, [ h('img', { attrs: { src: url, class: 'group-img' } }) ])
    },
    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      this.opBtnList.forEach((value) => {
        operations.push(h('i-button',
          {
            class: 'group-operation-btn ml5 mr5 mt5 mb5',
            props: { type: value.type },
            directives: [
              {
                name: 'has',
                value: value.permissions
              }
            ],
            on: { click: () => { this.rowOperation(value.code, row) }}
          }, value.text))
      })
      return h('div', operations);
    },
    // 重置搜索条件
    resetClick() {
      this.searchForm = { isTemplate: true, active: true }
      this.loadData()
    },
    // 分页获取活动模板列表
    async searchTemplate() {
      this.searchForm['pageNo'] = this.page.no
      this.searchForm['pageSize'] = this.page.size
      let resp = await this.$api.searchTemplate(this.searchForm)
      this.templateList = resp.data.items
      this.page.total = resp.data.total
    },
    // 分页操作
    changePageNo(no) {
      this.page.no = no
      this.searchTemplate()
    },
    changePageSize(size) {
      this.page.size = size
      this.searchTemplate()
    },

    // 表格项操作
    rowOperation(code, row) {
      switch(code) {
        case 'store':
          this.createStoreActivity(row.id)
          break
        case 'edit':
          this.getTemplateDetail(row.id)
          break
        case 'disable':
          this.disableTemplate(row)
          break
      }
    },

    async createStoreActivity(id) {
      this.title = '使用集团模板创建门店活动'
      this.id = id
      this.opFrom = 'template'
      this.showStoreDialog = true
    },

    // 获取活动模板详情
    async getTemplateDetail(id) {
      this.title = '编辑集团活动模板'
      this.id = id
      this.opFrom = 'update'
      this.showDialog = true
    },
    // 禁用模板
    disableTemplate(row) {
      this.$modal.confirm({
        'title': '集团模板禁用',
        'content': `您即将禁用布集团模板<span class='confirm-msg'>${row.name}</span>.<br>请确认!`,
        'okText': '确定',
        'cancelText': '取消',
        'onOk': () => {
          this.doDisableAction(row.id)
        }
      })
    },

    async doDisableAction(id) {
      let resp = await this.$api.disableTemplate(id)
      if (resp.status == 'success') {
        this.searchTemplate()
      }
    },

    // 新建
    popupCreate() {
      this.title = '创建集团活动模版'
      this.opFrom = 'create'
      this.templateDetail = {}
      this.showDialog = true
    },
    modalCertain() {
      this.closeDailog()
    },
    modalCancel() {
      this.closeDailog()
    },
    closeDailog() {
      switch(this.opFrom) {
        case 'template':
          this.showStoreDialog = false
          break
        default:
          this.showDialog = false
      }
      this.id = ''
      this.searchTemplate()
    }
  }
}
</script>

<style lang="less">
@import url('../css/activityCommon.less');
.groupActivities {
  .search {
    width: 400px!important;
  }
  .modal-content {
    width: 100%;
    .modal-item {
      width: 500px;
    }
  }
  .span-pic-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 0 auto;
    padding:10px 0;
    height: auto;
    text-align: center;
    vertical-align: middle;
    .group-img {
      width: 4.375vw;
      height: 2.5vw;
    }
  }
  .group-operation-btn {
    text-align: center;
  }
  .group-operation-btn>span{
    display: block;
    width: 30px!important;
    box-sizing: border-box;
    text-align: center;
  }
}
</style>
