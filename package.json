{"name": "backfrontend", "version": "1.0.0", "description": "a management system framework based on iview", "author": "zhangxu <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --host 0.0.0.0 --disableHostCheck true --config build/webpack.dev.conf.js", "start": "npm run dev", "unit": "cross-env BABEL_ENV=test karma start test/unit/karma.conf.js --single-run", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "build": "node build/build.js", "doc": "jsdoc -c ./config/doc.conf.js -r"}, "dependencies": {"axios": "^0.18.0", "babel-polyfill": "^6.26.0", "iview": "^3.1.3", "minami": "^1.2.3", "vue": "^2.5.2", "vue-base64-file-upload": "^1.0.4", "vue-echarts": "^3.1.3", "vue-good-table": "^2.15.3", "vue-i18n": "^8.2.1", "vue-router": "^3.0.1", "vuex": "^3.0.1"}, "devDependencies": {"autoprefixer": "^7.1.2", "autoprefixer-loader": "^3.2.0", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-import": "^1.10.0", "babel-plugin-istanbul": "^4.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chai": "^4.1.2", "chalk": "^2.0.1", "chromedriver": "^2.27.2", "copy-webpack-plugin": "^4.0.1", "cross-env": "^5.0.1", "cross-spawn": "^5.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "inject-loader": "^3.0.0", "jsdoc": "^3.5.5", "karma": "^1.4.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-phantomjs-shim": "^1.4.0", "karma-sinon-chai": "^1.3.1", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.31", "karma-webpack": "^2.0.2", "less": "^2.7.3", "less-loader": "^2.2.3", "mocha": "^3.2.0", "mockjs": "^1.0.1-beta3", "nightwatch": "^0.9.12", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "phantomjs-prebuilt": "^2.1.14", "portfinder": "^1.0.13", "postcss-import": "^11.1.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "sinon": "^4.0.0", "sinon-chai": "^2.8.0", "style-loader": "^0.13.1", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}