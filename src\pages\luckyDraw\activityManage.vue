<template>
  <div class="activity-manage">
    <i-row class="mt20 mb30 top-btns">
      <i-col  class="text-right">
        <i-button type="primary" class="create-activity" @click="createNewActivity" >新建活动</i-button>
      </i-col>
    </i-row>
    <i-row class="table-wrap">
      <i-table class="text-center" border :columns="activityColumns" :data="activityList" >
        <!-- 操作 -->
        <template slot-scope="{ index, row }" slot="action">
          <div class="button-list">
            <i-button class="button-item" @click="rowOperation(btnItem.code, row)" v-for="(btnItem, btnIndex) in activityStatusConfig[row.status].buttonList" :key="btnIndex" :type="btnItem.type">{{ btnItem.text }}</i-button>
          </div>
        </template>
      </i-table>
    </i-row>
    <i-row class="mt15">
      <i-col class="text-right">
        <i-page :total="page.total" show-total 
          :current="page.pageNo"
          :page-size="page.pageSize" show-sizer 
          :page-size-opts="pageSizeOpts"
          @on-change="changePageNo" @on-page-size-change="changePageSize" />
      </i-col>
    </i-row>
  </div>
</template>

<script>
import { pageSizeOpts } from '@/utils/util'
import { pageUrl } from '@/config/constant'
export default {
  name: 'ActivityManage',
  data() {
    return {
      // 分页相关配置
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      pageSizeOpts: pageSizeOpts,
      activityColumns: [
        { title: '活动ID', key: 'id', width: 80, className: 'th-class pro-img-box'},
        { title: '奖品图', key: 'imageLinkList', className: 'th-class pro-img-box', width: 200, render: (h, params) => { return this.showRowImage(h, params.row.imageLinkList) }},
        { title: '活动生效时间', key: 'startTime', className: 'th-class', render: (h, params) => { return h('span', params.row.startTime) }},
        { title: '活动创建时间', key: 'createTime', className: 'th-class', render: (h, params) => { return h('span', params.row.createTime) }},
        { title: '活动状态', key: 'status', className: 'th-class', render: (h, params) => { return this.getStatusText(h, params.row) }},
        { title: '操作',  slot: 'action', className: 'th-class action-buttons', align:'left', fixed: 'right', width: 230 }
      ],
      // 活动列表
      activityList: [],
      // 活动状态文案配置
      activityStatusConfig: {
        'IN_EFFECT': {
          text: '生效中',
          color: '#149B65',
          buttonList: [
            { code: 'edit', text: '编辑', type: 'default' },
            { code: 'copy', text: '复制', type: 'default' },
          ]
        },
        'TO_BE_EFFECT': {
          text: '待生效',
          color: '#DB6900',
          buttonList: [
            { code: 'edit', text: '编辑', type: 'default' },
            { code: 'copy', text: '复制', type: 'default' },
            { code: 'delete', text: '删除', type: 'error' }
          ]

        },
        'DRAFT': {
          text: '编辑中',
          color: '#616161',
          buttonList: [
            { code: 'edit', text: '编辑', type: 'default' },
            { code: 'copy', text: '复制', type: 'default' },
            { code: 'delete', text: '删除', type: 'error' }
          ],
        },
        'ENDED': {
          text: '已结束',
          color: '#949494',
          buttonList: [
            { code: 'view', text: '查看', type: 'default' },
            { code: 'copy', text: '复制', type: 'default' },
          ]
        }
      },
    };
  },
  methods: {
    createNewActivity(){
      this.goToAwardActivityPage('create', null);
    },
    // 分页操作
    changePageNo(pageNo) {
      this.page.pageNo = pageNo
      this.getActivityList()
    },
    changePageSize(pageSize) {
      this.page.pageSize = pageSize
      this.getActivityList()
    },
    async getActivityList() {
      const { pageNo, pageSize	 } = this.page;
      const params = {
        pageNo,
        pageSize	
      }
      let resp = await this.$api.ldActivityList(params);
      if (resp.status === 'success') {
        this.activityList = resp.data.items || [];
        this.page.total = resp.data.total
      }

    },

    goToAwardActivityPage(pageType, id = null, activityStatus = null){
      this.$local.setStorage('PAGE_INFO', { pageType, id, activityStatus })
      const { awardActivityConfig } = pageUrl.luckyDraw;
      let pathUrl = `${awardActivityConfig}`;
      this.$router.push({
        path: pathUrl
      })
    },
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      if(!imageUrls || imageUrls.length === 0) {
        return h('span', '')
      }
      let imgArr = [];
      imageUrls.forEach((item, index) => {
        item && imgArr.push(h('img', { attrs: { src: item, class: 'table-img proImg' } }));
      });
      return h('div', { class: 'table-pic-box' }, [imgArr]);
    },
    // 生成活动状态最终显示彩色文案
    getStatusText(h, row) {
      let statusConfig = this.activityStatusConfig[row.status];
      return h('span', { style: { color: statusConfig.color } }, statusConfig.text);
    },
    rowOperation(code, row) {
      const { id } = row;
      switch (code) {
        case 'view':
        case 'edit':
        case 'copy':
          this.goToAwardActivityPage(code, id, row.status);
          break;
        case 'delete':
          this.showDeleteActivityModal(id);
          break;
      }
    },
    showDeleteActivityModal(id) {
      this.$modal.confirm({
        'title': '确定删除活动？',
        'content': ``,
        'okText': '删除',
        'cancelText': '取消',
        'onOk': () => {
          this.handleDeleteActivity(id);
        }
      })
    },
    async handleDeleteActivity(id) {
      console.log('>>>>>删除活动');
      // ldActivityDelete
      let resp = await this.$api.ldActivityDelete(id);
      if (resp.status === 'success') {
        this.$message.success('删除活动成功');
        this.getActivityList();
      }
    }
  },
  created() {
    this.getActivityList();
  },
};
</script>

<style scoped lang="less">
.activity-manage {
  /deep/.table-wrap{
    .pro-img-box {
      .table-pic-box {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        padding:10px 0;
        height: auto;
        overflow: hidden;
        text-align: center;
        box-sizing: border-box;
        .proImg {
          width: 48px!important;
          height: 48px!important;
          &:not(:last-child) {
            margin-right: 12px;
          }
        }
      }
    }
    .ivu-btn-error{
      border: 1px solid #FF4D4F;
      background: #FFF ;
      color: #FF4D4F;
    }
    .button-list{
      float: left;
      .button-item{
        margin-left: 10px;
      }
    }
  }
}
</style>