<template>
  <div class="container column item-center">
    <div class="navi-bar title-font">活动详情</div>
    <div class="banner-img title-font">
      <img class="banner-img" :src="details.gallery ? details.gallery.url : ''" alt="">
    </div>
    <div class="desc">
      <div class="activity-block border-bottom">
        <div class="activity-item title-font">{{details.name}}</div>
        <div class="activity-item font-gery">报名时间: {{details.times ? details.times[0] : ''}}-{{details.times ? details.times[1] : ''}}</div>
      </div>
      <div class="activity-block border-bottom">
        <div class="activity-item title-font">{{details.store ? details.store.name : ''}}</div>
        <div class="activity-item font-gery">{{details.store ? details.store.address : ''}}</div>
        <div class="activity-item mt10 row overflow-x">
          <div class="time-grid column item-center" v-for="session in details.sessions" :key="session.id">
            <div>{{session.day}}</div>
            <div>{{session.times ? session.times[0] : ''}}-{{session.times ? session.times[1] : ''}}</div>
            <div v-if="session.remainingStock == 0">已满</div>
            <div v-else>{{session.remainingStock}}/{{session.totalStock}}</div>
          </div>
        </div>
      </div>
      <div class="activity-block">
        <div class="activity-item row item-center"> 活动介绍 </div>
        <div class="activity-item">
          <p class="font-gery">{{details.description}}</p>
        </div>
      </div>
    </div>
    <div class="desc">
      <div class="activity-block">
        <div class="activity-item row item-center"> 活动详情 </div>
        <div class="activity-item">
          <p class="font-gery">运动分类:  {{details.category ? details.category.name : ''}}</p>
          <p class="font-gery">活动类型: {{details.type}}</p>
          <p class="font-gery">活动人群: {{details.target}}</p>
          <p class="font-gery">运动强度: {{details.level}}</p>
          <p class="font-gery">报名方式: {{details.paymentType}}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "s-activity",
  props: {
    details: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  methods: {
  }
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  background: rgba(245, 246, 247, 1);
  .navi-bar {
    top: 0;
    width: 100%;
    height: 35px;
    text-align: center;
    line-height: 35px;
    background: #fff;
  }
  .banner-img {
    width: 100%;
    height: 100px;
  }
  .desc {
    width: 95%;
    margin-top: 10px;
    padding: 10px 0;
    background: #fff;
    border-radius: 4px;
    .activity-block {
      padding: 10px 0;
    }
    .activity-item {
      padding: 0 10px;
    }
    .time-grid {
      padding: 5px 0;
      margin-right: 5px;
      height: 60px;
      width: 100px;
      border: solid 1px rgba(245, 246, 247, 1);
      border-radius: 4px;
    }
  }
  .border-bottom {
    border-bottom: solid 1px rgba(245, 246, 247, 1);
  }
  .overflow-x {
    display: -webkit-box;
    overflow-x: auto;
  }
  .title-font {
    font-size: 14px;
  }
  .font-gery {
    color: rgb(190, 186, 186)
  }
  ::-webkit-scrollbar {
    display: none;
  }
}

</style>