<template>
  <div class="tab-wrapper">
    <!-- Your component's HTML template goes here -->
    <ul class="tabs">
      <li class="tab">Tab 1</li>
      <li class="tab">Tab 2</li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'ToggleTab',
  data() {
    return {
      // Your component's data goes here
    };
  },
  methods: {
    // Your component's methods go here
  },
  mounted() {
    // Code to run when the component is mounted goes here
  },
};
</script>

<style scoped lang="less">
/* Your component's CSS styles go here */
  .tab-wrapper{
    width: 100%; height: auto;
    ul,li{ list-style:none; }
    .tabs{
      display: flex; justify-content: flex-start;
      li{
        display: inline-block;
        text-align: center;
        padding: 10px 0;
        background: #f0f0f0;
        cursor: pointer;
        &:hover{
          background: #e0e0e0;
        }
      }
    }

  }
</style>