<template>
  <div class="store_report">
    <div class="mt20 mb30 row item-center justify-between">
      <div>
        <i-select v-model="batchNumber" style="width:160px" placeholder="月份" clearable @on-change="handleSearch">
          <i-option :value="item" v-for="item in batchNumberList" :key="item">{{item}}</i-option>
        </i-select>
      </div>
      <div v-if="batchNumber">
        <i-button icon="md-download" @click="handleExport">导出</i-button>
      </div>
    </div>
    <div>
      <div class="mt15 mb15">未上架产品门店：</div>
      <i-table border :columns="productStoreColumns" :data="productStoreList" ref="store" />
      <div class="table-footer">
        <div class="pull-right">
          <i-page :total="page.total" show-total 
            :page-size="page.size" show-sizer 
            :page-size-opts="pageSizeOpts"
            @on-change="changePageNo" @on-page-size-change="changePageSize" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { pageSizeOpts } from "@/utils/util"

export default {
  name: "store_product_report",
  data() {
    return {
      batchNumber: '',
      batchNumberList: [],
      page: {
        no: 1,
        size: 10,
        total: 0
      },
      pageSizeOpts: pageSizeOpts,

      productStoreColumns: [
        { title: '门店号', key: 'dktStoreNumber', className: 'th-class', sortable: true },
        { title: '门店城市', key: 'city', className: 'th-class' },
        { title: '门店名称', key: 'name', className: 'th-class' },
      ],
      productStoreList: []
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 初始数据
    async loadData() {
      let resp = await this.$api.listBatch()
      if (resp['status'] === 'success') {
        this.batchNumberList = resp.data
      }
    },

    // 刷新未发布产品门店
    async handleSearch() {
      if (this.batchNumber) {
        let searchForm = { 'batchNumber': this.batchNumber, 'pageNo': this.page.no, 'pageSize': this.page.size }
        let resp = await this.$api.getUnpublishProductStoreReport(searchForm)
        if (resp['status'] == 'success') {
          this.productStoreList = resp.data.items
          this.page.total = resp.data.total
        }
      }
    },

    // 分页操作
    changePageNo(no) {
      this.page.no = no;
      this.handleSearch();
    },
    changePageSize(size) {
      this.page.size = size;
      this.handleSearch();
    },

    // 导出报表
    async handleExport() {
      let searchForm = { 'batchNumber': this.batchNumber, 'all': true }
      let resp = await this.$api.getUnpublishProductStoreReport(searchForm)
      if (resp['status'] == 'success') {
        let stores = resp.data
        stores.forEach((value) => {
          value.dktStoreNumber = '\t' + value.dktStoreNumber
        })
        this.$refs.store.exportCsv({
          quoted: true,
          filename: `${this.batchNumber}_未发布产品门店报表`,
          columns: this.productStoreColumns,
          data: stores
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.store_report {
  .table-footer {
    margin: 20px 0;
    overflow: hidden;
  }
}

</style>