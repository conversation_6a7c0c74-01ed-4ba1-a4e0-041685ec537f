<template>
  <div class="modal-box">
    <i-modal fullscreen footer-hide scrollable :closable="false" :title="title" v-model="showDialog">
      <div class="modal-content-box flex column item-center">
        <i-form :model="activity" label-position="right" :label-width="100">
          <i-input v-model="activity.id" v-show="false" />
          <i-row>
            <i-col span="6">
              <i-form-item label="活动主图" class="modal-item mt15">
                <i-input v-model="activity['galleryId']" v-show="false" />
                <template v-if="activity['galleryUrl']">
                  <img :src="activity['galleryUrl']" alt="" style="width:175px;height:100px;">
                </template>
                <div>
                  <i-button icon="md-image" @click="popupGalleryChoose" v-if="!disableAll">选择图片</i-button>
                </div>
              </i-form-item>
            </i-col>
            <i-col span="6">
              <i-form-item label="活动标题" class="modal-item mt15">
                <i-poptip trigger="focus" content="字数限制10个" placement="top-start">
                  <i-input placeholder="请输入活动标题" style="width:12.5vw" v-model="activity['name']" :maxlength="10" :disabled="disabled || disableAll" :clearable="!disabled && !disableAll" />
                </i-poptip>
              </i-form-item>
              <i-form-item label="面向人群" class="modal-item">
                <i-select style="width:12.5vw" class="search mr15" v-model="activity['target']" placeholder="请选择面向人群"
                  :disabled="disabled || disableAll" clearable >
                  <i-option v-for="item in targetListCopy" :value="item.value" :key="item.value">
                    {{item.label}}
                  </i-option>
                </i-select>
              </i-form-item>
            </i-col>
            <i-col span="6">
              <i-form-item label="运动分类" class="modal-item mt15">
                <i-select style="width:12.5vw"  v-model="activity['categoryId']" placeholder="请选择运动分类" 
                  :disabled="disabled || disableAll" clearable @on-change="changeCategory">
                  <i-option :value="item.id" v-for="item in categoryList" :key="item.id">
                    {{item.name}}
                  </i-option>
                </i-select>
              </i-form-item>
              <i-form-item label="运动等级" class="modal-item">
                <i-select style="width:12.5vw" class="search mr15" v-model="activity['level']" placeholder="请选择运动等级" 
                  :disabled="disabled || disableAll" clearable >
                  <i-option v-for="item in levelList" :value="item.value" :key="item.value">
                    {{item.label}}
                  </i-option>
                </i-select>
              </i-form-item>
            </i-col>
            <i-col span="6">
              <i-form-item label="活动类型" class="modal-item mt15">
                <i-select style="width:12.5vw" class="search mr15" v-model="activity['type']" placeholder="请选择活动类型" 
                  :disabled="disabled || disableAll" clearable @on-change="changeType">
                  <i-option v-for="item in typeList" :value="item.value" :key="item.value">
                    {{item.label}}
                  </i-option>
                </i-select>
              </i-form-item>
            </i-col>
            <i-col span="6">
              <i-form-item label="线下费用" class="modal-item">
                <input style="width:12.5vw" onkeyup="this.value=this.value.replace(/[^\d|.]/ig,'')" class="ivu-input ivu-input-default" type="number" min='0' placeholder="请输入线下费用" v-model="activity['price']" :disabled="disabled || disableAll" :clearable="!disabled && !disableAll" />
              </i-form-item>
            </i-col>
          </i-row>
          <div class="add-border-line">
            <div class="border-line"></div>
          </div>
          <i-row>
            <i-col span="12">
              <i-form-item label="活动详情" class="modal-item">
                <i-input style="width:33vw" v-model="activity['description']" type="textarea" :autosize="{minRows:3,maxRows:8}" 
                  placeholder="请输入活动详情..." :disabled="disableAll" :clearable="!disableAll" />
              </i-form-item>
            </i-col>
            <i-col span="12">
              <!-- <i-form-item label="内文图片" class="modal-item">
                <img v-if="activity.innerBannerUrl && activity.innerBannerUrl.indexOf('https')>=0" :src="activity.innerBannerUrl" alt="" style="width:175px;height:100px;">
                <base64-upload style="width:16vw" v-model="activity.innerBannerUrl" :max-size="1" placeholder="点击上传内文图" @size-exceeded="onSizeExceeded" @file="onFile" @load="onLoad" />
              </i-form-item> -->
            </i-col>
          </i-row>
          <div class="add-border-line">
            <div class="border-line"></div>
          </div>
          <i-row>
            <i-col span="6">
              <i-form-item label="报名方式" class="modal-item">
                <i-radio-group class="mr15" v-model="activity.paymentType" v-for="item in paymentList" :key="item.value" @on-change="changePaymentType">
                  <i-radio :label="item.value" :disabled="disableAll">{{item.label}}</i-radio>
                </i-radio-group>
              </i-form-item>
              <i-form-item label="积分消耗" class="modal-item" v-if="activity['paymentType']=='POINT'">
                <i-select style="width:12.5vw" v-model="activity['points']" placeholder="选择积分消耗" :disabled="disableAll" :clearable="!disableAll">
                  <i-option :value="50">50</i-option>
                  <i-option :value="100">100</i-option>
                  <i-option :value="200">200</i-option>
                  <i-option :value="300">300</i-option>
                  <i-option :value="400">400</i-option>
                  <i-option :value="500">500</i-option>
                  <i-option :value="750">750</i-option>
                  <i-option :value="1000">1000</i-option>
                  <i-option :value="1500">1500</i-option>
                </i-select>
              </i-form-item>
            </i-col>
            <i-col span="6">
              <i-form-item label="每场限额" class="modal-item">
                <i-input style="width:12.5vw" v-model="activity['totalStock']" :disabled="disableAll" :clearable="!disableAll" />
              </i-form-item>
            </i-col>
            <i-col span="12">
              <i-form-item label="活动场次" class="modal-item">
                <i-row v-if="session.active" v-for="session in activity['sessions']" :key='session.id'>
                  <i-col span="19" style="margin-bottom:10px">
                    <i-date-picker v-model="session.day" type="date" :options="options" format="yyyy-MM-dd" 
                      style="width: 10vw;margin-right:10px" :readonly="activity.status == 'PUBLISHED' && session.id > 0" />
                    <i-time-picker v-model="session.times" type="timerange" format="HH:mm" 
                      style="width: 12vw" :readonly="activity.status == 'PUBLISHED' && session.id > 0" />
                  </i-col>
                  <i-col span="2" v-if="activity.status != 'PUBLISHED' || (activity.status == 'PUBLISHED' && !session.id)">
                    <i-button class="mr30" icon="md-trash" @click="removeSession(session)">移除</i-button>
                  </i-col>
                </i-row>
                <i-row>
                  <i-col span="19" style="margin-bottom:10px">
                    <i-date-picker v-model="sessionForm.day" type="date" :options="options" format="yyyy-MM-dd" style="width: 10vw;margin-right:10px" clearable />
                    <i-time-picker v-model="sessionForm.times" type="timerange" format="HH:mm" style="width: 12vw" clearable />
                  </i-col>
                  <i-col span="2">
                    <i-button class="mr30" icon="md-add" @click="addSession">添加</i-button>
                  </i-col>
                </i-row>
              </i-form-item>
            </i-col>
          </i-row>
          <div class="mt30 flex row justify-center">
            <i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
            <i-button type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
          </div>
        </i-form>
      </div>
    </i-modal>
    <g-modal v-if="showGalleryDialog" :close="galleryClose" />
  </div>
</template>
<script>
import cfg from '../../config'
import { mapGetters, mapActions } from 'vuex'
import { checkFill, judgeFill, gmtToString, isPositive } from '@/utils/util'
// import VueBase64FileUpload from 'vue-base64-file-upload'

import Modal from '../../gallery/popup'

export default {
  name: 'store_actovity_modal',
  components: {
    'g-modal': Modal,
    // 'base64-upload': VueBase64FileUpload
  },
  props: {
    title: '',
    opFrom: '',
    activityId: '',
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      showDialog: true,
      disabled: false,
      disableAll: false,
      categoryList: [],               // 分类列表

      copySessions: [],
      activity: {
        id: '',               // ID
        name: '',             // 标题
        categoryId: '',       // 分类
        type: '',             // 类型
        target: '',           // 面向人群
        level: '',            // 等级
        price: null,          // 活动费用
        paymentType: 'FREE',  // 报名方式
        points: '',           // 消耗积分点
        description: '',      // 描述
        status: 'UNPUBLISHED',// 状态
        // innerImageType: '',   // 内文图图片格式
        // innerBannerUrl: '',   // 内文图
        publishTime: '',      // 发布时间
        totalStock: '',       // 每场活动名额
        galleryId: '',        // 图库图片ID
        galleryUrl: '',       // 图库图片URL
        parentId: '',
        active: '',           // 是否可见
        isTemplate: '',       // 是否是模板,
        isStar: false,        // 是否精选
        isNationalStar: false,// 是否全国精选
        sessions: []          // 活动场次
      },

      typeList: cfg.typeList,         // 活动类型
      targetList: cfg.targetList,     // 面向人群
      levelList: cfg.levelList,       // 运动等级
      paymentList: cfg.paymentList,   // 报名方式
      statusList: cfg.statusList,     // 状态

      targetListCopy: [],             // 面向人群列表副本
      
      activityRules: {
        name: { value: null, error: false, text: '请输入标题' },
        categoryId: { value: null, error: false, text: '请选择分类' },
        type: { value: null, error: false, text: '请选择类型' },
        target: { value: null, error: false, text: '请选择面向人群' },
        level: { value: null, error: false, text: '请选择运动等级' },
        paymentType: { value: null, error: false, text: '请选择报名方式' },
        description: { value: null, error: false, text: '请填写活动详情' },
        status: { value: null, error: false, text: '请填写活动状态' },
        galleryId: { value: null, error: false, text: '请选择活动主图' }
      },

      options: {
        disabledDate (date) {
            return date && date.valueOf() < Date.now() - 86400000;
        }
      },
      sessionForm: {
        day: '',            // 场次日期
        times: [],          // 开始/结束时间
        active: ''          // 是否可见
      },
      sessionRules: {
        startTime: { value: null, error: false, text: '请填写场次开始时间' },
        endTime: { value: null, error: false, text: '请填写场次结束时间' }
      },

      selectedGallery: '',  // 已选择图片
      showGalleryDialog: false
    }
  },
  mounted() {
    if (this.opFrom == 'template') {
      this.getTemplateDetail(this.activityId)
    } else if (this.activityId) {
      this.getSportsDetail(this.activityId)
    }
    this.loadData()
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  methods: {
    async getTemplateDetail(id) {
      let resp = await this.$api.getTemplateById(id)
      if (resp['status'] == 'success') {
        if (resp.data && resp.data.price) {
          resp.data['price'] = 0
        }
        this.activity = resp.data
        this.activity['parentId'] = this.activity.id
        this.activity['categoryId'] = this.activity.category.id
        this.activity['galleryId'] = this.activity.gallery.id
        this.activity['galleryUrl'] = this.activity.gallery.url
        this.activity['paymentType'] = 'FREE'
        this.activity['status'] = 'UNPUBLISHED'
        delete this.activity.id   // 删除ID属性
        this.disabled = true
      }
    },
    async getSportsDetail(id) {
      let resp = await this.$api.getActivityById(id)
      if (resp['status'] == 'success') {
        // if (resp.data && resp.data.price) {
        //   resp.data['price'] = 0
        // }
        this.activity = resp.data
        this.activity['categoryId'] = this.activity.category.id
        this.activity['galleryId'] = this.activity.gallery.id
        this.activity['galleryUrl'] = this.activity.gallery.url
        this.disableAll = this.activity.status == 'PUBLISHED' ? true : false
      }
    },

    // 初始化数据
    loadData() {
      this.getCategories()
      this.targetListCopy = [...this.targetList]
    },
    // 获取分类列表
    async getCategories() {
      let resp = await this.$api.getSportsCategories()
      this.categoryList = resp.data
    },
    changeCategory(option) {
      if ((!this.selectedGallery && option != this.activity.category.id) 
        || (this.selectedGallery && this.selectedGallery.categoryId != this.activity.categoryId)) 
      {
        this.$message.warning({ content: '运动分类与模板主图分类不一致', closable: true, duration: 3 })
        this.activity.categoryId = this.activity.category.id
      }
    },
    // 改变类型(亲子类型, 人群只有儿童和青少年)
    changeType(option) {
      this.activity.target = ''
      if (option === 'PARENT_CHILD') {
        this.targetListCopy = this.targetList.filter(target => target.value != "ADULT" && target.value != "FULL")
      } else {
        this.targetListCopy = [...this.targetList]
      }
    },
    changePaymentType(value) {
      if (value == 'FREE') {
        this.activity.points = 0
      }
    },
    // 选择图片框
    popupGalleryChoose() {
      this.showGalleryDialog = true
    },
    // 图片框关闭回调事件
    galleryClose(item) {
      this.selectedGallery = item
      if (item) {
        this.activity.galleryId = item.id
        this.activity.galleryUrl = item.url
      }
      this.showGalleryDialog = false
    },
    // 内文图
    // onFile(file) {
    //   let fileName = file.name
    //   this.activity.innerImageType = fileName.substring(fileName.lastIndexOf('.'))
    // },
    // onLoad(dataUri) {
    //   this.activity.innerBannerUrl = dataUri.substring(dataUri.indexOf('base64,')+7)
    // },
    // onSizeExceeded(size) {
    //   this.$message.warning({ content: `图片大小${size}MB超出1MB限制!`, closable: true, duration: 3 })
    // },
    // 添加活动时间
    addSession() {
      if (this.sessionForm.day && this.sessionForm.times
        && this.sessionForm.times.length == 2 
        && this.sessionForm.times[0] && this.sessionForm.times[1]) 
      {
        if (this.activity.sessions === undefined 
          || this.activity.sessions.length == 0) {
          this.$set(this.activity, 'sessions', [])
        }
        if (this.activity.sessions.filter(s => s.active).length < 5) {
          let objc = {}
          for (let key in this.sessionForm) {
            if (key != 'day') {
              objc[key] = this.sessionForm[key]
            }
          }
          objc['day'] = gmtToString(this.sessionForm.day)
          objc['active'] = true
          this.activity.sessions.push(objc)
          this.sessionForm.day = ''
        } else {
          this.$message.warning({ content: '最多有5场未开始的活动场次', closable: true, duration: 3 })
          this.sessionForm = {}
        }
      }
    },
    // 移除活动时间
    removeSession(session) {
      session.active = false
    },
    // 新建/更新门店活动
    async certainClick() {
      // 添加最后session
      this.addSession()
      this.activity.category = { id: this.activity.categoryId  }
      this.activity.gallery = { id: this.activity.galleryId }
      this.activity.store = this.currentStore
      this.activity.isTemplate = false
      this.activity.active= true
      if (this.activity.name.length > 10) {
        this.$message.warning({ content: '活动名称须10字以内', closable: true, duration: 3 })
        return
      }
      if (this.activity.galleryId) {
        if (this.selectedGallery && this.activity.category.id != this.selectedGallery.categoryId) {
          this.$message.warning({ content: '运动分类与活动主图分类不一致', closable: true, duration: 3 })
          return
        }
      } else {
        this.$message.warning({ content: '请选择活动主图', closable: true, duration: 3 })
        return
      }
      if (!isPositive(this.activity.totalStock)) {
        this.$message.warning({ content: '每场限额必须为正数', closable: true, duration: 3 })
        return
      }
      if (this.activity.paymentType == 'POINT' && !isPositive(this.activity.points)) {
        this.$message.warning({ content: '积分消耗必须为正数', closable: true, duration: 3 })
        return
      }

      let sessions = this.activity.sessions
      let gtmSessions = []
      if (sessions) {
        sessions.forEach(session => {
          let objc = {}
          for (let key in session) {
            if (key != 'day') {
              objc[key] = session[key]
            }
          }
          objc['day'] = gmtToString(session.day)
          gtmSessions.push(objc)
        })
      }
      let copyActivity = Object.assign({}, this.activity)
      copyActivity.sessions = gtmSessions
      
      let flag = true
      for(let i=0; i < copyActivity.sessions.length && flag; i++) {
        let ss = copyActivity.sessions[i]
        if(!ss.day || !ss.times || (ss.times && (!ss.times[0] || !ss.times[1]))) {
          flag = false
          this.$message.warning({ content: '请确认活动场次填写完整', closable: true, duration: 3 })
        }
      }
      if (!flag) {
        return
      }
      if (copyActivity.sessions.length == 0) {
        this.$message.warning({ content: '请填写活动场次', closable: true, duration: 3 })
        return
      } else if (copyActivity.sessions.filter(s => s.active).length > 5) {
        this.$message.warning({ content: '最多有5场未开始的活动场次', closable: true, duration: 3 })
        return
      }
      let resp = await this.$api.createOrUpdateActivity(copyActivity)
      if (resp.status === 'success') {
        this.certain && this.certain()
      }
    },
    cancelClick() {
      this.showDialog = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
  }
}
</script>

<style lang="less" scoped>
.modal-box {
  width: 100vw;
}
.modal-content-box {
  width: 95vw;
  margin:0 auto;
  padding-top: 30px;
  form {
    width: 85vw;
    margin:0 auto;
    box-sizing: border-box!important;
  }
  .add-border-line {
    display: flex;
    justify-content: center;
    margin: 0 auto;
    text-align: center;
  }
  .border-line {
    width: 80vw;
    text-align: right;
    margin: 0 0 20px;
    border: 1px dashed #eee;
  }
}
</style>
