/**
 * 动态路由处理方法
 */
import { session } from '@/utils/util'

// 存入本地
const saveRoutes = (value) => {
    session.setStorage('ROUTES', value)
}

// 去除路由
const getRoutes = () => {
    return session.getStorage('ROUTES')
}

// 将后台路由数据格式化
const formatRoutes = (value) => {
    let routes = value.filter(route => {
        if (route.component) {
            // 路由导出
            route.component = import(`@/pages/'${route.component}`)
        }
        // 通过递归处理
        if (route.children && route.children.length) {
            route.children = formatRoutes(route.children)
        }
        return true
    })
    return routes
}

// 将路由处理成菜单所需格式
const formatMenus = (value) => {
    
}

export default {
    getRoutes,
    saveRoutes,
    formatRoutes,
    formatMenus
 }