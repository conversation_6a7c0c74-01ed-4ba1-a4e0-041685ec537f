// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import './config/iviewConfig'
import './css/themes/index.less'
import './css/layout/index.less'

import { Modal, Message } from 'iview'
import Vue from 'vue'
import './plugins/vuex'
import i18n from './plugins/i18n'
import store from './store'
import api from './api'
import { local, session } from './utils/util'
import directives from './directives/index'
import App from './App'
import router from './router'


// 全局挂载
Vue.prototype.$api = api
Vue.prototype.$local = local
Vue.prototype.$session = session
Vue.prototype.$modal = Modal
Vue.prototype.$message = Message
// 生产环境下是否提示警告
Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  components: { App },
  template: '<App/>'
})
