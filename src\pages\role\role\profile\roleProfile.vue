<template>
  <div class="role">
    <div class="w90 text-left">
      <i-icon 
        type="ios-undo" 
        class="f22"
        @click="returnPage"
      />
    </div>
    <div class="f22 w50 text-left mb30">添加角色</div>
    <div class="content">
      <i-row class="row mb30"> 
        <i-col span="4" class="text-left">
          sss
        </i-col>
        <i-col span="20">
          <i-input class="common-input" size="large" placeholder="Enter something..."/>
        </i-col>
      </i-row>
      <i-row class="row mb30"> 
        <i-col span="4" class="text-left">
          ss
        </i-col>
        <i-col span="20">
          <!-- <div class="" v-model="fruit" v-for="item in profile"> -->
            <div>
            <i-col span="12" class="checkbox-item">
              <i-checkbox></i-checkbox>
            </i-col>
            <i-col span="12">kk</i-col>
          </div>
        </i-col>
      </i-row>
      <i-row>
        <i-button type="primary" @click="handleSubmit('formCustom')">Confirm</i-button>
          <i-button @click="handleReset('formCustom')" style="margin-left: 8px">Cancle</i-button>
      </i-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Role',
  data () {
    return {
      cityList: [
          {
              value: 'New York',
              label: 'New York'
          },
          {
              value: 'London',
              label: 'London'
          },
          {
              value: 'Canberra',
              label: 'Canberra'
          }
      ],
      model1: '',
      profile: [
        {
          label: 'AAA',
          value: 1
        },
        {
          label: 'BBB',
          value: 2
        },
        {
          label: 'CCC',
          value: 3
        },
        {
          label: 'DDD',
          value: 4
        },
        {
          label: 'EEE',
          value: 5
        }
      ],
      fruit: []
    }
  },
  methods: {
    returnPage() {
      
    }
  }
}
</script>

<style scoped>
  .f22{
    font-size: 22px;
  }
  .w90{
    width: 90vw;
    margin: 0 auto;
  }
  .w50{
    width: 50vw;
    margin: 0 auto;
  }
  .text-left{
    text-align: left
  }
  .content {
    width:50%;
    margin: 0 auto;
    padding: 10vh 8vw;
    border: 1px solid #ddd;
  }
  .mb30{
    margin-bottom:30px
  }
  .row{
    display: flex;
    align-items: center;
  }
  .checkbox-item {
    margin-bottom: 10px;
  }
</style>