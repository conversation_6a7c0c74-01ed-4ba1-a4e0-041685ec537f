<template>
  <div class="container modal-box">
    <i-modal footer-hide width="600px" :closable="false" :title="title" v-model="showDialog">
      <div class="modal-content mt30 flex column mr30">
        <i-form :model="sessionCancel" label-position="right" :label-width="100">
          <i-input v-model="sessionCancel.id" v-show="false" />
					<i-form-item label="活动" class="modal-item">
						<i-input v-model="sessionCancel.name" :disabled="true" />
					</i-form-item>
					<i-form-item label="场次" class="modal-item">
						<i-input v-model="sessionCancel.value" :disabled="true" />
					</i-form-item>
					<i-form-item label="取消原因" class="modal-item">
						<i-select v-model="sessionCancel.reason" placeholder="取消原因" clearable  >
							<i-option :value="item" v-for="item in reasonList" :key="item">
								{{item}}
							</i-option>
						</i-select>
					</i-form-item>
					<i-form-item label="取消备注" class="modal-item">
					<i-input v-model="sessionCancel.comments" type="textarea" 
						:autosize="{minRows:3,maxRows:8}" placeholder="请输入取消备注" clearable />
					</i-form-item>
					<div class="mt30 flex row justify-center">
						<i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
						<i-button type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
					</div>
				</i-form>
      </div>
    </i-modal>
  </div>
</template>

<script>
export default {
  name: 'session_cancel_modal',
  props: {
    title: '',
    cancelForm: {
			id: '',
			name: '',
			value: ''
		},
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
			showDialog: true,
			reasonList: [
				'天气原因',
				'场地原因',
				'其他原因'
			],
			sessionCancel: {
				id: '',
				name: '',
				value: '',
				reason: '',
				comments: ''
			}
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 初始化数据
    loadData() {
			this.sessionCancel = this.cancelForm
      this.showDialog = true
    },

    certainClick() {
			if (this.sessionCancel.reason 
				&& this.sessionCancel.comments) {
				this.$modal.confirm({
					'title': '门店活动取消',
					'content': `您即将取消<span class='confirm-msg'>${this.cancelForm.name}的${this.cancelForm.value}</span>场次的活动.<br>确认取消后将通知已报名用户!`,
					'okText': '确定',
					'cancelText': '取消',
					'onOk': () => {
						this.cancelSportsSession(this.sessionCancel)
					}
        })
			} else {
				this.$modal.warning({
          'title': '提示',
          'content': '请选择正确选择取消原因及备注'
        })
			}
		},
		async cancelSportsSession(sessionCancel) {
			let resp = await this.$api.cancelSportsSession(this.sessionCancel)
			if (resp.status == 'success') {
				this.certain && this.certain()
			}
		},
    cancelClick() {
			this.showDialog = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
  }
}
</script>
<style lang="less" scoped>
.modal-box {
	.dropdown {
		width: 200px;
	}

	.modal-content {
		width: 100%;
		.modal-item {
			width: 500px;
		}
	}
}
</style>