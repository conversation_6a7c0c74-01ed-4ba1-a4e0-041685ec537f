<template>
  <div class="edit-award-wrap">
    <div class="content-wrap">
      <i-form ref="formValidate" class="form-wrap" :model="formValidate" :rules="ruleValidate" :label-width="120">
        <i-form-item label="编号：" class="card-path">
          <p>{{ position }}</p>
        </i-form-item>
        <i-form-item label="Model code：" prop="modelCode" class="card-path">
          <i-input :disabled="disableEditInEffect" v-model="formValidate.modelCode" placeholder="请输入Model code"
            @on-enter="showSearchModal"></i-input>
          <i-button :disabled="disableEditInEffect" type="primary" @click="showSearchModal">
            <i-icon type="ios-search" size="18"></i-icon>
          </i-button>
        </i-form-item>
        <i-form-item label="Item code：" prop="itemCode" class="card-path">
          <i-input :disabled="disableEditInEffect" v-model="formValidate.itemCode" placeholder="请输入Item code"></i-input>
        </i-form-item>
        <i-form-item label="奖品名称：" prop="name" class="card-title">
          <div class="rule-input">
            <i-input v-model="formValidate.name" :maxlength="nameMaxLength" placeholder="请输入奖品名称"></i-input>
            <p class="text-count">{{ formValidate.name.length }}/{{ nameMaxLength }}</p>
          </div>
        </i-form-item>
        <i-form-item label="奖品图：" prop="imageUrl" class="upload-item">
          <upload-img ref="uploadImg" :imageUrl="formValidate.imageUrl" @updateImageUrl="updateBgImage"
            :pixelLimit="bgPixelLimit" :sizeLimit="bgSizeLimit" />
        </i-form-item>
        <i-form-item label="价格：" prop="price" class="card-path">
          <i-input v-model="formValidate.price" placeholder="请输入价格" @on-keyup="handlePriceEdit($event)" ></i-input>
        </i-form-item>
        <i-form-item label="奖品简介：" prop="description" class="activity-rule">
          <div class="rule-input">
            <i-input v-model="formValidate.description" type="textarea" :rows="4" :maxlength="descriptionMaxLength"
              show-word-limit placeholder="请输入奖品简介"></i-input>
            <p class="text-count">{{ formValidate.description.length }}/{{ descriptionMaxLength }}</p>
          </div>
        </i-form-item>
        <i-form-item label="" prop="prizeQuantity" class="card-path">
          <span slot="label">
            奖品数量
            <i-tooltip content="该数量为手机端展示的奖品数量说明" placement="top-start" :offset="-15">
              <i-icon type="ios-information-circle-outline" :size="14"></i-icon>
            </i-tooltip>：
          </span>
          <i-input :disabled="disableEditInEffect" v-model="formValidate.prizeQuantity" @on-keyup="handlePrizeQuantityEdit($event)"
            placeholder="请输入奖品数量"></i-input>
        </i-form-item>
        <div class="bottom-bar">
          <i-button type="primary" class="save" @click="handleSubmit('formValidate')">保存</i-button>
          <i-button class="cancel" @click="handleCancel">取消</i-button>
        </div>
      </i-form>
    </div>
    <!-- 搜索 modelcode Modal -->
    <search-modal-code ref="searchModal" :visible="searchModalVisible" :title="title"
      :modelCode="formValidate.modelCode" @certain="modalCertain" @cancel="modalCancel"></search-modal-code>
  </div>
</template>

<script>
import uploadImg from '@/components/uploadImg/uploadImg';
import searchModalCode from '@/components/searchModelCode/index';
export default {
  name: 'editSingleAward',
  components: {
    'upload-img': uploadImg,
    'search-modal-code': searchModalCode
  },
  computed: {
    // 生效中活动 model item 奖品数量信息不可编辑
    disableEditInEffect() {
      const { pageType, activityStatus } = this.pageInfo;
      const isEditInEffect = activityStatus === 'IN_EFFECT' && pageType === 'edit';
      return isEditInEffect;
    },
  },
  data() {
    return {
      title: '查找产品',
      position: 0,
      curAwardIndex: '',
      searchModalVisible: false,
      bgPixelLimit: [550,550], 
      pageInfo: {},
      bgSizeLimit: 2,
      formValidate: {
        modelCode: '',         // Model code
        itemCode: '',          // Item code
        name: '',         // 奖品名称
        imageUrl: '',             // 奖品图
        price: '',             // 价格
        description: '',      // 奖品简介
        prizeQuantity: '',          // 奖品数量
      },
      descriptionMaxLength: 120,
      nameMaxLength: 10,
      ruleValidate: {
        modelCode: [
            { required: true, message: '请填写Model code', trigger: 'blur' }
        ],
        itemCode: [
            { required: true, message: '请填写Item code', trigger: 'blur' }
          ],
        name: [
            { required: true, message: '请填写奖品名称', trigger: 'blur' },
            { type: 'string', max: 10, message: '奖品名称不得超过10个字符', trigger: 'blur,change' }
        ],
        imageUrl: [
            { required: true, message: '请上传奖品图', trigger: 'blur' }
        ],
        price: [
            { required: true, message: '请填写价格', trigger: 'blur' },
        ],
        description: [
            { required: true, message: '请填写奖品简介', trigger: 'blur' },
            { type: 'string', max: 120, message: '奖品简介不得超过120个字符', trigger: 'blur,change' }
        ],
        prizeQuantity: [
            { required: true, message: '请输入奖品数量', trigger: 'blur' }
        ],
      }
    };
  },
  created(op) {
    const positionId = this.$route.params.id
    console.log('>>>>>singleAward created', positionId)
    this.position = positionId;
    // 回显当前奖品信息
    this.pageInfo = this.$local.getStorage('PAGE_INFO') || {};
    let { awardActivityInfo } = this.pageInfo;
    this.curAwardIndex = awardActivityInfo.productInfoList.findIndex(item => {
      if(item.price === null || item.price === 'null'){
        item.price = ''
      } else {
        item.price += '';
      }
      if(item.prizeQuantity === null || item.prizeQuantity === 'null'){
        item.prizeQuantity = ''
      } else {
        item.prizeQuantity += '';
      }
      return (item.position+'') === this.position;
    });
    if( this.curAwardIndex < 0 ) { return; }
    let curFormData = awardActivityInfo.productInfoList[this.curAwardIndex];
    this.formValidate = JSON.parse(JSON.stringify(curFormData));
  },
  methods: {
    // ...mapActions(['setAwardActivityInfo']),
    showSearchModal() {
      this.searchModalVisible = true;
    },
    modalCertain(obj) {
      const { modelCode, selectedProduct } = obj;
      this.formValidate.modelCode = modelCode;
      this.closeDialog()
      // 解析当前选择的产品信息
      let { itemCode, title, imageUrls, price , description } = selectedProduct;
      this.formValidate.itemCode = itemCode;
      this.formValidate.name = title;
      this.formValidate.imageUrl = imageUrls && imageUrls.length > 0 ? imageUrls[0] : '';
      this.$refs.uploadImg.updateCurImgUrl(this.formValidate.imageUrl);
      this.formValidate.price = price + '';
      this.formValidate.description = description;
      this.$refs["formValidate"].validateField('imageUrl');
    },
    modalCancel() {
      this.closeDialog()
    },
    closeDialog() {
      this.searchModalVisible = false;
    },
    handlePriceEdit(ev) {
      let val = ev.target.value;
      // 只允许输入数字和小数点
      val = val.replace(/[^\d.]/g, '');
      // 只允许一个小数点
      const parts = val.split('.');
      if (parts.length > 2) {
        val = `${parts[0]}.${parts.slice(1).join('')}`;
      }
      // 限制小数点后1位
      let value = val.toString();
      if (value.includes('.')) {
        const parts = value.split('.');
        if (parts[1].length > 1) {
          val = `${parts[0]}.${parts[1].substring(0, 1)}`;
        }
      }
      this.formValidate.price = val;
    },
    handlePrizeQuantityEdit(ev) {
      let val = ev.target.value;
      this.formValidate.prizeQuantity = val.replace(/\D/g, '');
    },
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          // 验证价格必须带一位小数
          const { price } = this.formValidate;
          if (!/^\d+\.\d$/.test(price)) {
            this.$message.error('价格需保留1位小数');
            return;
          }
          // 更新localstorage里存储的奖品信息
          let pageInfo = this.$local.getStorage('PAGE_INFO') || {};
          let { pageType, id, activityStatus, awardActivityInfo } = pageInfo;
          // 根据索引更新 awardActivityInfo.productInfoList
          awardActivityInfo.productInfoList[this.curAwardIndex] = this.formValidate;
          this.$local.setStorage('PAGE_INFO', {
            pageType,
            id,
            activityStatus,
            actionType: 'updateAwardActivityInfo',
            awardActivityInfo: {
              productInfoList: awardActivityInfo.productInfoList || [],
              formValidate: awardActivityInfo.formValidate || {}
            }
          })
          // 回到奖品配置页面，并加载存储的奖品活动配置页面
          this.$router.replace(`/luckyDraw/awardActivityConfig`);
        }
      })
    },
    updateBgImage(url) {
      this.formValidate.imageUrl = url;
      this.$refs["formValidate"].validateField('imageUrl');
    },
    handleCancel() {
      this.$router.replace({ path: '/luckyDraw/awardActivityConfig' });
    }
    
  },
  
  mounted() {
  },
};
</script>

<style scoped lang="less">
.edit-award-wrap {
  background: #FFFFFF;
  /deep/.content-wrap{
    padding: 24px 10px;
    .ivu-form{
      .ivu-form-item-label:before {
        font-size: 14px; 
      }
      .activity-rule, .card-title{
        .rule-input {
          position: relative;
          width: 380px;
          .text-count{
            position: absolute; right: 3px; bottom: -16px;
            display: inline-block;
            height: 16px; line-height: 16px;
            padding: 0 4px;
            background: #D9D9D9;
            color: #FFFFFF; font-size: 9px; 
          }
        }
      }
      .card-title, .card-path{
        .rule-input{
          width: 300px;
        }
        .ivu-input-wrapper {
          width: 300px;
        }
      }
      .bottom-bar{
        .save{ margin-left: 16px; }
        .cancel{ margin-left: 16px; }
      }
    }
  }
  
    
}
</style>