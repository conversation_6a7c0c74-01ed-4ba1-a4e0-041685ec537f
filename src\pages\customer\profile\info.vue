<template>
	<div class="customer_info">
		<i-modal :mask-closable="false" @on-cancel="cancelClick" footer-hide title="更新用户信息" v-model="show">
			<div class="mt10 mb15 row item-center justify-between">
				<i-form :model="customerInfo">
					<div class="row item-center">
						<div class="modal-item">姓</div>
						<div class="customer-text">
							<i-input :maxlength="10" placeholder="姓" v-model="customerInfo.dktLastName"/>
						</div>
					</div>
					<div class="row item-center mt15">
						<div class="modal-item">名</div>
						<div class="customer-text">
							<i-input :maxlength="10" placeholder="名" v-model="customerInfo.dktFirstName"/>
						</div>
					</div>
					<div class="row item-center mt15">
						<div class="modal-item">生日</div>
						<div class="customer-text">
							<i-date-picker
								@on-change="changeBirthday"
								class="customer-text"
								format="yyyy-MM-dd"
								type="date"
								v-model="birthday"
							/>
						</div>
					</div>
					<div class="row item-center mt15">
						<div class="modal-item">性别</div>
						<div class="customer-text">
							<i-radio-group v-model="customerInfo.gender">
								<i-radio :key="item.value" :label="item.value" v-for="item in genderList">{{item.label}}</i-radio>
							</i-radio-group>
						</div>
					</div>
					<div class="row item-center mt15">
						<div class="modal-item">会员卡号</div>
						<div class="customer-text">
							<i-input :disabled="true" v-model="customerInfo.dktCardNo"/>
						</div>
					</div>
					<div class="row item-center mt15">
						<div class="modal-item">偏好门店</div>
						<div>
							<i-cascader
								:data="cascaderStoreList"
								@on-change="storeChange"
								class="customer-text"
								filterable
								placeholder="请选择门店"
								v-model="store"
							/>
						</div>
					</div>
					<div class="row item-center mt15">
						<div class="modal-item">电子邮箱</div>
						<div>
							<i-input class="customer-text" placeholder="邮箱" v-model="customerInfo.dktEmail"/>
						</div>
					</div>
					<div class="row item-center mt15">
						<div class="modal-item">手机号码</div>
						<div>
							<i-input :disabled="true" class="customer-text" v-model="customerInfo.dktMobile"/>
						</div>
					</div>
					<div class="row item-center mt15">
						<div class="modal-item"></div>
						<div class="customer-text">
							<i-checkbox v-model="customerInfo.dktOptin">订阅迪卡侬最新资讯和活动通知</i-checkbox>
						</div>
					</div>
				</i-form>
			</div>
			<div class="mt30 flex row justify-center">
				<i-button @click="cancelClick" class="mr30" shape="circle" style="width: 100px">取消</i-button>
				<i-button @click="certainClick" shape="circle" style="width: 100px" type="primary">确认</i-button>
			</div>
		</i-modal>
	</div>
</template>
<script>
import cfg from '../config'
export default {
  name: 'customer_info',
  props: {
    customer: {
      id: '',
      dktPersonId: '',
      dktFirstName: '',
      dktLastName: '',
      gender: 1,
      dktCardNo: '',
      dktMobile: '',
      dktEmail: '',
      dktStoreNumber: '',
      dktBirthday: '',
      dktOptin: ''
    },
    storeList: {
      type: Array,
      default: () => []
    },
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      show: true,
      store: [],
      cascaderStoreList: [],
      customerInfo: {},
      birthday: '',
      tempBirthday: '',
      genderList: cfg.genderList
    }
  },
  mounted() {
    this.birthday = this.customer.dktBirthday
    this.customerInfo = Object.assign({}, this.customer)
    const currentStore = this.findPerferStore(this.storeList)
    this.store = [encodeURI(currentStore.city), currentStore.dktStoreNumber]
    this.cascaderStoreList = this.handleStoreList(this.storeList)
  },
  methods: {
    // 取消
    cancelClick() {
      this.show = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    },

    changeBirthday(value, type) {
      if (value) {
        this.tempBirthday = value
      }
    },

    storeChange(e) {
      this.customerInfo.dktStoreNumber = e[1]
    },

    validateEmail(email) {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailRegex.test(email);
    },
    /**
     * 网络请求
     */
    // 更新用户信息
    async certainClick() {
      const dktEmail = this.customerInfo.dktEmail || '';
      if(dktEmail) {
        let isEmailValid = this.validateEmail(dktEmail);
        if (!isEmailValid) {
          this.$message.error('请输入正确的电子邮箱');
          return;
        }
      }

      this.customerInfo['genderCn'] = cfg.convert(
        this.customerInfo.gender,
        cfg.genderList
      )
      if (this.tempBirthday) {
        this.customerInfo.dktBirthday = this.tempBirthday
      }
      let resp = await this.$api.updateCustomerInfo(this.customerInfo)
      if (resp['status'] == 'success') {
        this.certain && this.certain(this.customerInfo)
      }
    },

    /**
     * 数据处理
     */
    // 处理cascader所需数据
    handleStoreList(list) {
      let cascaderArr = []
      // 过滤,去重城市
      let cities = []
      for (let store of list) {
        let city = store.city
        if (cities.indexOf(city) === -1) {
          cities.push(city)
        }
      }
      // 进行数据组合
      for (let city of cities) {
        let objc = {
          label: city,
          value: encodeURI(city),
          children: []
        }
        for (let store of list) {
          store['label'] = store.name
          store['value'] = store.dktStoreNumber
          if (city === store.city) {
            objc.children.push(store)
          }
        }
        cascaderArr.push(objc)
      }
      return cascaderArr
    },
    findPerferStore(list) {
      if (list && list.length <= 0) return {}
      let perferStore = null
      perferStore = list.find(item => {
        if (item.dktStoreNumber === this.customer.dktStoreNumber) {
          return item
        }
      })
      return perferStore || {}
    }
  }
}
</script>
<style lang="less" scoped>
.modal-item {
  width: 100px;
  margin-right: 30px;
  text-align: right;
}

.customer-text {
  width: 300px;
}
</style>