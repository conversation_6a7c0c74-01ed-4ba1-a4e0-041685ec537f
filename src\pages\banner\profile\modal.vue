<template>
  <div class="container">
    <i-modal :closable="false" :mask-closable="false" :title="title" footer-hide v-model="show" width="700">
      <div class="modal-content mt30 flex column">
        <i-row>
          <i-col offset="2" span="20">
            <i-form class="form-style" :label-width="120" label-position="right" span="12"  ref="formValidate" :model="formValidate" :rules="ruleValidate">
              <div class="hover-img" @mouseover="handleMouseOver"  @mouseout="handleMouseOut" >
                <img class="hover-img-icon" src="https://ccbdatprdcne2.blob.core.chinacloudapi.cn/dktcnecmp-static/mp-bo/Info-circle.svg"  alt=""/>
                <img :style="{ display: isTipImgHovered ? 'block' : 'none'}" class="tip-img" :src="modalHoverImg"  alt=""/>
              </div>
              <i-form-item class="modal-item banner-input-item" label="名称" prop="name">
                <i-input placeholder="请输入名称" :maxlength="inputMaxLength" v-model="formValidate.name" />
                <div class="banner-input-tip">{{ formValidate.name.length }} / {{ inputMaxLength }}</div>
              </i-form-item>

              <i-form-item class="modal-item display-img" :label="imgLabelTitle"  prop="file" v-if="">
                <upload-img :disabledStatus="false" :imageUrl="formValidate.file" :upLoadUrlType="'banner'" ref="uploadImgDisplay"
                            @updateImageUrl="updateBgImage" :pixelLimit="bgPixelLimit" :needPixelCheck="false" :sizeLimit="bgSizeLimit" />
              </i-form-item>

              <i-form-item v-if="activitySubType !== '3' && activitySubType !== '8'" class="modal-item" label="跳转页面目标类型:" prop="typeId">
                <i-select class="mr15" v-model="formValidate.typeId">
                  <i-option :key="item" :value="item" v-for="item in typeIdList">{{modalSelectRedirectType[item]}}</i-option>
                </i-select>
              </i-form-item>
              <i-form-item v-if="activitySubType !== '3' && activitySubType !== '8'" class="modal-item" label="跳转页面目标地址:" prop="typeUrl">
                <i-input placeholder="请输入地址" v-model="formValidate.typeUrl" />
              </i-form-item>
              <i-form-item class="modal-item" label="生效方式:" v-if="wayDisplay" prop="way">
                <i-radio-group v-model="formValidate.way">
                  <i-radio :disabled="wayDisabled" label="立即" key="1">
                    <span>立即</span>
                  </i-radio>
                  <i-radio :disabled="wayDisabled" label="定时" key="2">
                    <span>定时</span>
                  </i-radio>
                </i-radio-group>
              </i-form-item>
              <i-form-item v-if="dateDisplay" class="modal-item" label="生效时间:" prop="time">
                <i-row>
                  <i-date-picker :loading="false" :disabled="dateDisabled" :options="optionsRules" v-model="formValidate.time" type="datetimerange" format="yyyy-MM-dd HH:mm" placeholder="请输入时间" style="width: 300px"></i-date-picker>
                </i-row>
              </i-form-item>
              <i-form-item v-if="endDateDisplay" class="modal-item" label="生效时间:" prop="time">
                <i-date-picker disabled="true" v-model="endDateTime" type="datetime" format="yyyy-MM-dd HH:mm" placeholder="Select date" style="width: 200px"></i-date-picker>
              </i-form-item>
            </i-form>
          </i-col>
        </i-row>
        <div class="modal-items mt30 flex row modal-items-end">
          <i-button @click="cancelClick" class="mr30">{{ modalDisplaySaveBtn ? '取消' : '关闭' }}</i-button>
          <i-button v-if="modalDisplaySaveBtn"  @click="certainClick('formValidate')" type="primary">发布</i-button>
        </div>
      </div>
    </i-modal>
  </div>
</template>

<script>
import uploadImg from '@/components/uploadImg/uploadImg';
import { modalSelectRedirectType } from '../config/mapping'
export default {
  name: 'gallery_modal',
  components: {
    'upload-img': uploadImg,
  },
  props: {
    title: '',
    id: '',
    certain: {
      type: Function,
      default: () => {}
    },
    modalDisplaySaveBtn: {
      type: Boolean,
      default: true
    },
    activitySubType: {
      type: String,
      default: ''
    },
    activityType: {
      type: String,
      default: '',

    },
    modalHoverImg: {
      type: String,
      default: ''
    },
    cancel: {
      type: Function,
      default: () => {}
    },
    modalStatus: {
      type: Object,
      default: { status: '',  btn: '' },
    },
    size: {
      type: Array,
      default:  () => [500, 750]
    },
  },
  data() {
    return {
      inputMaxLength: 50,
      bgPixelLimit: [this.size[0], this.size[1]],
      bgSizeLimit: 2,
      idDetail: '',
      imgLabelTitle: `图片 . . .`,
      // form data
      wayDisabled: false, // 跳转方式 单选disable
      wayDisplay: true,  // 跳转方式 display
      dateDisabled: false, // 定时日期 disable
      dateDisplay: true, // 定时日期 display
      endDateDisplay: false, // 特殊日期
      endDateTime: '',// 特殊日期
      optionsRules: {
        disabledDate (date) {
          return date && date.valueOf() < Date.now();
        }
      },
      formValidate: {
        name: '',
        typeId: '',
        typeUrl: '',
        file: '',
        time: [],
        way: '',
      },
      ruleValidate: {
        name: [
          { required: true, message: '请输入名字', trigger: 'blur' }
        ],
        typeId: [
          { required: false, },
        ],
        typeUrl: [
          { required: false, }
        ],
        file: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        time: [
          { required: true, type: 'array', min: 2, message: '请选择一个时间范围', trigger: 'change' },
          { validator: this.validateDateTimeRange, trigger: 'change' }
        ],
        way: [
          { required: true, message: '请选择发布方式', trigger: 'change' }
        ],

      },
      // modal data
      show: true,
      typeIdList: [],
      isTipImgHovered: false,
      modalSelectRedirectType: modalSelectRedirectType
    }
  },
  mounted() {
    this.queryPageTypeApi()
  },
  watch: {
    'show': { // 监听 $route 对象的变化
      immediate: true,
      handler() {
        this.dateDisplay = true
        this.wayDisplay = true
      },
    },
    'formValidate.way': {
      immediate: true,
      handler(newVal) {
        this.dateDisplay = newVal !== '立即';
      },
    }
  },
  methods: {
    validateDateTimeRange(rule, value, callback) {
      if (this.dateDisabled) return
      if (value && value.length === 2) {
        const [startTime, endTime] = value;
        if (this.modalStatus === '生效中' && startTime < new Date()) {
          callback(new Error('开始时间不能早于当前时间'));
        } else if (startTime && endTime && startTime < endTime) {
          callback();
        } else {
          callback(new Error('结束时间必须晚于开始时间'));
        }
      } else {
        callback(new Error('请选择一个时间范围'));
      }
    },
    timeFun(date) {
      if (!date) return null
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    async certainClick() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.callApi()
        }
      })
    },
    async callApi() {
      const data = this.formValidate
      let postData = {
        activitySubType: Number(this.activitySubType),
        activityType: "BANNER",
        id: this.id,
        activityDetail: {
          configDto: {
            id: this.idDetail,
            activityEffectType: data.way === '立即' ? 'NOW' : 'TIMING',
            activityName: data.name,
            imageUrl: data.file,
            extendInfo: ['3', '8'].includes(this.activitySubType) ? null : JSON.stringify({
              actionType: data.typeId,
              actionUrl: data.typeUrl
            })
          }
        }
      }
      if (data.time && data.time.length > 1) {
        postData.activityDetail.configDto.planEndTime =  this.timeFun(data.time[1])
        postData.activityDetail.configDto.planEffectTime =  this.timeFun(data.time[0])
      }
      const resq = await this.$api.saveDetailData(postData)
      if (resq.status === 'success') {
        this.certain()
        this.$message.success({
          content: '保存成功',
          closable: false,
        });
      }
    },
    // 取消
    cancelClick() {
      this.show = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    },
    async queryPageTypeApi() {
      let resp = await this.$api.queryPageType()
      this.typeIdList = resp.data
      this.formValidate.typeId = resp.data[0]
      if (this.id) {
        this.getDetailDataApi()
      }
    },
    async getDetailDataApi() {
      this.wayDisabled = false
      const resa = await this.$api.getDetailData(this.id)
      if (resa.status === 'success') {
        const urlData = JSON.parse(resa.data.configDto.extendInfo || '{}')
        const planEndTime = resa.data.configDto.planEndTime
        const planEffectTime = resa.data.configDto.planEffectTime
        this.formValidate.name = resa.data.configDto.activityName
        this.formValidate.typeId = urlData.actionType || ''
        this.formValidate.typeUrl = urlData.actionUrl || ''
        this.formValidate.way = resa.data.configDto.activityEffectType === 'NOW' ? '立即': '定时'
        this.formValidate.file = resa.data.configDto.imageUrl
        this.idDetail = resa.data.configDto.id
        this.updateModalDisplay(resa.data.configDto.activityEffectType, planEffectTime, planEndTime);
        this.$refs.uploadImgDisplay.updateCurImgUrl(resa.data.configDto.imageUrl)
      }
    },
    updateModalDisplay(effectType, planEffectTime, planEndTime) {
      if (this.modalStatus.btn === 'edit' && this.modalStatus.status === '生效中') {
        this.wayDisplay = true;
        this.wayDisabled = true;

        this.$nextTick(() => {
          this.handleTimingEffect(effectType, planEffectTime, planEndTime);
          if (effectType !== 'TIMING') {
            this.endDateDisplay = false;
            this.dateDisplay = false;
          }else {
            this.dateDisabled = false;
          }
        });
      } else if (this.modalStatus.btn === 'edit' && this.modalStatus.status === '待生效') {
        this.endDateDisplay = false;
        this.$nextTick(() => {
          this.dateDisplay = true;
          this.wayDisplay = true;
          this.wayDisabled = false;
          this.formValidate.time = [planEffectTime, planEndTime];
        });
      } else if (this.modalStatus.btn === 'query') {
        this.wayDisabled = true;
        this.endDateDisplay = false;
        this.$nextTick(() => {
          if (effectType !== 'NOW') {
            this.dateDisplay = true;
            this.dateDisabled = true;
            this.formValidate.time = [planEffectTime, planEndTime];
          } else {
            this.dateDisplay = false;
          }
        });
      }
    },
    handleTimingEffect(effectType, planEffectTime, planEndTime) {
      if (effectType === 'TIMING' && planEffectTime !== null) {
        this.dateDisplay = false;
        this.endDateDisplay = true;
        this.endDateTime = planEffectTime;
      }
      if (effectType === 'TIMING') {
        this.dateDisabled = true;
        this.dateDisplay = true;
        this.endDateDisplay = false;
        this.formValidate.time = [planEffectTime, planEndTime];
      }
    },
    updateBgImage(url) {
      this.formValidate.file = url;
    },
    // 鼠标事件
    handleMouseOver() {
      this.isTipImgHovered = true;

    },
    handleMouseOut() {
      this.isTipImgHovered = false;
    },
  }
}
</script>
<style lang="less" scoped>
/deep/ .ivu-icon-ios-loading{
  display: none;
}
.modal-content {
  width: 100%;
  .modal-items {
    width: 50%;
    margin-left: 50%;
    padding: 20px;
    &-title {
      padding-bottom: 10px;
    }
    &-upload-content {
      padding: 20px 0;
    }
  }
  .cards-img {
    width: 18vw;
    height: 8vw;
  }
}
.form-style {
  position: relative;
  z-index: 8
}
.hover-img {
  position: absolute;
  height: 16px;
  background: #fff;
  top: 64px;
  left: 90px;
  z-index: 9;
  text-align: center;
  width: 22px;
}
.hover-img-icon {
  padding-top: 2px;
  height: 14px;
  width: 14px;
}
.tip-img {
  position: absolute;
  top: 20px;
  left: -20px;
  width: 240px;
  height: 463px;
  opacity: 0.8;
  z-index: 110;
}
.modal-items-end {
  justify-content: end;
}
.banner-input-item {
  position: relative;
}
.banner-input-tip {
  position: absolute;
  width: 42px;
  height: 18px;
  top: 32px;
  right: 0px;
  background-color: rgba(0, 16, 24, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}
</style>

