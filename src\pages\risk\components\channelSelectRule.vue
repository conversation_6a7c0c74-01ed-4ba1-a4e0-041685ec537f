<template>
  <div class="channel-select">
    <i-select
      v-model="selectedChannels"
      multiple
      :disabled="disabled"
      placeholder="请选择渠道"
      @on-change="handleChange"
    >
      <i-option
        v-for="item in channelOptions"
        :key="item.id"
        :value="item.id"
        :label="item.name"
      />
    </i-select>
  </div>
</template>

<script>
import { channelOptions } from '../constants/ruleOptions'

export default {
  name: 'ChannelSelectRule',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      channelOptions,
      selectedChannels: []
    }
  },
  watch: {
    value: {
      handler(val) {
        this.selectedChannels = val || []
      },
      immediate: true
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    }
  }
}
</script>
