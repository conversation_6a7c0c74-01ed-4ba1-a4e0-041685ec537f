/**
 * banner
 */
import fetch from '@/utils/network/fetch'

const getTableData = (query = {}) => {
  return fetch('/api/v1/banner', { data: query })
}
const saveDetailData = data => {
  return fetch(`/api/v1/banner/save`, { method: 'post' , data: JSON.stringify(data)})
}
const getDetailData = id => {
  return fetch(`/api/v1/banner/${id}`)
}
const moveUpRow = (id) => {
  return fetch(`/api/v1/banner/move_up/${id}`, { method: 'put' })
}
const moveDownRow = (id) => {
  return fetch(`/api/v1/banner/move_down/${id}`, { method: 'put' })
}
const deleteRow = (id) => {
  return fetch(`/api/v1/banner/${id}`, { method: 'delete' })
}
const removeRow = (id) => {
  return fetch(`/api/v1/banner/remove/${id}`, { method: 'put' })
}
const creatBanner = (id, name) => {
  return fetch(`/api/v1/sports_galleries/${id}?name=${name}`, { method: 'put' })
}
const editBanner = (id, name) => {
  return fetch(`/api/v1/sports_galleries/${id}?name=${name}`, { method: 'put' })
}
const queryPageType = () => {
  return fetch(`/api/v1/banner/action_type`, { method: 'get' })
}


export default {
  getTableData,
  getDetailData,
  saveDetailData,
  moveUpRow,
  moveDownRow,
  editBanner,
  deleteRow,
  removeRow,
  creatBanner,
  queryPageType,
}
