<template>
  <div class="navi-menu">
    <i-menu width="auto" @on-select="selectMenu" :active-name="activeMenu" :open-names="openMenus" theme="dark" accordion>
      <span v-for="item in list" :key="item.name">
        <i-submenu v-if="item.children && !!item.children.length" class="sider-submenu" :name="item.name" v-has="item.permissions">
          <template slot="title">
            <i-icon :type="item.icon" /> {{ item.title }}
          </template>
          <i-menuItem v-for="subItem in item.children" :key="subItem.title" :name="subItem.path" v-has="subItem.permissions">
            {{ subItem.title }}
          </i-menuItem>
        </i-submenu>

        <i-menuItem v-else :name="item.path" v-has="item.permissions">
          <i-icon :type="item.icon" /> {{ item.title }}
        </i-menuItem>
      </span>
    </i-menu>
  </div>
</template>

<script>
export default {
  name: "naviMenu",
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 激活menu
      activeMenu: '',
      // 展开的submenu
      openMenus: []
    }
  },

  created() {
    this.setActiveMenu()
  },

  methods: {
    // 设置激活菜单
    setActiveMenu() {
      this.activeMenu = this.$route.path === '' ? '' : this.$route.path
      let menu = this.list.find(item => {
        for (let subItem of item.children) {
          if (subItem.path === this.activeMenu) {
            return item
          }
        }
      })
      this.openMenus = menu ? [menu.title] : []
    },
    // 菜单选择
    selectMenu(path) {
      this.$router.push({
        path: path
      })
    }
  }
}
</script>

<style lang="less" scoped></style>
