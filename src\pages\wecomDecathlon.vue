<template>
  <div class="wecom-decathlon">
    <span class="action-text" @click="() => toWecom()">点击跳转到企微管理后台</span>
  </div>
</template>

<script>
import Env<PERSON>he<PERSON> from '../utils/env'

export default {
  data() {
    return {
      wecomURL: ''
    }
  },
  mounted() {
    this.wecomURL = EnvChecker.isPR()
      ? 'https://wecom.decathlon.com.cn/backoffice/#/dashboard'
      : 'https://wecom-preprod.decathlon.com.cn/backoffice/#/dashboard'

    this.toWecom()
  },
  methods: {
    toWecom() {
      window.open(this.wecomURL, '_blank')
    }
  }
}
</script>

<style lang="less" scoped>
.wecom-decathlon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .action-text {
    cursor: pointer;
    color: #3080c3;
    font-weight: bold;
    text-decoration: underline;
  }
}
</style>