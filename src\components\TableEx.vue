<template>
  <div class="container">
    <vue-good-table
      :ref="tableRef"
      :rtl="rtl"
      :rows="rows"
      :columns="columns"
      :max-height="maxHeight"
      :fixed-header="fixedHeader"
      :line-numbers="lineNumbers"
      :row-style-class="rowStyleClass"
      :search-options="searchOptions"
      :sort-options="sortOptions"
      :pagination-options="pagination"
    >
      <template slot="table-row" slot-scope="props">
        <span class="td-item" v-if="props.column.field == 'table_operations'">
          <i-button
            class="oper-button"
            shape="circle"
            v-for="item in props.column.opers"
            :key="item.value"
            :icon="item.icon"
            @click="onOperationClick(item.value, props.row)"
          >
            {{item.label}}
          </i-button>
        </span>
        <span class="td-item span-pic-box" v-else-if="props.column.field == 'table_image'">
          <img class="table-img" :src="props.row[props.column.src]" alt="table_iamge">
        </span>
        <div class="td-item" v-else>
          {{props.formattedRow[props.column.field]}}
        </div>
      </template>
      <template slot="pagination-bottom" slot-scope="props">
        <div class="table-footer row item-center justify-between">
          <!-- 批量操作 -->
          <div>
            <i-dropdown
              v-if="options.length"
              @on-click="name => onOptionsClick(name)"
            >
              批量操作
              <i-dropdownMenu slot="list">
                <i-dropdownItem
                  class="dropdown-item"
                  v-for="item in options"
                  :key="item.value"
                  :name="item.value"
                >
                      {{item.label}}
                </i-dropdownItem>
              </i-dropdownMenu>
            </i-dropdown>
          </div>
          <!-- 分页 -->
          <div>
            <i-page
              show-total
              :total="total"
              :page-size="pagination.perPage"
              size="small"
              show-sizer 
              :page-size-opts="pageSizeOption"
              @on-change="onPageChange"
              @on-page-size-change="onSizeChange"
            />
          </div>
        </div>
      </template>
    </vue-good-table>
  </div>
</template>

<script>
/**
 * 基于vue-good-table封装
 * @module Table
 * @see 具体参数设置请查看 {@link https://xaksis.github.io/vue-good-table/}
 */

/**
 * Props 接受父组件的传值
 * @prop { String } tableRef 可通过ref获取选择的行 this.$refs['my-table'].selectedRows; 
 * @prop { Array } rows 包含行对象的数组 
 * @prop { Array } columns 描述表列的对象的数组
 * @prop { String } maxHeight 设置表体的最大高度
 * @prop { Boolean } fixedHeader 滚动表格时保持在视图中
 * @prop { Boolean } lineNumbers 显示每行的行号
 * @prop { String | Function } rowStyleClass 将类分配给行的属性。这可以是表示css类名的字符串或函数
 * @prop { Boolean } rtl 为表启用“从右到左”布局
 * @prop { Object } searchOptions 搜索选项
 * @prop { Object } sortOptions 排序选项
 * @prop { Object } selectOptions 复选框 {enable: true}
 * @prop { Array } options 批量操作 [{label: '刷新', value: 'refresh'}]
 * @prop { Number } perPage 每页显示记录条数
 * @prop { Number } total 分页的数据总数
 * @prop { Function } operationClick 表格操作点击事件回调
 * @prop { Function } optionClick 批量操作事件回调
 * @prop { Function } pageChange 页码改变事件回调
 */
export default {
  name: 'tableEx',
  props: {
    tableRef: {
      type: String,
      default: ''
    },
    rows: {
      type: Array,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    perPage: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    },
    maxHeight: String,
    fixedHeader: {
      type: Boolean,
      default: false
    },
    lineNumbers: {
      type: Boolean,
      default: false
    },
    rowStyleClass: [String, Function],
    rtl: {
      type: Boolean,
      default: false
    },
    searchOptions: {
      type: Object,
      default: () => {}
    },
    selectOptions: {
      type: Object,
      default: () => {}
    },
    sortOptions: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Array,
      default: () => []
    },
    operationClick: {
      type: Function,
      default: () => {}
    },
    optionClick: {
      type: Function,
      default: () => {}
    },
    pageChange: {
      type: Function,
      default: () => {}
    },
    sizeChange: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      pagination: {
        enabled: true, 
        perPage: this.perPage
      },
      pageSizeOption: [10, 20, 50, 100]
    }
  },
  methods: {
    // 表格操作
    onOperationClick(oper, row) {
      this.operationClick && this.operationClick(oper, row)
    },

    // 批量操作
    onOptionsClick(opt) {
      this.optionClick && this.optionClick(opt)
    },

    // 页码改变
    onPageChange(page) {
      this.pageChange && this.pageChange(page)
    },

    // 每页显示记录条数改变
    onSizeChange(size) {
      this.pagination['perPage'] = size
      this.sizeChange && this.sizeChange(size)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  // width: 90%;
  margin: 0 auto;
  .oper-button {
    margin-left: 15px;
  }
  .span-pic-box {
    display: block;
    height: 40px;
    .table-img {
      width: 80px;
      height: 60px;
    }
  }
  .table-footer {
    padding: 10px 20px;
  }
}

</style>
