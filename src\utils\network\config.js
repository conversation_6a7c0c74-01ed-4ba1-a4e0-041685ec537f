/**
 * axiox 基础配置
 **/
import comConfig from '@/config/apiConfig.js'

export default {
  // 网络请求URL
  url: '',

  // 请求方法，默认get
  method: 'get',

  // 基础URL路径，会自动添加到url前，除非url是绝对URL
  baseURL: comConfig.BASE_URL,

  // 允许在向服务器发送前，修改请求数据。适用PUT' || 'POST' || 'PATCH'
  transformRequest: [
    function (data) {
      return data
    }
  ],
 
  // 在传递给 then/catch 前，允许修改响应数据
  transformResponse: [
    function (data) {
      return data ? JSON.parse(data) : data
    }
  ],

  // 自定义请求头
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'x-api-key': comConfig.API_PORTAL
  },

  // 即将与请求一起发送的 URL 参数
  params: {},

  // 请求主体被发送的数据。适用于 'PUT' || 'POST' || 'PATCH'
  // data: null,

  // 请求超时时间
  timeout: 80000,

  // 表示跨域请求时是否需要使用凭证，默认false
  withCredentials: true,

  // 允许为上传处理进度事件
  onUploadProgress: function (progressEvent) { },

  // 允许为下载处理进度事件
  onDownloadProgress: function (progressEvent) { }
}
