/**
 * 目前包括指令
 * 权限指令
 * 
 */
import Vue from 'vue'
import { local } from '@/utils/util'

// - 权限指令
Vue.directive('has', {
  inserted: (el, binding) => {
    let bindingList = binding.value
    if (!bindingList) {
      console.warn('permissions value is null')
      return
    }
    if (!_UTIL.checkPermissions(bindingList)) {
      el.parentNode.removeChild(el)
    }
  }
})

const _UTIL = {

  // 判断权限
  checkPermissions(list) {
    let localPermissions = local.getStorage('PERMISSIONS')
    let permissions = localPermissions ? localPermissions : []
     for (let per of list) {
       // 权限列表中属否存在该权限
       if (permissions.indexOf(per) > -1) {
         return true
       }
     }
    // 返回false, 说明用户没有该权限
    return false
  }
}