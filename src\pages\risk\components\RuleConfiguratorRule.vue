<template>
  <div class="rule-configurator">
    <div v-for="(rule, idx) in rules" :key="rule._uid" class="rule-row">
      <!-- 且/或选择器，第一条不显示 -->
      <i-select v-if="idx > 0" :value="rule.relation" @on-change="val => updateRule(idx, 'relation', val)" style="width: 60px; margin-right: 10px" :disabled="disabled">
        <i-option value="and">且</i-option>
        <i-option value="or">或</i-option>
      </i-select>
      <!-- 左参数 -->
      <i-select
        :value="rule.left"
        @on-change="val => updateRule(idx, 'left', val)"
        style="width: 150px; margin-right: 10px"
        :disabled="disabled"
        placeholder="选择左参数"
      >
        <i-option
          v-for="item in leftOptions"
          :key="item.key"
          :value="item.key"
        >
          {{ item.name }}
        </i-option>
      </i-select>
      <!-- 操作符 -->
      <i-select :value="rule.operator" @on-change="val => updateRule(idx, 'operator', val)" style="width: 90px; margin-right: 10px" :disabled="disabled" placeholder="选择操作符">
        <i-option v-for="op in getOperatorOptions(rule.left)" :key="op.value" :value="op.value">{{ op.label }}</i-option>
      </i-select>
      <!-- 右参数（非空时不显示） -->
      <i-select
        v-if="rule.operator !== 'notEmpty'"
        :value="rule.right"
        @on-change="val => updateRule(idx, 'right', val)"
        style="width: 150px; margin-right: 10px"
        :disabled="disabled"
        placeholder="选择右参数"
      >
        <i-option v-for="item in rightOptions" :key="item.key" :value="item.key">{{ item.name }}</i-option>
      </i-select>
      <!-- 加号 -->
      <i-button v-if="rules.length < 6 && idx === rules.length-1" type="primary" icon="md-add" size="small" @click="addRule" :disabled="disabled" style="margin-left: 5px" />
      <!-- 减号，第一条不显示 -->
      <i-button v-if="rules.length > 1 && idx > 0" type="error" icon="md-remove" size="small" @click="removeRule(idx)" :disabled="disabled" style="margin-left: 5px" />
    </div>
    <!-- 新建时无规则 -->
    <div v-if="rules.length === 0">
      <i-button type="primary" icon="md-add" @click="addRule" :disabled="disabled">添加规则</i-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RuleConfiguratorRule',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    expression: {
      type: String,
      default: ''
    },
    leftOptions: {
      type: Array,
      default: () => []
    },
    rightOptions: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: [] // [{_uid, relation, left, operator, right}]
    }
  },
  watch: {
    expression: {
      handler(val) {
        if (val && !this._isUserChange) {
          this.rules = this.parseExpression(val)
        } else if (!val) {
          this.rules = []
        }
        this._isUserChange = false
      },
      immediate: true
    }
  },
  methods: {
    getOperatorOptions(leftKey) {
      // 统一返回所有操作符选项，不再根据参数类型限制
      return [
        { value: '>', label: '大于' },
        { value: '<', label: '小于' },
        { value: '=', label: '等于' },
        { value: 'contains', label: '包含' },
        { value: 'notEmpty', label: '非空' }
      ]
    },
    addRule() {
      if (this.rules.length >= 6) return
      this.rules.push({
        _uid: Date.now()+Math.random(),
        relation: this.rules.length>0 ? 'and' : '',
        left: '',
        operator: '',
        right: ''
      })
      this._isUserChange = true
      // 立即emit，保证表达式和界面同步
      this.emitChange()
    },
    removeRule(idx) {
      if (this.rules.length <= 1) return
      this.rules.splice(idx,1)
      this._isUserChange = true
      this.emitChange()
    },
    // 新增：每次 select 变动都 emitChange
    updateRule(idx, key, value) {
      this.rules[idx][key] = value
      this._isUserChange = true
      this.emitChange()
    },
    emitChange() {
      // 生成表达式
      const expr = this.generateExpression(this.rules)
      // 直接emit，保证表达式和界面同步
      this.$emit('change', expr)
    },
    parseExpression(expr) {
      if (!expr) return []

      // 使用更精确的解析方法，保持原始顺序
      const rules = []

      // 先将表达式按照 && 和 || 分割，但保持操作符信息
      const tokens = []
      let currentToken = ''
      let parenCount = 0

      for (let i = 0; i < expr.length; i++) {
        const char = expr[i]
        const nextChar = expr[i + 1]

        if (char === '(') {
          parenCount++
          currentToken += char
        } else if (char === ')') {
          parenCount--
          currentToken += char
        } else if (parenCount === 0 && char === '&' && nextChar === '&') {
          if (currentToken.trim()) {
            tokens.push(currentToken.trim())
            tokens.push('&&')
          }
          currentToken = ''
          i++ // 跳过下一个 &
        } else if (parenCount === 0 && char === '|' && nextChar === '|') {
          if (currentToken.trim()) {
            tokens.push(currentToken.trim())
            tokens.push('||')
          }
          currentToken = ''
          i++ // 跳过下一个 |
        } else {
          currentToken += char
        }
      }

      if (currentToken.trim()) {
        tokens.push(currentToken.trim())
      }

      // 解析每个token
      let lastRelation = ''
      for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i]

        if (token === '&&') {
          lastRelation = 'and'
          continue
        }
        if (token === '||') {
          lastRelation = 'or'
          continue
        }

        // 解析具体的表达式
        let left = '', op = '', right = ''

        // 匹配包含操作
        let match = token.match(/#([\w]+)\.contains\(#([\w]+)\)/)
        if (match) {
          // 修正contains操作的参数位置：
          // #storeList.contains(#store) 应该解析为：
          // left: store (出现在contains方法参数中的，应该在左边选择器)
          // right: storeList (调用contains方法的，应该在右边选择器)
          left = match[2]  // store (contains方法的参数)
          op = 'contains'
          right = match[1] // storeList (调用contains方法的对象)
        } else {
          // 匹配非空操作 (#store != '' && #store != null)
          match = token.match(/\(#([\w]+)\s*!=\s*''\s*&&\s*#\1\s*!=\s*null\)/)
          if (match) {
            left = match[1]
            op = 'notEmpty'
            right = ''
          } else {
            // 匹配其他操作符 (#markMax > #mark)
            match = token.match(/#([\w]+)\s*(>|<|=|!=)\s*#([\w]+)/)
            if (match) {
              left = match[1]
              op = match[2]
              right = match[3]
            }
          }
        }

        if (left && op) {
          rules.push({
            _uid: Date.now() + Math.random() + i,
            relation: rules.length > 0 ? lastRelation : '',
            left,
            operator: op,
            right
          })
        }

        lastRelation = ''
      }

      return rules
    },
    generateExpression(rules) {
      const opMap = {
        '>': '>',
        '<': '<',
        '=': '=',
        'contains': 'contains',
        'notEmpty': 'notEmpty'
      }
      const notEmptySet = new Set()
      return rules
        .filter(r => r.left && r.operator && (r.operator === 'notEmpty' || r.right))
        .map((r, idx) => {
          let exp = ''
          if (r.operator === 'contains') {
            // 修正contains表达式生成：
            // left: store (左边选择器的值)
            // right: storeList (右边选择器的值)
            // 生成: #storeList.contains(#store)
            exp = `#${r.right}.contains(#${r.left})`
          } else if (r.operator === 'notEmpty') {
            if (notEmptySet.has(r.left)) return '' // 跳过重复
            notEmptySet.add(r.left)
            exp = `(#${r.left} != '' && #${r.left} != null)`
          } else {
            exp = `#${r.left} ${opMap[r.operator]||r.operator} #${r.right}`
          }
          if (exp && idx > 0 && r.relation) {
            exp = (r.relation === 'and' ? '&&' : '||') + ' ' + exp
          }
          return exp
        })
        .filter(Boolean)
        .join(' ')
    },
    getAllLeftOptions(operator) {
      // 对于包含操作，左参数可以是list类型的参数（无论是leftOptions还是rightOptions）
      if (operator === 'contains') {
        const allOptions = [...this.leftOptions, ...this.rightOptions]
        return allOptions.filter(i => i.param_type === 'list')
      }
      // 其他操作则返回所有左参数选项
      return [...this.leftOptions, ...this.rightOptions]
    }
  }
}
</script>

<style lang="less" scoped>
.rule-configurator {
  padding: 20px;
  .rule-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
}
</style>
