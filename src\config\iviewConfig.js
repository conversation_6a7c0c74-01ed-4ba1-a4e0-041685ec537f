/**
 * iview按需引入
 */

import Vue from 'vue'
import 'iview/dist/styles/iview.css'

import { 
  Button,
  ButtonGroup,
  Collapse,
  Layout,
  Header,
  Sider,
  Content,
  Menu,
  MenuItem,
  Submenu,
  Icon,
  Row,
  Col,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Select,
  Option,
  Input,
  Checkbox,
  CheckboxGroup,
  Steps,
  Step,
  Card,
  Modal,
  Upload,
  Form,
  FormItem,
  DatePicker,
  TimePicker,
  RadioGroup,
  Radio,
  Table,
  Page,
  Switch,
  Poptip,
  Cascader,
  Avatar,
  Tabs,
  TabPane,
  Tooltip
} from 'iview'

Vue.component('i-button', Button)
Vue.component('i-button-group', ButtonGroup)
Vue.component('i-collapse', Collapse)
Vue.component('i-layout', Layout)
Vue.component('i-header', Header)
Vue.component('i-sider', Sider)
Vue.component('i-content', Content)
Vue.component('i-menu', Menu)
Vue.component('i-menuItem', MenuItem)
Vue.component('i-submenu', Submenu)
Vue.component('i-icon', Icon)
Vue.component('i-row', Row)
Vue.component('i-col', Col)
Vue.component('i-dropdown',Dropdown)
Vue.component('i-dropdownMenu', DropdownMenu)
Vue.component('i-dropdownItem', DropdownItem)
Vue.component('i-select', Select)
Vue.component('i-option', Option)
Vue.component('i-input', Input)
Vue.component('i-checkbox', Checkbox)
Vue.component('i-checkbox-group', CheckboxGroup)
Vue.component('i-steps', Steps)
Vue.component('i-step', Step)
Vue.component('i-card', Card)
Vue.component('i-modal', Modal)
Vue.component('i-upload', Upload)
Vue.component('i-form', Form)
Vue.component('i-form-item', FormItem)
Vue.component('i-date-picker', DatePicker)
Vue.component('i-time-picker', TimePicker)
Vue.component('i-radio-group', RadioGroup)
Vue.component('i-radio', Radio)
Vue.component('i-table', Table)
Vue.component('i-page', Page)
Vue.component('i-switch', Switch)
Vue.component('i-poptip', Poptip)
Vue.component('i-cascader', Cascader)
Vue.component('i-avatar', Avatar)
Vue.component('i-tabs', Tabs)
Vue.component('i-tab-pane', TabPane)
Vue.component('i-tooltip', Tooltip)