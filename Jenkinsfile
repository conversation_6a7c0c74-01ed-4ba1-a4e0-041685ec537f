#!/usr/bin/env groovy

@Library('custom-lib@feat_cn_methods_k8s') _

/*
Arguments need to be modified:
- stack_name                         # stack name that the service is located in rancher, e.g. facade-services
- image_name                         # path to your image in registry, e.g. facade/payment-api
- service_name                       # service name in Rancher, same to the souce code repository name by default
- rancher_access_key_credential_id   # credential id of rancher access key
- rancher_secret_key_credential_id   # credential id of rancher secret key
- email_to                           # email receivers, e.g. shiphub@decathlon.<NAME_EMAIL>
- dockerfile_path                    # path to the Dockerfile, '.' by default
- jdk                                # jdk version e.g. java11 java12 java8
- maven                              # maven version e.g. maven3.6 maven 3.5
- daas_job_id                        # DaaS job id associated to this project
*/

def args = [:]

args.stack_name = 'membership'
args.image_name = 'membership/dktbackoffice'
args.service_name = 'dktbackoffice'

args.rancher_access_key_credential_id = env.BRANCH_NAME == 'master' ? 'membership_rancher_access_key_production' : 'membership_rancher_access_key_preprod'
args.rancher_secret_key_credential_id = env.BRANCH_NAME == 'master' ? 'membership_rancher_secret_key_production' : 'membership_rancher_secret_key_preprod'

args.email_to   = '<EMAIL>'
args.node = 'node12'

args.dockerfile_path = '.'

/*http://proxy-internet-aws-sg.subsidia.org:3128*/
args.build_command = "npm config set registry registry https://nexus-cn.dktapp.cloud/repository/npm-proxy-taobao/ && \
npm config set proxy http://proxy-internet-azure-cn.dktapp.cloud:3128 && \
export http_proxy=http://proxy-internet-azure-cn.dktapp.cloud:3128 && \
export https_proxy=http://proxy-internet-azure-cn.dktapp.cloud:3128 && \
export no_proxy='localhost|.subsidia.org|127.0.0.1|.dktapp.cloud' && \
npm install phantomjs-prebuilt --unsafe-perm && npm install chromedriver --ignore-scripts --unsafe-perm && npm install --unsafe-perm && npm run build"

args.daas_job = null

pipeline_nodejs(args)