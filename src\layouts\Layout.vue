<template>
	<div class="container">
		<i-layout class="layout">
			<i-header class="layout-header">
				<div class="layout-header-content">
					<!-- logo -->
					<i-col class="layout-logo-box" span="4">
						<img alt class="layout-logo-box" src="../assets/dktlogo.png">
					</i-col>
					<div class="row item-center mr30">
						<i-cascader
							:data="storeList"
							@on-change="storeChange"
							class="cascader-store mr30"
							filterable
							placeholder="请选择门店"
							v-if="storeList && storeList.length"
							v-model="store"
						/>
						<i-dropdown>
							<a>
								<i-poptip placement="bottom-end">
									<div class="user-content row item-center justify-center">
										<div class="user-name">{{userInfo.profile}}</div>
										<div class="row item-center justify-center" v-if="!!userInfo.photoUrl">
											<i-avatar :src="userInfo.photoUrl" size="large"/>
										</div>
										<div class="avatar-content row item-center justify-center shadow" v-else>
											<img class="avatar-img" src="../assets/dkt.jpeg">
										</div>
									</div>
									<div class="poptip-content" slot="content">
										<div class="userInfo-item row item-center">
											<i-icon class="userInfo-icon" type="ios-person"/>
											<div class="ml10" style="font-size: 12px;">{{userInfo.displayName}}</div>
										</div>
										<div class="userInfo-item row item-center">
											<i-icon class="userInfo-icon" type="ios-mail"/>
											<div class="ml10" style="font-size: 15px;">{{userInfo.email}}</div>
										</div>
                    <div class="userInfo-item row item-center" @click="handleLogOut">
											<i-icon class="userInfo-icon" type="md-log-out"/>
											<div class="ml10" style="font-size: 15px;">登出</div>
										</div>
									</div>
								</i-poptip>
							</a>
						</i-dropdown>
					</div>
				</div>
			</i-header>
			<i-layout class="layout-layout">
				<!-- menu list -->
				<i-sider class="layout-sider">
					<navi-menu :list="menuList"/>
				</i-sider>
				<!-- content -->
				<i-content class="layout-content">
					<router-view/>
				</i-content>
			</i-layout>
		</i-layout>
	</div>
</template>

<script>
import pageConfig from './config.js'
import { mapGetters, mapActions } from 'vuex'
import { defaultMenuList } from '@/config/menuConfig'
import NaviMenu from '@/components/NaviMenu'
import token from '@/mock/token'
import stores from '@/mock/store'

export default {
  name: 'layout',
  components: {
    'navi-menu': NaviMenu
  },
  data() {
    return {
      // 支持语言
      languages: pageConfig.languages,
      // 当前语言
      currentLang: pageConfig.languages[0].label,
      // menu list
      menuList: [],
      // 门店列表
      storeList: this.$local.getStorage('STORE_LIST') || [],
      // cascader绑定值
      store: [],
      // 用户信息
      userInfo: this.$local.getStorage('USER_INFO') || {}
    }
  },
  created() {
    let query = this.$route.query
    if (query.code) {
      this.getToken(query.code)
    } else {
      this.getStoreList()
    }
  },
  mounted() {
    if (this.storeList.length) {
      this.setDefaultStore(this.storeList)
    }
    // 如果token存在 直接渲染菜单
    if (this.$local.getStorage('TOKEN')) {
      this.menuList = defaultMenuList
    }
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  watch: {
    // 监控门店切换 刷新数据
    currentStore(newVal, oldVal) {
      this.$router.push(0)
    }
  },
  methods: {
    ...mapActions(['setStore']),
    /**
     * 页面事件
     */
    storeChange(e) {
      if (e.length) {
        let parent = this.storeList.find(item => {
          if (e[0] === item.value) return item
        })
        let currentStore = parent.children.find(item => {
          if (e[1] === item.value) return item
        })
        this.setStore(currentStore)
      } else {
        // this.setStore({})
      }
    },

    /**
     * 网络请求
     */
    // 获取token
    async getToken(code) {
      let res = await this.$api.getToken(code)
      if (res) {
        // 清空刷新次数
        this.$session.removeStorage('TOKEN_REFRESH_TIMES')
        // 渲染用户信息
        this.userInfo = res.data
        // 存储用户权限
        this.$local.setStorage('PERMISSIONS', res.data.permissions)
        // 储存用户信息
        this.$local.setStorage('USER_INFO', res.data)
        // 渲染菜单列表
        this.menuList = defaultMenuList
        // 获取门店列表
        this.getStoreList()
      }
    },
    // 获取门店列表
    async getStoreList() {
      let res = await this.$api.getStoreList()
      if (res) {
        this.storeList = this.handleStoreList(res.data)
        this.$local.setStorage('STORE_LIST', this.storeList)
      }
    },

    /**
     * 数据处理
     */
    // 设置store默认值
    setDefaultStore(list) {
      let localStore = this.$local.getStorage('STORE')
      let defaultStore = {}
      if (localStore) {
        for (let item of list) {
          for (let subItem of item.children) {
            if (localStore.dktStoreNumber === subItem.dktStoreNumber) {
              defaultStore = {
                children: [subItem],
                label: item.label,
                value: item.value
              }
              break
            }
          }
        }
      } else {
        defaultStore = list[0]
      }
      this.store = [defaultStore.value, defaultStore.children[0].value]
      this.setStore(defaultStore.children[0])
    },

    handleLogOut() {
     
      // 调用logout接口
      this.handleLogout();
    },

    async handleLogout() {
      let res = await this.$api.goLogout()
      if (res && res.status === 'success') {
        // console.log('>>>> logout res >>>>>', res)
        try {
          this.$local.removeStorage('TOKEN')
          this.$local.removeStorage('USER_INFO')
          this.$local.removeStorage('PERMISSIONS')
          this.$local.removeStorage('STORE')
          this.$local.removeStorage('STORE_LIST')
          this.$session.removeStorage('TOKEN_REFRESH_TIMES')
        } catch (error) {}
        this.$router.replace('/login')
      }
    },

    // 处理cascader所需数据
    handleStoreList(list) {
      let cascaderArr = []
      // 过滤,去重城市
      let cities = []
      for (let store of list) {
        let city = store.city
        if (cities.indexOf(city) === -1) {
          cities.push(city)
        }
      }
      // 进行数据组合
      for (let city of cities) {
        let objc = {
          label: city,
          value: encodeURI(city),
          children: []
        }
        for (let store of list) {
          store['label'] = store.name
          store['value'] = store.dktStoreNumber
          if (city === store.city) {
            objc.children.push(store)
          }
        }
        cascaderArr.push(objc)
      }
      if (cascaderArr.length) {
        this.setDefaultStore(cascaderArr)
      }
      return cascaderArr
    }
  }
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  .layout {
    height: 100%;
    &-header {
      padding: 0;
      background: #fff;
      .layout-header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .user-content {
          color: #3080c3;
          .user-name {
            padding-right: 8px;
            font-size: 10px;
            font-weight: bold;
          }
          .avatar-content {
            height: 40px;
            width: 40px;
            border-radius: 40px;
            .avatar-img {
              width: 30px;
            }
          }
        }
        .poptip-content {
          .userInfo-item {
            padding: 8px 0;
            color: #3080c3;
            .userInfo-icon {
              font-size: 18px;
            }
          }
        }
        .cascader-store {
          width: 180px;
        }
        .dropdown-item {
          text-align: center;
        }
        .layout-logo-box {
          width: 200px;
          height: 64px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          background: #3080c3;
          box-sizing: border-box;
          img {
            width: 140px;
            height: 21px;
          }
        }
      }
    }
    .layout-layout {
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.8);
      overflow: hidden;
      .layout-sider {
        background: #515a6e !important;
        width: 200px;
        min-height: 90vh;
        overflow: auto;
        background: #f8f9fb;
      }
      .layout-content {
        padding: 20px 20px;
        background: #f5f5f5;
      }
      .sider-submenu {
        background: #f8f9fb;
      }
    }
  }
}
</style>