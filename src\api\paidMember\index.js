import fetch from '@/utils/network/fetch'

// 查询已增加的模板库管理列表
const getTplManageList = (query = {}) => {
	return fetch('/api/v2/product_templates', { data: query })
}

// 添加产品到模板库
const createTemplateProduct = (body = {}) => {
    return fetch('/api/v2/product_templates', { method: 'post', data: body })
}

// 搜索
const searchModel = (data = {}) => {
	return fetch('/api/v3/products/models', { data: data })
}
// 禁用
const disableModal = id => {
	return fetch(`/api/v2/product_templates/${id}`, { method: 'delete' })
}

// 取消禁用
const enableModal = (id, body={}) => {
	return fetch(`/api/v2/product_templates/${id}/status`, { method: 'patch', data: JSON.stringify(body) })
}

// 开卡礼管理-下架商品
// const removeProductTemplate = (catalogue_id, params) => {
// 	return fetch(`/api/v2/products/${catalogue_id}`, { method: 'put', data: JSON.stringify(params) })
// }
// 门店开卡礼管理 - 首页 - 从模板库创建商品

const createProductFromTemplate = (body = {}) => {
    return fetch('/api/v2/products', { method: 'post', data: body })
}

// 下架开卡礼卡槽/下架单个开卡礼产品
const removeOpenCardGift = (params) => {
	return fetch(`/api/v2/products`, { method: 'put', data: JSON.stringify(params) })
}

// 模版库管理 - 首页 - 编辑模版信息
const updateProductTemplate = (template_id, params) => {
	return fetch(`/api/v2/product_templates/${template_id}`, { method: 'put', data: JSON.stringify(params) })
}

// 查询开卡礼管理列表
const getOpenCardManageList = (query = {}) => {
	return fetch('/api/v2/products/fixed/paid_member', { data: query })
}

// 查看开卡礼卡槽所属商品
const getCardSlotProducts = (query = {}) => {
	return fetch('/api/v2/products/fixed/paid_member/order_number', { data: query })
}

// 开卡礼管理-修改库存
const updateProductStock = (data = {}) => {
  return fetch(`/api/v2/products/${data.catalogue_id}/stock`, {
    method: 'put',
    data: JSON.stringify({
		stock: data.stock,
		store_number: data.store_number,
	})
  })
}

// 开卡礼管理-修改多个库存
const updateMultiProductStock = (data = {}, store_number) => {
	return fetch(`/api/v2/products/stock/${store_number}`, {
    method: 'put',
    data: JSON.stringify(data)
  })
}

// 开卡礼管理-从模板库添加商品-查询是否同一dsm_code
const checkSameDsmCode = (query = {}) => {
	return fetch('/api/v2/product_templates/check_same', { data: query })
}


// 付费会员管理-订单管理
const getOrderList = (query = {}) => {
	return fetch('/api/v2/paid_member_orders', { data: query })
}
// 获取城市门店
const getCityStore = (query = {}) => {
	return fetch('/api/v2/paid_members/stores', { data: query })
}

const handleOrderRefund = (body = {}) => {
	return fetch('/api/v2/paid_member_orders/refund', { method: 'post', data: JSON.stringify(body) })
}

// 付费会员管理-订单详情
const getOrderDetail = (query = {}) => {
	return fetch('/api/v2/paid_member_orders/detail', { data: query })
}


const downloadTemplate = (query = {}) => {
	return fetch('/api/v2/benefit/templates/doc', { data: query })
}

const uploadTemplate = (data = {}) => {
  	return fetch('/api/v2/benefit/templates/doc', data)
}

const uploadProcuctTemplateImage = (data = {}) => {
	return fetch('/api/v2/product_templates/image', data)
}

 const downloadFile = (res, fileName = '导出文件', contentType = 'text/csv;charset=utf-8') => {
	const blob = new Blob([`\uFEFF ${res}`], {
	  type: contentType
	});
	const url = window.URL.createObjectURL(blob);
	const link = document.createElement('a');
	link.style.display = 'none';
	link.href = url;
	link.setAttribute('download', `${fileName}.csv`);
	document.body.appendChild(link);
	link.click();
  }

export default {
  getTplManageList,
  createTemplateProduct,
  searchModel,
  disableModal,
	removeOpenCardGift,
  createProductFromTemplate,
  enableModal,
  updateProductTemplate,
  getOpenCardManageList,
	getCardSlotProducts,
  updateProductStock,
	updateMultiProductStock,
	checkSameDsmCode,
	getOrderList,
	getCityStore,
	handleOrderRefund,
	getOrderDetail,
	downloadTemplate,
	uploadTemplate,
	uploadProcuctTemplateImage,
	downloadFile
}
