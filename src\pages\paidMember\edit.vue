<template>
  <div class="container">
    <i-modal footer-hide width="1100" :closable="false" title="编辑产品信息" v-model="showDialog">
      <i-table border :columns="productColumns" :data="productList" >
        <template slot-scope="{ index }" slot="title">
          <i-input placeholder="请输入名称" v-model="productList[index].attributes.title" />
        </template>
        <template slot-scope="{ index }" slot="short_name">
          <i-input placeholder="请输入简称" v-model="productList[index].attributes.short_name" />
        </template>
        <template slot-scope="{ index }" slot="color">
          <i-input placeholder="请输入颜色" v-model="productList[index].attributes.color" />
        </template>
        <template slot-scope="{ index }" slot="size">
          <i-input placeholder="请输入尺码" v-model="productList[index].attributes.size" />
        </template>
        <template slot-scope="{ index }" slot="description">
            <i-input :autosize="{minRows:3,maxRows:8}" type="textarea" placeholder="请输入描述" v-model="productList[index].attributes.description" />
        </template>
        <template slot-scope="{ index }" slot="image_url">
          <div class="img-box">
            
            <i-upload
              class="img-upload-wrap"
              :before-upload="beforeUpload"
              :show-upload-list="false"
              action
              :max-size="1"
              name="file"
              type="select"
            >
              <div class="img-wrap" @click="goChangePic">
                <img :src="productList[index].attributes.image_urls[0]" alt  class="cards-img" />
                <p class="change">更换</p>
              </div>
            </i-upload>
          </div>
        </template>
      </i-table>
      <div class="mt30 flex row justify-center">
        <i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
        <i-button v-if="!disabled" type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
      </div>
    </i-modal>
  </div>
</template>
<script>
import paidMemberApi from '@/api/paidMember/index'

export default {
  name: 'edit_product_modal',
  props: {
    id: '',
    from: '',
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      showDialog: true,

      productList: [],     // DKT产品列表
      productColumns: [
        // { title: '选择', key: 'id', width: 70, align: 'center', render: (h, params) => { return this.showRadioSelect(h, params) }},
        { title: 'Model Code', key: 'model_code', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.model_code) } },
        { title: 'Item Code', key: 'item_code', className: 'th-class', render: (h, params) => { return h('span', params.row.attributes.item_code) } },
        { title: '名称', slot: 'title', className: 'th-class'},
        { title: '简称', slot: 'short_name', className: 'th-class' },
        { title: '颜色', slot: 'color', className: 'th-class' },
        { title: '尺码', slot: 'size', className: 'th-class' },
        { title: '描述', slot: 'description', className: 'th-class', width:180},
        { title: '图片', slot: 'image_url', className: 'pt5 th-class', fixed: 'right', width: 160},
        // { title: '图片', slot: 'image_url', className: 'pt5 th-class', fixed: 'right', width: 160, render: (h, params) => { return this.showRowImage(h, params.row.attributes.image_urls) }},
      ],
      title: '',
      description: '',
      disabled: '',
      uploadFile: null,
    }
  },
  mounted() {
    if (this.id) {
      this.getProductDetail(this.id)
    }
  },
  methods: {
    // 获取产品详情
    async getProductDetail(id) {

      let resp = await paidMemberApi.getTplManageList({template_id: id});
      if (resp.status === 'success') {
        this.productList = resp.data.items
      }
    },
    // // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'span-pic-box' }, [h('img', { attrs: { src: imageUrls[0], class: 'table-img mt5', width:'80px', height: '80px' } })])
    },
    // 上传头像
    beforeImgUpload(file) {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPGOrPNG) {
        this.$message.error('上传图片只能是 JPG，png 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPGOrPNG && isLt2M
    },
    beforeUpload(file) {
      this.uploadFile = file;
      let isValidPic = this.beforeImgUpload(file);
      if( !isValidPic ){ 
        return false; 
      } else {
        this.handleImgUpload();
      }
    },
    async handleImgUpload() {
      let objc = {
          isForm: true,
          method: 'post',
          data: this.handleFormData()
        }
				let resp = await paidMemberApi.uploadProcuctTemplateImage(objc);
        if (resp.status === 'success') {
          let image_url = resp.data.image_url;
          this.$set(this.productList[0].attributes.image_urls, 0, image_url)
        } 
    },

    // 处理表单数据
    handleFormData(file) {
      let params = new FormData()
      params.append('file', this.uploadFile, this.uploadFile.name)
      return params
    },

    async certainClick() {
      let params = this.productList[0];
      const { title, short_name, description, image_urls } = params.attributes;
      if (!title) {
        this.$message.warning({ content: '请填写名称', closable: true, duration: 3 })
        return
      }
      if (!short_name) {
        this.$message.warning({ content: '请填写简称', closable: true, duration: 3 })
        return
      }
      if (!description) {
        this.$message.warning({ content: '请填写描述', closable: true, duration: 3 })
        return
      }
      if ( !image_urls || (image_urls.length<1) ) {
        this.$message.warning({ content: '请选择图片', closable: true, duration: 3 })
        return
      }

      let resp = await paidMemberApi.updateProductTemplate(params.template_id, params);
			if (resp.status == 'success') {
					this.certain && this.certain()
			}
    },
    cancelClick() {
      this.showDialog = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    },
    goChangePic() {
    },
  }
}
</script>
<style lang="less" scoped>
.text {
  width: 90%;
}
.mt5 {
  margin-top: 5px;
}
.img-box{
  display: flex; justify-content: center;
  .img-wrap{
    width: 80px; height: 80px;
    position: relative;
    cursor: pointer;
    display: flex;
    img{ width: 80px; object-fit: contain;}
    .change{ 
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%; height: 20px; background: rgba(0, 15, 23, 0.6); 
      font-size: 14px; color: #FFFFFF;
    }
  }
}
</style>
