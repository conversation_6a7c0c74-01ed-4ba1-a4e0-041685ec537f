<template>
  <div class="my-decathlon">
    <div class="tips">My decathlon 功能已下线，相关功能已合并至当前站点<p class="high-light" @click="goCustomerSearch">【客户查找】</p>。</div>
    <div class="tips">操作手册：<p class="high-light" @click="seeDoc">MPCN操作手册-修改手机&注销 - Google 文档</p></div>
  </div>
</template>

<script>

export default {
  data() {
    return {
    }
  },
  methods: {
    goCustomerSearch() {
      this.$router.push({ path: '/customerSearch' })
    },
    seeDoc() {
      const url = 'https://docs.google.com/document/d/1_DaPV0NpISqXkOA3I7jF1S4ZN_tN3Z0tlxoo2u5UVyI/edit?tab=t.0#heading=h.jxdzvrqtf1ts';
      window.open(url, '_blank', 'noopener');
    }
  },
  mounted() {
  }
}
</script>

<style lang="less" scoped>
.my-decathlon {
  width: 100%;
  height: 100%;
  font-size: 12px; line-height: 18px; 
  .tips{
    display: flex;
    .high-light{
      cursor: pointer;
      color:#3080c3
    }
  }
}
</style>