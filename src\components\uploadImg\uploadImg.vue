<template>
  <div class="upload-wrap">
    <div :class="['upload-box', disabledStatus ? 'disabled': '' ]">
      <template v-if="!curImgUrl">
        <template v-if="!disabledStatus">
          <i-upload
            class="img-upload-wrap"
            :before-upload="beforeUpload"
            :show-upload-list="false"
            action
            :max-size="1"
            name="file"
            type="select"
          >
            <div class="img-wrap pointer">
              <img :src="curImgUrl" alt  class="cards-img" />
              <div class="cover">
                <p class="add">+</p>
                <p class="click-upload">点击上传</p>
              </div>
            </div>
          </i-upload>
        </template>
        <template v-else>
          <div class="img-upload-wrap">
            <div class="img-wrap pointer">
              <img :src="curImgUrl" alt  class="cards-img" />
              <div class="cover">
                <p class="add">+</p>
                <p class="click-upload">点击上传</p>
              </div>
            </div>
          </div>
        </template>

      </template>
      <template v-else>
        <div class="img-upload-wrap" @mouseover="onMouseOver" @mouseleave="onMouseLeave">
          <div class="img-wrap">
            <img :src="curImgUrl" alt  class="cards-img" />
            <!-- 禁用状态下不显示操作 -->
            <div class="cover mask" v-if="!disabledStatus && imageMaskVisible">
              <img class="preview-icon" src="https://membership-pr.oss-cn-shanghai.aliyuncs.com/sport/catalogue/053234ef-c071-4110-b185-33decd61bd26.png" @click="previewImg" alt="">
              <img class="delete-icon"  src="https://membership-pr.oss-cn-shanghai.aliyuncs.com/sport/catalogue/398cc6ea-1d3b-4fad-9f3c-ebeaf2953882.png" @click="deleteImg" alt="">
            </div>
          </div>

          <i-modal class="image-preview-box" footer-hide :mask-closable="false"  width="80vw" v-model="previewVisible">
            <div class="preview-wrap">
              <img class="preview-image" :src="curImgUrl" alt="">
            </div>
          </i-modal>

        </div>
      </template>
    </div>
    <div class="tips">
      <p>建议尺寸：{{pixelLimit[0]}}x{{pixelLimit[1]}}（<{{sizeLimit}}MB）</p>
      <p>支持格式：png, jpg, jpeg</p>
    </div>
  </div>
</template>

<script>
const upLoadImgUrl = {
  default: '/api/v1/draw/image',
  banner: '/api/v1/banner/image',
}
export default {
  name: 'uploadImg',
  props: {
    upLoadUrlType: {
      type: String,
      default: 'default'
    },
    pixelLimit: {
      type: Array,
      default: () => [500,400]
    },
    sizeLimit: {
      type: Number,
      default: 2
    },
    // 是否检查像素(调整为默认不检查)
    needPixelCheck: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabledStatus: {
      type: Boolean,
      default: false
    },
    imageUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadFile: null,
      curImgUrl: this.imageUrl,
      imageMaskVisible: false,
      previewVisible: false,
    };
  },
  methods: {
    async beforeUpload(file) {
      this.uploadFile = file;
      let isValidPic = await this.beforeImgUpload(file);
      if( !isValidPic ){
        return false;
      } else {
        this.handleImgUpload();
      }
    },
    // 上传头像
    beforeImgUpload(file) {
      // console.log('>>>>file', file)
      return new Promise((resolve, reject) => {

      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png'
      const isSizeCorrect = file.size / 1024 / 1024 < this.sizeLimit

      if (!isJPGOrPNG) {
        this.$message.error('上传图片只能是 JPG，png 格式!');
        reject(false);
      }
      if (!isSizeCorrect) {
        let tips = `上传图片大小不能超过 ${this.sizeLimit}MB!`;
        this.$message.error(tips);
        reject(false);
      }

      if (!this.needPixelCheck) {
        resolve(true);
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (ev) => {
          let img = new Image();
          img.src = ev.target.result;
          img.onload = () => {
            let width = img.width;
            let height = img.height;
            // console.log('>>>>width', width, '>>>>height', height)
            if (width !== this.pixelLimit[0] || height !== this.pixelLimit[1]) {
              let tips = `上传图片尺寸必须为${this.pixelLimit[0]}x${this.pixelLimit[1]}`
              this.$message.error(tips);
              reject(false)
            } else {
              resolve(true)
            }
          }
        }
      }


      })
    },
    async handleImgUpload() {
      let objc = {
          isForm: true,
          method: 'post',
          data: this.handleFormData()
        }
        let  resp = await this.$api.ldUploadImage(upLoadImgUrl[this.upLoadUrlType], objc);
        if (resp.status === 'success') {
          let imageUrl = resp.data.imageUrl;
          this.curImgUrl = imageUrl;
          this.$emit('updateImageUrl', imageUrl)
        }
    },

    previewImg() {
      this.previewVisible = true;
    },
    deleteImg() {
      this.$modal.confirm({
        'title': '确认删除图片？',
        'content': ``,
        'okText': '删除',
        'cancelText': '取消',
        'onOk': () => {
          this.curImgUrl = '';
          this.$emit('updateImageUrl', '');
        }
      })
    },
    updateCurImgUrl(url) {
      this.curImgUrl = url;
    },
    onMouseOver() {
      this.imageMaskVisible = true;
    },
    onMouseLeave() {
      this.imageMaskVisible = false;
    },
    // 处理表单数据
    handleFormData(file) {
      let params = new FormData()
      params.append('file', this.uploadFile, this.uploadFile.name)
      return params
    },
  },
  mounted() {
  },
};
</script>

<style scoped lang="less">
  .upload-wrap{
    display: flex; flex-direction: row; align-items: center;
    .upload-box{
      &.disabled{
        cursor: not-allowed;
        .img-wrap{
          cursor: not-allowed !important;
        }
      }
      .img-upload-wrap{
        width: 104px; height: 104px; background: #F7F8F9;
        .img-wrap{
          width: 104px; height: 104px;
          position: relative;
          display: flex;
          &.pointer{ cursor: pointer; }
          img{ width: 104px; object-fit: contain;}
          .cover{
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%; height: 100%;
            font-size: 14px; color: pink;
            &.mask{
              background: rgba(0, 0, 0, 0.5);
              display: flex; align-items: center; justify-content: center;
            }
            .add{
              margin-top: 24px;
              font-size: 18px; text-align: center; line-height: 18px;
              color: #1A2A34;
            }
            .click-upload{
              margin-top: 10px; text-align: center;
              font-size: 14px; line-height: 22px;
              color: #1A2A34;
            }
            .preview-icon{
              width: 16px; height: 16px; border: 0;
              cursor: pointer;
            }
            .delete-icon{
              margin-left: 16px;
              width: 16px; height: 16px; border: 0;
              cursor: pointer;
            }
          }
        }
      }
    }
    .tips{
      margin-left: 10px;
      p{ font-size: 14px; line-height: 22px; color: #949494; }
    }
  }
  .image-preview-box{
    /deep/.ivu-modal{
      top: 50px !important;
      width: 700px !important;
    }
    .preview-wrap{
      display: flex; justify-content: center; align-items: center;
      width: 100%;height: 501px;
      .preview-image{
        width: 500px; height: 500px;
        object-fit: contain;
      }
    }
  }
</style>
