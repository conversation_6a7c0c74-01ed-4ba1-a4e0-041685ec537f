import fetch from '@/utils/network/fetch'

// 获取积分列表
const getIntegralList = storeNumber => {
  return fetch(`/api/v1/benefit_plan?storeNumber=${storeNumber}`)
}

const getOneIntegraDetail = id => {
  return fetch(`/api/v1/benefit_plan/${id}`)
}

const creatOneIntegra = params => {
  return fetch('/api/v1/benefit_plan', { method: 'post', data: JSON.stringify(params) })
}
// 更新一个积分详情
// // 更新订单
const updateOneIntegraDetail = (params = {}) => {
  return fetch(`/api/v1/benefit_plan/${params.planId}`, { method: 'put', data: JSON.stringify(params) })
}
export default {
  getIntegralList,
  getOneIntegraDetail,
  creatOneIntegra,
  updateOneIntegraDetail
}
