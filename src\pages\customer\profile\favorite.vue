<template>
  <div class="container">
		<i-modal footer-hide width="1000" title="偏好管理" v-model="show" :mask-closable="false" @on-cancel="cancelClick">
    	<div class="modal-content mt30 flex column">
				<i-checkbox-group v-model="sportsCategories" @on-change="checkSportsCategories">
					<i-row>
						<i-col :span="6" v-for="cate in categories" :key="cate.id" class="mt10">
							<i-checkbox :label="cate.name"/>
						</i-col>
					</i-row>
				</i-checkbox-group>
			</div>

			<div class="mt30 flex row justify-center">
				<i-button class="mr30" @click="cancelClick" size="large" shape="circle" style="width: 100px">取消</i-button>
				<i-button type="primary" @click="certainClick" size="large" shape="circle" style="width: 100px">确认</i-button>
			</div>
		</i-modal>
  </div>
</template>
<script>
import cfg from '../config'
export default {
	name: 'favorite_category_modal',
	props: {
		id: '',
		dktPersonId: '',
		favorites: '',
		certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    }
	},
	data() {
		return {
			show: true,
			categories: [],											// 活动分类
			sportsCategories: this.favorites,		// 选中爱好分类
		}
	},
	mounted() {
		this.getCategories()
	},
	methods: {
		// 获取分类列表
    async getCategories() {
      let resp = await this.$api.getSportsCategories()
      this.categories = resp.data
		},
		// 验证勾选是否超过5个
		checkSportsCategories() {
			if (this.sportsCategories.length > 6) {
				this.$message.warning({ content: '每个客户最多可以选择6种偏好', closable: true, duration: 3 })
				this.sportsCategories.pop()
			}
		},
		async certainClick() {
			let favorites = this.categories.filter((c) => this.sportsCategories.indexOf(c.name) > -1)
			let ids = []
			let updates = []
			favorites.forEach((value) => {
				ids.push(value.id)
				updates.push(value)
			})
			let data = { 'membershipId': this.id, 'dktPersonId': this.dktPersonId, 'categoryIds': ids }
			let resp = await this.$api.updateFavoriteCategories(data);
			if (resp['status'] == 'success') {
				this.certain && this.certain(updates)
			}
		},
		cancelClick() {
			this.show = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    }
	}
}
</script>
<style lang="less" scoped>
.modal-content {
  width: 100%;
}
</style>