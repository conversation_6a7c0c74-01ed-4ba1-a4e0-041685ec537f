<template>
  <div class="container">
    <i-row class="text-left mt20 mb30">
      <i-button 
        icon="md-add"
        @click="createRole"
      >新增</i-button>
    </i-row>
    <vue-good-table
      :rows="permissionList"
      :columns="roleList"
    >
      <template slot="table-column" slot-scope="columnProps">
        <span v-if="columnProps.column && columnProps.column.title">
          <i-dropdown @on-click="name => roleOperationClick(name)">
            <a href="javascript:void(0)">
              {{columnProps.column.title}}
              <i-icon type="ios-arrow-down"></i-icon>
            </a>
            <i-dropdownMenu slot="list">
              <i-dropdownItem
                v-for="oper in operations"
                :key="oper.value"
                :name="oper.value"
              >
               {{oper.label}}
              </i-dropdownItem>
            </i-dropdownMenu>
          </i-dropdown>
        </span>
      </template>
      
      <template slot="table-row" slot-scope="rowProps">
        <span v-if="rowProps.column.field === 'permissions_field'">
          {{rowProps.row.title}}
        </span>
        <span v-else>
          <i-icon v-hasPer="{pers: rowProps.column.permissions, per: rowProps.row}" type="md-checkmark"/>
        </span>
      </template>
    </vue-good-table>
  </div>
</template>

<script>
import pageConfig from './config'


export default {
  name: 'role',
  data() {
    return {
      // 权限列表
      permissionList: [],
      // 角色列表
      roleList: [{
        label: 'Name',
        field: 'permissions_field',
        title: '',
        sortable: false
      }],
      // 角色操作
      operations: pageConfig.operations
    }
  },
  directives: {
    // 权限指令
    hasPer: {
      inserted:(el, binding) => {
        let pers = binding.value.pers
        let per = binding.value.per
        let judge = pers.find(item => {
          if (per.id === item.id) {
            return true
          }
        })
        if (!judge) {
          el.parentNode.removeChild(el)
        }
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 数据初始化
    async loadData() {
      let rowConfig = {
        thClass: 'th-class',
        tdClass: 'td-class',
        sortable: false,
        width: 200
      }
      let roleRes = await this.$api.getRoles()
      let arr = roleRes.data.map(item => {
        item.field = item.title
        Object.assign(item, rowConfig)
        return item
      })
      this.roleList = this.roleList.concat(arr)

      let params = { loading: true }
      let permissionsRes = await this.$api.getPermissions(params)
      this.permissionList = permissionsRes.data
    },

    // 添加角色
    createRole() {
    },

    // 角色操作
    roleOperationClick(value) {
      
    }
  }
}
</script>

<style lang="less" scoped>

</style>