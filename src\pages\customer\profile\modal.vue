<template>
	<div class="container">
		<i-modal
			:mask-closable="false"
			:styles="{ top: '5vh' }"
			:title="title"
			@on-cancel="cancelClick"
			footer-hide
			v-model="show"
			width="80vw"
		>
			<div class="modal-container">
				<div class="modal-content mt30 flex column">
					<i-row>
						<i-col :span="11">
							<div class="modal-content-header row item-center justify-between">
								<div class="f-600">个人信息</div>
								<i-button @click="handleCustomerEdit" size="small" type="primary" v-has="['CUSTOMER_DETAIL_UPDATE']">更新信息</i-button>
							</div>
						</i-col>
            <i-col :span="8">
							<div class="revoke">
								<i-button @click="showRevokeModal(true)" size="small" type="error" v-has="['ACCOUNT_DEACTIVATE']">注销</i-button>
							</div>
						</i-col>
					</i-row>
					<i-row>
						<i-col :span="11">
							<div class="border-line"></div>
						</i-col>
					</i-row>
					<i-row>
						<i-col :span="6">
							<div>姓: {{ customerInfo.dktLastName }}</div>
							<div class="mt20">名: {{ customerInfo.dktFirstName }}</div>
						</i-col>
						<i-col :span="6">
							<div>生日: {{ customerInfo.dktBirthday }}</div>
							<div class="mt20">性别: {{ customerInfo.genderCn }}</div>
						</i-col>
						<i-col :span="6">
							<div>电子邮箱: {{ customerInfo.dktEmail }}</div>
							<div class="mt20">
                手机号码: {{ customerInfo.dktMobile }}
                <i-button @click="showPhoneModify(true)" size="small" type="primary" v-has="['PHONE_NUMBER_UPDATE']">修改</i-button>
              </div>

						</i-col>
						<i-col :span="6">
							<div>会员卡号: {{ customerInfo.dktCardNo }}</div>
							<div class="mt20">偏好门店: {{ customerInfo.preferStore }}</div>
						</i-col>
						<i-col :span="6">
							<div class="mt20">是否订阅: {{ customerInfo.dktOptin == true ? '是' : '否' }}</div>
						</i-col>
						<i-col :span="6">
							<div class="mt20">
								黑名单: {{ (customerInfo.id && customerInfo.isInBlackList === true) == true ? '是' : '否' }}
								<a
									@click="onRemoveBlackListClick"
									style=" text-decoration: underline; margin-left: 10px;"
									v-if="customerInfo.id && customerInfo.isInBlackList"
								>移除黑名单</a>
							</div>
						</i-col>
					</i-row>
				</div>
				<div class="mt20">
					<i-row>
						<i-col :span="11">
							<div class="modal-content mt30 flex column">
								<div class="modal-content-header row item-center justify-between">
									<div class="f-600">积分变更</div>
									<i-button @click="changePoint" size="small" type="primary" v-has="['CUSTOMER_DETAIL_UPDATE']">提交变更</i-button>
								</div>
								<div class="border-line"></div>
								<div style="width: 100%;">
									<i-row class>
										<i-col :span="8">当前账户积分: {{ customerInfo.dktPointBalance }}</i-col>
									</i-row>
									<i-row class="text-center item-center" justify="space-between" type="flex">
										<i-col :span="6">
											<i-radio-group
												:key="item.value"
												@on-change="changeOperation"
												v-for="item in pointOpList"
												v-model="point.type"
											>
												<i-radio :label="item.value">{{ item.label }}</i-radio>
											</i-radio-group>
										</i-col>
										<i-col :span="6">
											<i-input clearable placeholder="请输入积分" size="small" v-model="point.value" />
										</i-col>
										<i-col :span="11">
											<i-select clearable placeholder="变更原因" size="small" style="width: 100%" v-model="point.reason">
												<i-option :key="s" :value="s" v-for="s in reasonList">{{ s }}</i-option>
											</i-select>
										</i-col>
									</i-row>
								</div>
								<div>
									<i-input
										:autosize="{ minRows: 3, maxRows: 8 }"
										class="mt20"
										placeholder="积分变更备注"
										size="small"
										type="textarea"
										v-model="point.comments"
									/>
								</div>
							</div>
						</i-col>
						<i-col :push="2" :span="11">
							<div class="modal-content mt30 flex column">
								<div class="modal-content-header row item-center justify-between">
									<div class="f-600">运动爱好</div>
									<i-button @click="manageCategory" size="small" type="primary">管理爱好</i-button>
								</div>
								<div class="border-line"></div>
								<i-row>
									<i-col :key="sc.code" :span="6" class="mt20" v-for="sc in customerInfo.sportsCategories">
										<i-button size="small" style="width: 95%">
											<i-tooltip :content="sc.name">{{ sc.name }}</i-tooltip>
										</i-button>
									</i-col>
								</i-row>
							</div>
						</i-col>
					</i-row>
				</div>

				<i-tabs class="tab-content mt30 flex column" size="small" value="purchases">
					<!-- 购买记录 -->
					<i-tab-pane label="购买记录" name="purchases">
						<i-table :columns="orderColumns" :data="orders" border></i-table>
						<div style="margin-top: 10px; overflow: hidden">
							<div style="float: right;">
								<i-button-group>
									<i-button :disabled="disablePrev" @click="prevOrders">
										<i-icon type="ios-arrow-back" />上一页
									</i-button>
									<i-button :disabled="disableNext" @click="nextOrders">
										下一页
										<i-icon type="ios-arrow-forward" />
									</i-button>
								</i-button-group>
							</div>
						</div>
					</i-tab-pane>

					<!-- 积分记录 -->
					<i-tab-pane label="积分记录" name="points">
						<i-table :columns="pointRecordColumns" :data="pointsRecords" border></i-table>
						<div style="margin-top: 10px; overflow: hidden">
							<div style="float: right;">
								<i-page :page-size="pointPage.size" :total="pointPage.total" @on-change="changePointsRecordsPageNo" show-total />
							</div>
						</div>
					</i-tab-pane>

					<!-- 我的优惠券 -->
					<i-tab-pane label="我的优惠券" name="coupon3">
						<i-table :columns="coupon3Columns" :data="coupon3List" border></i-table>
						<div style="margin-top: 10px; overflow: hidden">
							<div style="float: right;">
								<i-page :page-size="coupon3Page.size" :total="coupon3Page.total" @on-change="changeCoupon3PageNo" show-total />
							</div>
						</div>
					</i-tab-pane>

					<!-- 积分礼券 -->
					<i-tab-pane label="积分礼券" name="coupon99">
						<i-table :columns="coupon99Columns" :data="coupon99List" border></i-table>
						<div style="margin-top: 10px; overflow: hidden">
							<div style="float: right;">
								<i-page
									:page-size="coupon99Page.size"
									:total="coupon99Page.total"
									@on-change="changeCoupon99PageNo"
									show-total
								/>
							</div>
						</div>
					</i-tab-pane>

					<!-- 积分兑换 -->
					<i-tab-pane label="积分兑换" name="orders" v-if="customerInfo.id">
						<i-table :columns="pointOrderColumns" :data="pointOrderList" border></i-table>
						<div style="margin-top: 10px; overflow: hidden">
							<div style="float: right;">
								<i-page
									:page-size="pointOrderPage.size"
									:total="pointOrderPage.total"
									@on-change="changePointOrderPageNo"
									show-total
								/>
							</div>
						</div>
					</i-tab-pane>
				</i-tabs>
			</div>
		</i-modal>

    <!-- 修改手机，发送验证码 -->
    <phone-modify-modal
      v-if="phoneModifyVisibility"
			:customer="customerInfo"
      @hidePhoneModify="showPhoneModify(false)"
      @phoneModifySuccess="phoneModifySuccess"
    />
    <!-- 注销会员 -->
    <member-revoke-modal
      v-if="showRevokeDialog"
			:customer="customerInfo"
      @hideRevoke="showRevokeModal(false)"
    />

		<f-modal
			:cancel="favoriteCancel"
			:certain="favoriteCertain"
			:dkt-person-id="customerInfo.dktPersonId"
			:favorites="favorites"
			:id="customerInfo.id"
			v-if="showFavoriteDialog"
		/>

		<ci-modal
			:cancel="infoCancel"
			:certain="infoCertain"
			:customer="customerInfo"
			:store-list="storeList"
			v-if="showInfoDialog"
		/>
	</div>
</template>
<script>
import cfg from '../config'
import { Modal } from 'iview'
import { checkFill, judgeFill, isPositive } from '@/utils/util'
import CustomerInfoModal from './info'
import FavoriteModal from './favorite'
import expandRow from './table-expand.vue'
import phoneModifyModal from './phoneModifyModal'
import memberRevokeModal from './memberRevokeModal'
import { constants } from 'fs'

export default {
  name: 'customer_detail_modal',
  components: {
    'phone-modify-modal': phoneModifyModal,
    'member-revoke-modal': memberRevokeModal,
    'ci-modal': CustomerInfoModal,
    'f-modal': FavoriteModal
  },
  props: {
    title: '',
    customer: {
      id: '',
      dktFirstName: '',
      dktLastName: '',
      gender: 0,
      dktCardNo: '',
      dktMobile: '',
      dktEmail: '',
      dktBirthday: '',
      dktOptin: '',
      orders: [],
      dktStoreNumber: '',
      preferStore: '',
      pointsRecords: [],
      coupon3Records: [],
      coupon99Records: [],
      pointOrders: []
    },
    cancel: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      show: true,
      customerInfo: {},
      // 购买记录相关数据
      orderPage: { no: 1, size: 10 },
      disablePrev: true,
      disableNext: false,
      orderColumns: [
        {
          type: 'expand',
          width: 50,
          render: (h, params) => {
            return h(expandRow, {
              props: { items: params.row.items }
            })
          }
        },
        {
          title: '收银员',
          key: 'cashier_number',
          className: 'th-class',
          width: 80
        },
        {
          title: '收银台',
          key: 'till_number',
          className: 'th-class',
          width: 80
        },
        {
          title: '交易号',
          key: 'transaction_number',
          className: 'th-class',
          width: 150
        },
        {
          title: '购买渠道',
          key: 'purchase_channel',
          className: 'th-class',
          width: 130
        },
        {
          title: '订单总金额',
          key: 'total_price',
          className: 'th-class',
          width: 150
        },
        {
          title: '购物日期',
          key: 'purchase_date',
          className: 'th-class',
          width: 150
        },
        {
          title: '订单状态',
          key: 'transaction_status',
          className: 'th-class',
          render: (h, params) => {
            return h(
              'span',
              cfg.convert(
                params.row.transaction_status,
                params.row.channel === 'digital'
                  ? cfg.digitalPurchaseStatusList
                  : cfg.purchaseStatusList
              )
            )
          }
        }
      ],
      orders: [],

      // 积分记录相关数据
      pointPage: {
        no: 1,
        size: 3,
        total: 0
      },
      pointRecordColumns: [
        {
          title: '变更时间',
          key: 'updateTime',
          className: 'th-class',
          width: 200,
          render: (h, params) => {
            return h(
              'span',
              new Date(params.row.updateTime).format('yyyy-MM-dd hh:mm')
            )
          }
        },
        { title: '事件', key: 'eventType', className: 'th-class', width: 300 },
        {
          title: '积分变更',
          key: 'pointChange',
          className: 'th-class',
          width: 200
        },
        { title: '备注', key: 'comment', className: 'th-class' }
      ],
      pointsRecords: [],

      // 我的优惠券相关数据
      coupon3Page: {
        no: 1,
        size: 3,
        total: 0
      },
      coupon3Columns: [
        { title: '名称', key: 'name', className: 'th-class', width: 300 },
        {
          title: '过期时间',
          key: 'expireTime',
          className: 'th-class',
          width: 400,
          render: (h, params) => {
            return h(
              'span',
              new Date(params.row.expireTime).format('yyyy-MM-dd hh:mm')
            )
          }
        },
        { title: '券号', key: 'itemCode', className: 'th-class' }
      ],
      coupon3List: [],

      // 积分礼券相关数据
      coupon99Page: {
        no: 1,
        size: 3,
        total: 0
      },
      coupon99Columns: [
        { title: '名称', key: 'name', className: 'th-class', width: 300 },
        {
          title: '过期时间',
          key: 'expireTime',
          className: 'th-class',
          width: 400,
          render: (h, params) => {
            return h(
              'span',
              new Date(params.row.expireTime).format('yyyy-MM-dd hh:mm')
            )
          }
        },
        { title: '券号', key: 'itemCode', className: 'th-class' }
      ],
      coupon99List: [],

      // 积分礼券相关数据
      pointOrderPage: {
        no: 1,
        size: 3,
        total: 0
      },
      pointOrderColumns: [
        {
          title: '产品名称',
          key: 'name',
          className: 'th-class',
          width: 300,
          render: (h, params) => {
            return h('span', params.row.pointOrderItems[0].productInfo.title)
          }
        },
        {
          title: '兑换门店',
          key: 'dktStoreName',
          className: 'th-class',
          width: 400
        },
        { title: '兑换积分', key: 'usedPoints', className: 'th-class' }
      ],
      pointOrderList: [],

      pointOpList: cfg.pointOpList,
      reasonList: cfg.changeReasonList,
      point: {
        type: '',
        value: '',
        reason: '',
        comments: ''
      },

      // 喜好选择
      showFavoriteDialog: false,
      favorites: [],
      // 门店列表
      storeList: [],
      // 修改手机弹窗
      phoneModifyVisibility: false,
      // 注销弹窗
      showRevokeDialog: false,
      showInfoDialog: false
    }
  },
  mounted() {
    // 处理购买记录
    const purchasesParams = {
      pageNo: this.orderPage.no,
      pageSize: this.orderPage.size,
      dktCardNo: this.customer.dktCardNo
    }
    this.handlePurchases(purchasesParams)

    // 处理积分记录
    const pointsRecordParams = {
      pageNo: this.pointPage.no,
      pageSize: this.pointPage.size,
      dktCardNo: this.customer.dktCardNo
    }
    this.handlePointsRecord(pointsRecordParams)

    // 处理我的优惠券
    const coupon3Params = {
      type: 3,
      pageNo: this.coupon3Page.no,
      pageSize: this.coupon3Page.size,
      dktCardNo: this.customer.dktCardNo
    }
    this.handleCoupon3(coupon3Params)

    // 处理积分礼券
    const coupon99Params = {
      type: 99,
      pageNo: this.coupon99Page.no,
      pageSize: this.coupon99Page.size,
      dktCardNo: this.customer.dktCardNo
    }
    this.handleCoupon99(coupon99Params)

    // 处理积分订单
    if (this.customer.id) {
      let params = {
        membershipId: this.customer.id,
        pageNo: this.pointOrderPage.no,
        pageSize: this.pointOrderPage.size
      }
      this.handlePointOrder(params)
    }

    // 门店获取
    this.getAllStoreList()
  },

  methods: {
    // 获取所有门店列表
    async getAllStoreList() {
      const res = await this.$api.getAllStoreList()
      if (res) {
        const list = res.data || []
        const perferStore = this.findPerferStore(list, this.customer)
        this.storeList = res.data
        this.customer.preferStore = perferStore.name
      }
      this.customerInfo = this.customer
    },

    // 购买记录上一页
    async prevOrders() {
      if (this.orderPage.no > 1) this.orderPage.no--

      if (this.orderPage.no === 1) {
        this.disablePrev = true
      } else {
        this.disablePrev = false
      }

      const params = {
        pageNo: this.orderPage.no,
        pageSize: this.orderPage.size,
        dktCardNo: this.customer.dktCardNo
      }
      this.handlePurchases(params)
      this.disableNext = false
    },
    // 购买记录下一页
    async nextOrders() {
      this.orderPage.no++

      const params = {
        pageNo: this.orderPage.no,
        pageSize: this.orderPage.size,
        dktCardNo: this.customer.dktCardNo
      }
      this.handlePurchases(params)
      this.disablePrev = false
    },

    // 积分变更
    async changePointsRecordsPageNo(no) {
      const params = {
        pageNo: no,
        pageSize: this.pointPage.size,
        dktCardNo: this.customer.dktCardNo
      }
      this.handlePointsRecord(params)
    },

    // 官网免邮券
    async changeCoupon3PageNo(no) {
      const params = {
        type: 3,
        pageNo: no,
        pageSize: this.coupon3Page.size,
        dktCardNo: this.customer.dktCardNo
      }
      this.handleCoupon3(params)
    },

    // 积分礼券
    async changeCoupon99PageNo(no) {
      const params = {
        type: 99,
        pageNo: no,
        pageSize: this.coupon99Page.size,
        dktCardNo: this.customer.dktCardNo
      }
      this.handleCoupon99(params)
    },

    // 积分兑换
    async changePointOrderPageNo(no) {
      let params = {
        pageNo: no,
        membershipId: this.customer.id,
        pageSize: this.pointOrderPage.size
      }
      this.handlePointOrder(params)
    },

    // 用户信息更新
    handleCustomerEdit() {
      this.showInfoDialog = true
    },
    // 注销
    showRevokeModal(status = true) {
      this.showRevokeDialog = status
    },
    // 手机号码更新
    showPhoneModify(status = true) {
      this.phoneModifyVisibility = status;
    },
    // 手机号码更新成功,更新手机号
    phoneModifySuccess(phone) {
      this.customerInfo.dktMobile = phone;
    },

    // 移除黑名单
    onRemoveBlackListClick() {
      Modal.confirm({
        title: '提示',
        content: '是否移除黑名单?',
        onOk: async () => {
          console.log(this.customerInfo)
          console.log('是否移除黑名单')
          const { id } = this.customerInfo
          const res = await this.$api.removeBlackList(id)
          if (res.status === 'success') {
            this.customerInfo.isInBlackList = undefined
          }
          console.log('onRemoveBlackListClick: ', res)
        }
      })
    },
    // 管理爱好
    manageCategory() {
      this.favorites = []
      if (
        this.customerInfo.sportsCategories &&
        this.customerInfo.sportsCategories.length > 0
      ) {
        this.customerInfo.sportsCategories.forEach(value =>
          this.favorites.push(value.name)
        )
      }
      this.showFavoriteDialog = true
    },

    changeOperation() {
      switch (this.point.type) {
        case '+':
          this.placeholder = '增加积分值'
          break
        case '-':
          this.placeholder = '减少积分值'
          break
      }
    },
    // 提交积分变更
    async changePoint() {
      this.point['dktCardNo'] = this.customer.dktCardNo
      this.point['mobile'] = this.customer.dktMobile
      if (!isPositive(this.point.value)) {
        this.$message.warning({
          content: '变更积分值须为正数！如是扣积分,请选择减少.',
          closable: true,
          duration: 3
        })
        return
      }
      let resp = await this.$api.updatePoints(this.point)
      if (resp['status'] == 'success') {
        this.$message.success({
          content: '积分变更成功!',
          closable: true,
          duration: 3
        })
        this.point = {}
      }
    },

    cancelClick() {
      this.show = false
      setTimeout(() => {
        this.cancel && this.cancel()
      }, 300)
    },

    infoCertain(customer) {
      this.showInfoDialog = false
      const findStore = this.findPerferStore(this.storeList, customer)
      this.customerInfo = Object.assign(this.customerInfo, customer, {
        preferStore: findStore.name
      })
    },
    infoCancel() {
      this.showInfoDialog = false
    },

    favoriteCertain(updates) {
      this.customerInfo.sportsCategories = updates
      this.showFavoriteDialog = false
    },
    favoriteCancel() {
      this.showFavoriteDialog = false
    },

    /**
     * 数据处理
     */

    // 处理积分记录数据
    async handlePointsRecord(params) {
      const resp = await this.$api.searchPoints(params)
      if (resp.status === 'success') {
        this.pointsRecords = resp.data.items || []
        this.pointPage.total = resp.data.total
      }
    },

    // 处理我的优惠券数据
    async handleCoupon3(params) {
      const resp = await this.$api.searhCoupons(params)
      if (resp.status === 'success') {
        this.coupon3List = resp.data.items || []
        this.coupon3Page.total = resp.data.total
      }
    },

    // 处理积分礼券数据
    async handleCoupon99(params) {
      const resp = await this.$api.searhCoupons(params)
      if (resp.status === 'success') {
        this.coupon99List = resp.data.items || []
        this.coupon99Page.total = resp.data.total
      }
    },

    // 处理积分订单数据
    async handlePointOrder(params) {
      const resp = await this.$api.searchOrders(params)
      if (resp.status === 'success') {
        this.pointOrderList = resp.data.items || []
        this.pointOrderPage.total = resp.data.total
      }
    },

    // 处理购买记录数据
    async handlePurchases(params) {
      let resp = await this.$api.searchPurchases(params)
      if (resp && resp.status === 'success') {
        this.orders = resp.data || []

        this.disableNext = this.orders.length < 1
      }
    },

    // 查找偏好门店
    findPerferStore(list, target) {
      if (list && list.length <= 0) return {}
      let perferStore = null
      perferStore = list.find(item => {
        if (item.dktStoreNumber === target.dktStoreNumber) {
          return item
        }
      })
      return perferStore || {}
    }
  }
}
</script>

<style lang="less" scoped>
.modal-container {
  padding: 0px 40px;
  height: 78vh;
  overflow: auto;
  /deep/.modal-content {
    width: 100%;
    &-header {
      width: 100%;
    }
    .border-line {
      width: 100%;
      height: 1px;
      margin: 5px 0 20px;
      background: rgba(221, 221, 221, 1);
    }
    .th-class {
      text-align: center !important;
      vertical-align: middle !important;
      min-width: 120px !important;
    }
    .text-center {
      text-align: center;
      vertical-align: middle;
    }
    .customer-text {
      width: 240px;
    }
    .revoke{
      margin-left: 20px;
      .ivu-btn-error{
        color: #fff !important;
        background-color: #ed4014 !important;
        border-color: #ed4014 !important;
      }
    }
  }
}
.f-600 {
  font-weight: 700;
}
</style>
