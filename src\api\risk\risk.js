import fetch from '@/utils/network/fetch'

// 黑白名单
const getNameListTable = (query = {}) => {
  return fetch('/api/v1/audit/user/risk/statics', { data: query })
}
const getRiskMemberList = query => {
  return fetch(`/api/v1/audit/user/risk`, { data: query})
}
const postRowData = data => {
  return fetch(`/api/v1/audit/imports/refine`, { method: 'post' , data: JSON.stringify(data)})
}
const deleteRowData = (biz_type, is_blacklist,person_id) => {
  return fetch(`/api/v1/audit/imports?biz_type=${biz_type}&is_blacklist=${is_blacklist}&person_id=${person_id}`, { method: 'delete' })
}
const postTableQuery = data => {
  return fetch(`/api/v1/audit/imports/refines`, data)
}
const postTableData = (data, biz_type, is_blacklist) => {
  return fetch(`/api/v1/audit/imports?biz_type=${biz_type}&is_blacklist=${is_blacklist}`, { method: 'post' , data: JSON.stringify(data)})
}

// 场景

const queryAllScene = (data) => {
  return fetch(`/api/v1/audit/scene`, { method: 'get' , data: JSON.stringify(data)})
}
const querySceneDetail = (data) => {
  return fetch(`/api/v1/audit/scene/detail?scene_id=${data}`, { method: 'get' })
}
const querySceneBlackList = (data) => {
  return fetch(`/api/v1/audit/blacklist`, { method: 'get' , data: data })
}
const querySceneBlackListDetail = (data) => {
  return fetch(`/api/v1/audit/hit/records?black_id=${data}`, { method: 'get' })
}
const createScene = (data) => {
  return fetch(`/api/v1/audit/scene`, { method: 'post', data: JSON.stringify(data)})
}
const updateScene = (data, ) => {
  return fetch(`/api/v1/audit/scene`, { method: 'put', data: JSON.stringify(data)})
}
const bindRules = (data) => {
  return fetch(`/api/v1/audit/scene/rule?scene_id=${data.scene_id}&rule_id=${data.rule_id}`, { method: 'post' })
}
const changeRulesPoint = (data, ) => {
  return fetch(`/api/v1/audit/scene/rule`, { method: 'put', data: JSON.stringify(data)})
}
const deleteRules = (data, ) => {
  return fetch(`/api/v1/audit/scene/rule?scene_id=${data.scene_id}&rule_id=${data.rule_id}`, { method: 'delete' })
}

// 规则
const queryAllRules = (data) => {
  return fetch(`/api/v1/audit/rule`, { method: 'get' , data: JSON.stringify(data)})
}
const queryRulesDetail = (data) => {
  return fetch(`/api/v1/audit/rule/detail?rule_id=${data}`, { method: 'get' })
}
const updateRules = (data) => {
  return fetch(`/api/v1/audit/rule`, { method: 'put' , data: JSON.stringify(data)})
}
const creatRules = (data) => {
  return fetch(`/api/v1/audit/rule`, { method: 'post' , data: JSON.stringify(data)})
}
const queryRules = (data) => {
  return fetch(`/api/v1/audit/params`, { method: 'get' })
}
const ruleHitRecord = (data) => {
  return fetch(`/api/v1/audit/rule/hit/records?card_number=${data.card_number}&rule_id=${data.rule_id}&page=${data.page}&size=${data.size}`, { method: 'get' })
}
// 更新规则系统参数
const updateRuleSystemParam = (ruleData) => {
  return fetch('/api/v1/risk/rule/system-param', {
    method: 'put',
    data: JSON.stringify(ruleData),
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
const queryRulesByName = (data) => {
  return fetch(`/api/v1/audit/rule?rule_name=${encodeURIComponent(data.name)}`, { method: 'get' })
}

export default {
  getNameListTable,
  getRiskMemberList,
  postRowData,
  deleteRowData,
  postTableQuery,
  postTableData,
  updateRuleSystemParam,
  queryAllScene,
  queryAllRules,
  queryRulesDetail,
  querySceneDetail,
  querySceneBlackList,
  querySceneBlackListDetail,
  createScene,
  updateScene,
  bindRules,
  changeRulesPoint,
  deleteRules,
  updateRules,
  creatRules,
  queryRules,
  ruleHitRecord,
  queryRulesByName,
}
