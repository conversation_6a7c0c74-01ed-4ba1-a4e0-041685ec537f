<template>
  <div class="phone-modify-wrapper">
    <i-modal :title="modalTitle" class="vertical-center-modal" footer-hide v-model="visible" @on-cancel="cancelClick">
      <div class="modal-container">
        <p class="red-tips">请顾客在修改前在各渠道解绑会员，避免使用异常。</p>
        <i-form :label-width="88" label-position="left">
          <i-row>
            <i-col>
              <i-form-item class="integralList-form" label="新手机号码：">
                <div style="display: flex;">
                  <i-input
                    class="integralList-form-input"
                    type="number"
                    placeholder="请输入新手机号"
                    @on-change="handlePhoneChange()"
                    v-model="modalData.phone"
                  />
                  <i-button :class="['sms-code', isCounting ? 'count-down':'']" @click="handleSendSMS" type="primary" :disabled="isCounting">
                    发送验证码
                    <p v-if="isCounting">{{ countdown }}s</p>
                  </i-button>
                </div>
                <!-- <p class="color-red" v-show="rules.planName.show">{{rules.planName.error}}</p> -->
              </i-form-item>
            </i-col>
          </i-row>
          <i-row>
            <i-col>
              <i-form-item class="integralList-form" label="验证码：">
                <div style="display: flex;">
                  <i-input
                    class="integralList-form-input"
                    placeholder="请输入验证码"
                    v-model="modalData.sms"
                  />
                </div>
              </i-form-item>
            </i-col>
          </i-row>
          <div class="bottom-btns mt10 text-center">
            <i-button @click="cancelClick" class="cancel-btn mr30">取消</i-button>
            <i-button @click="handleModifyPhone" type="primary" class="confirm-btn">提交</i-button>
          </div>
        </i-form>
      </div>
      
		</i-modal>
  </div>
</template>

<script>
export default {
  name: 'PhoneModifyModal',
  props: {
    customer: {
      type: Object,
      default: () => {
        return {
          dktPersonId: '', // personId
          id: '', // membershipId
        };
      }
    }
  },
  data() {
    return {
      visible: true,
      isCounting: false,
      countdown: 59,
      modalData: {
        phone: '',
        sms: ''
      },
      modalTitle: '修改手机',
    };
  },
  methods: {
    // Your methods go here
    cancelClick() {
      this.$emit('hidePhoneModify');
    },
    handlePhoneChange() {
      // 手机号码输入框变化
      this.$nextTick(() => {
        if(this.modalData.phone.length > 11) {
          this.modalData.phone = this.modalData.phone.slice(0, 11);
          console.log('>>>>> 手机号码输入框 value', this.modalData.phone);
        }
      });
    },
    isPhoneValid(phoneNumber) {
      const phoneRegex = /^1\d{10}$/;
      const isValid = phoneRegex.test(phoneNumber);
      return isValid;
    },
    handleSendSMS() {
      // 发送短信验证码
      console.log('>>>>> 发送短信验证码');
      const isValid = this.isPhoneValid(this.modalData.phone);
      if(!isValid) {
        this.$message.error('请输入正确的手机号码');
        return;
      }
      this.goSendSMS();
    },
    async goSendSMS() {
      // 发送短信验证码
      console.log('>>>>> goSendSMS');
      let params = {
        client_id: 'membership_cn',
        country_code: 'CN',
        language: 'ZH',
        mobile: this.modalData.phone,
        ppays: 'CN',
        state: 200
      };
      let res = await this.$api.sendVerifyCode(params);
      console.log('>>>send msg res', res)
      if(res) {
        this.updateCountDown();
      }
    },
    updateCountDown() {
      // send SMS code
      this.isCounting = true;
      this.countdown = 59;
      const interval = setInterval(() => {
        this.countdown--;
        if (this.countdown === 0) {
          clearInterval(interval);
          this.isCounting = false;
        }
      }, 1000);
    },
    handleModifyPhone() {
      // 提交修改手机号
      console.log('>>>>> 提交修改手机号');
      const isValid = this.isPhoneValid(this.modalData.phone);
      if(!isValid) {
        this.$message.error('请输入正确的手机号码');
        return;
      }
      if(this.modalData.sms.length < 1) {
        this.$message.error('请输入验证码');
        return;
      }
      this.goModifyPhone();
    },
    async goModifyPhone() {
      // 提交修改手机号
      const { phone, sms } = this.modalData;
      const { dktPersonId, id } = this.customer;
      let params = {
        person_id: dktPersonId,
        new_mobile: phone,
        code: sms,
        membership_id: id || null
      };
      let res = await this.$api.modifyPhoneNum(params);
        if(res && res.status === 'success') {
          this.$message.success('修改成功');
          this.cancelClick();
          this.$emit('phoneModifySuccess', phone);
        }
    }
    
  },
  mounted() {
  },
};
</script>

<style lang="less" scoped>
  .vertical-center-modal{
    .modal-container{
      padding: 0 10px;
    }
    /deep/.ivu-modal-body{
      .red-tips{
        color: red;
        font-weight: bolder;
        padding: 0 0 14px;
      }
      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }
      // input[type="number"]{
      //   -moz-appearance: textfield;
      // }
      .ivu-input-wrapper{
        width: 170px;
      }
      .bottom-btns{
        .cancel-btn, .confirm-btn{
          width: 100px;
        }
      }
    }
    .sms-code{
      margin-left: 14px;
      &.count-down{
        padding: 0 15px;
        line-height: 13px;
      }
    }
  }

</style>