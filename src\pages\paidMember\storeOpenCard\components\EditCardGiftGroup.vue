<template>
  <div class="edit-open-card-group">
    <i-modal :mask-closable="false" footer-hide title="编辑产品" :closable="false" v-model="cardGiftGroupShow" width="1200">
      <div class="word-wrap">
			  <i-table border :columns="productColumns" :data="productList" class="product-template-table-box" >
          <!-- 库存 -->
          <template slot-scope="{ index }" slot="remaining_stock">
            <i-input placeholder="" class="stock-input" v-model="productList[index].remaining_stock" />
          </template>
          <template slot-scope="{ index, row }" slot="operate">
            <i-button type="warning" @click="showRemoveSingleModal(row)">下架</i-button>
          </template>
        </i-table>
        <div class="bottom-btns">
          <i-button class="mr15" @click="hideModal">取消</i-button>
          <i-button type="primary" @click="handleStockUpdate">保存</i-button>
        </div>
      </div>
    </i-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { isPositive } from '@/utils/util'
import paidMemberApi from '@/api/paidMember/index'

export default {
  name: 'open_card_gift_group',
  props: {
    cardGiftGroupShow: {
      type: Boolean,
      default: false
    },
    orderNum: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      stock: '', // 商品库存
      productColumns: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          className: 'th-class th-class-img',
        },
        {
          title: '图片',
          key: 'imageUrls',
          width: 120,
          className: 'th-class th-class-img',
          render: (h, params) => {
            return this.showRowImage(h, params.row.attributes.image_urls)
          }
        },
        {
          title: 'Model Code',
          key: 'model_code',
          width: 120,
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.model_code)
          }
        },
        {
          title: 'Item Code',
          key: 'item_code',
          width: 120,
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.item_code)
          }
        },
        {
          title: '名称',
          key: 'title',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.title)
          }
        },
        {
          title: '简称',
          key: 'short_name',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.short_name)
          }
        },
        {
          title: '颜色',
          key: 'color',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.color)
          }
        },
        {
          title: '尺码',
          key: 'size',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.size)
          }
        },
        {
          title: '剩余库存',
          slot: 'remaining_stock',
          width: 90,
          className: 'th-class',
          // render: (h, params) => {
          //   return h('span', params.row.remaining_stock)
          // }

        },
        {
          title: '描述',
          key: 'description',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.description)
          }
        },
        {
          title: '价格',
          key: 'price',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.price)
          }
        },
        {
          title: '操作',
          slot: 'operate',
          width: 90,
          className: 'th-class',
        },
      ],
      productList: [], // 单个开卡礼插槽的产品集
    }
  },
  mounted() {
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  methods: {
    getProductList(paramsObj) {
      let { orderNum, noDataNeedReturn } = paramsObj;

      if (!orderNum) {
        console.log('>>>没有传入开卡礼卡槽的订单号');
        return
      }
      this.productList = [];
      let params = {
        store_number: this.currentStore.dktStoreNumber,
        order_number: orderNum,
      }
      paidMemberApi.getCardSlotProducts(params).then(resp => {
        if(resp.status === 'success') {
          this.productList = resp.data || [];
          if(noDataNeedReturn && this.productList.length === 0) {
            // 若下架后 当前开卡礼卡槽没有产品，则直接关闭弹窗，并刷新开卡礼列表
            this.hideModal();
            this.$emit('refreshOpenCardList');
          }
        }
      })
    },
    // 显示下架当前产品弹窗
    showRemoveSingleModal(row) {
      this.$modal.confirm({
        'title': '商品下架',
        'content': `您即将下架：<span class='confirm-msg' style="color: #3080c3">${row.attributes.title}</span> 请确认？`,
        'okText': '确定',
        'cancelText': '取消',
        'onOk': () => {
          this.removeSingleGiftItem(row)
        }
      })
    },
    // 下架当前产品
    async removeSingleGiftItem(row) {
      let params = {
        benefit_type_id: 8,
        catalogue_status: 'HALTSALE',
        store_number: this.currentStore.dktStoreNumber,
        catalogue_id: row.catalogue_id
      }
      console.log('>>>下架当前产品', params);
      let resp = await paidMemberApi.removeOpenCardGift(params);
      if (resp.status === 'success') {
        this.$message.success({
          content: '下架成功!',
          closable: true,
          duration: 3
        })

        // 重新查询当前行的开卡礼卡槽产品
        this.getProductList({
          orderNum: this.orderNum,
          noDataNeedReturn: true
        });

      }
      
    },
    hideModal() {
      this.$emit('cancel');
    },
    checkStockValid() {
      // 库存不能为空
      let stockNotFill = this.productList.some(item => {
        return item.remaining_stock === '' || item.remaining_stock === undefined
      })
      if(stockNotFill) {
        this.$message.warning({
          content: '请填写库存',
          closable: true,
          duration: 3
        })
        return false;
      }
      // 库存数量不得小于0
      let stockLessThanZero = this.productList.some(item => {
        return item.remaining_stock < 0
      })
      if(stockLessThanZero) {
        this.$message.warning({
          content: '库存数量不得小于0',
          closable: true,
          duration: 3
        })
        return false;
      }
      // 库存必须是正数
      let stockNotPositive = this.productList.some(item => {
        item.remaining_stock += ''; // 转为字符串
        return !isPositive(item.remaining_stock) && item.remaining_stock !== '0'
      })
      if(stockNotPositive) {
        this.$message.warning({
          content: '库存数量必须是正数',
          closable: true,
          duration: 3
        })
        return false;
      }
      // 库存不可超过50
      let stockOverLimit = this.productList.some(item => {
        return item.remaining_stock > 50
      })
      if(stockOverLimit) {
        this.$message.warning({
          content: '库存数量不得大于50',
          closable: true,
          duration: 3
        })
        return false;
      }
      return true;
    },
    async handleStockUpdate() {
      console.log('>>>库存更新', this.productList);
      let dktStoreNumber = this.currentStore.dktStoreNumber;
      // 发布前校验
      let stockValid = this.checkStockValid();
      if(!stockValid) { return }

      let tempArr = [];
      this.productList.forEach(item => {
        console.log('>>>库存更新的参数', item);
        tempArr.push({
          catalogue_id: item.catalogue_id,
          stock: item.remaining_stock,
          store_number: dktStoreNumber,
        })
      })
      console.log('>>>库存更新的参数', tempArr);
      // updateMultiProductStock
      let resp = await paidMemberApi.updateMultiProductStock(tempArr,dktStoreNumber);
      if(resp.status === 'success') {
        this.$message.success({
          content: '库存修改成功!',
          closable: true,
          duration: 3
        })
        // 重新获取开卡礼列表
        this.hideModal();
        this.$emit('refreshOpenCardList');

        
      }
    },
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      let generateBtn = [
        h('img', {
          attrs: {
            src: imageUrls[0],
            class: 'table-img mt5',
            width: '45px',
            height: '45px'
          }
        })
      ];
      let cla = 'tables-pic-box ';
      return h('div', { class: cla }, generateBtn)
    },
    cancelClick() {
      this.show = false
      this.$emit('cancel');
    }
  }
}
</script>

<style lang="less" scoped>
.edit-open-card-group {
  .word-wrap{ word-wrap: break-word; }
}
.stock-input{
  /deep/.ivu-input{
    text-align: center;
  }
}
.bottom-btns{
  color: red;
  display: flex; align-items: center; justify-content: center;
  margin-top: 15px;
}
</style>