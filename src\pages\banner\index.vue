<template>
  <div class="customer">
    <i-row v-if="creatBtnDisplay" class="mt20 mb30" style="padding-right: 94px;">
      <i-col offset="24" span="2">
        <i-button type="primary" @click="modalCreate" >新建banner</i-button>
      </i-col>
    </i-row>
    <i-row>
      <i-tabs value="1" v-model="activeTab" @on-click="tabClick">
        <i-tab-pane v-for="item in tabs" :key="item.name" :label="item.title" :name="item.name">
          <i-table :columns="bannerColumns" :data="bannerList" border class="product-order-table-box" ref="table" />
        </i-tab-pane>
      </i-tabs>
    </i-row>
    <div class="table-footer">
      <div style="float: right;">
        <i-page :total="page.total" show-total
                :page-size="page.size" show-sizer
                :page-size-opts="pageSizeOpts"
                @on-change="changePageNo" @on-page-size-change="changePageSize" />
      </div>
    </div>
    <z-modal v-if="modalTitle" :title="modalTitle" :modalDisplaySaveBtn="modalDisplaySaveBtn" :modalHoverImg="modalHoverImg" :size="tabNameTextMapping[activeTab].size"
             :activityType="activeTabModal" :modalStatus="modalStatus" :activitySubType="activitySubTypeModal" :certain="modalCertain" :cancel="modalCancel" :id="id" />
    <i-modal v-model="modalRemove" :closable="false" @on-ok="sureRemoveModel">
      <p class="model-confirm-title">是否确认下架？</p>
    </i-modal>
    <i-modal v-model="modalDelete" :closable="false" @on-ok="sureDeleteModel">
      <p class="model-confirm-title">是否确认删除？</p>
    </i-modal>
  </div>
</template>
<script >
import {
  bannerStatusMapping,
  bannerStatusBtnMapping,
  bannerBtnList,
  tabNameTextMapping,
  hoverImg,
} from './config/mapping'
import { pageSizeOpts } from '../../utils/util'
import Modal from './profile/modal'
export default {
  name: 'banner',
  components: {
    'z-modal': Modal
  },
  props: ['bannerType'],
  data () {
    return {
      id: '', // 当前编辑或者查看的row的id
      modalTitle: '', //编辑banner
      modalDelete: false,
      modalRemove: false,
      modalStatus: {
        status: '',
        btn: ''
      },
      page: { no: 1, size: 10, total: 0 },
      pageSizeOpts: pageSizeOpts,
      activitySubTypeModal: '',
      hoverImageVisible: [ false, false, false, false, false ],
      tabs: [], // 当前展示tab[]
      activeTab: '1', // 当前展示tab
      activeTabModal: '', // 传值
      tabNameTextMapping: tabNameTextMapping, // tabmapping
      tabMP: [ // e index name;value name:key
        {
          title: (h) => { return this.creatMouseHover(h, 0, '首页运营位', '1') },
          name: '1',
        },
        {
          title: (h) => { return this.creatMouseHover(h, 1, '兑换好物运营位', '2') },
          name: '2',
        },
        {
          title: (h) => { return this.creatMouseHover(h, 2, '游客模式头图', '4') },
          name: '4',
        },
        {
          title: (h) => { return this.creatMouseHover(h, 3, '非游客模式头图', '3') },
          name: '3',
        },
      ],
      tabMPM: [
        {
          title: (h) => { return this.creatMouseHover(h, 5, '会员页运营位', '6') }, // name不按顺序，index按顺序
          name: '6',
        },
        {
          title: (h) => { return this.creatMouseHover(h, 6, '游客模式头图', '9') },
          name: '9',
        },
        {
          title: (h) => { return this.creatMouseHover(h, 7, '非游客模式头图', '8') },
          name: '8',
        },
      ],
      hoverImg: hoverImg,
      bannerList: [],
      bannerListEditNum: 10,
      bannerColumns: [
        {
          title: '序号',
          key: 'num',
          className: 'th-class th-class-banner text',
          width: 60,
          render: (h, params) => { return this.creatIndexNum(h, params, this.bannerList) }
        },
        {
          title: '排序',
          key: 'sort',
          className: 'th-class th-class-banner',
          width: 140,
          render: (h, params) => { return this.creatMoveBtn(h, params)}
        },
        {
          title: '名称',
          key: 'name',
          className: 'th-class th-class-banner text',
          width: 100,
          render: (h, params) => { return this.creatName(h, params, this.bannerList )}
        },
        {
          title: 'a',
          key: 'img',
          className: 'th-class th-class-banner',
          width: 160,
          render: (h, params) => { return this.creatImg(h, params, this.bannerList )}
        },
        {
          title: '开始时间',
          key: 'startTime',
          className: 'th-class th-class-banner text',
        },
        {
          title: '结束时间',
          key: 'endTime',
          className: 'th-class th-class-banner text',
        },
        {
          title: '生效状态',
          key: 'status',
          className: 'th-class th-class-banner',
          width: 160,
          render: (h, params) => { return this.creatStatus(h, params)}
        },
        {
          title: '操作',
          key: 'function',
          className: 'th-class th-class-banner',
          width: 190,
          render: (h, params) => { return this.creatFun(h, params)}
        },
      ],
      bannerBtnList: bannerBtnList,
      modalHoverImg: '',
      modalDisplaySaveBtn: true,
      creatBtnDisplay: true
    }
  },
  mounted() {
  },
  watch: {
    // 监听 $route 对象的变化
    'bannerType': {
      immediate: true,
      handler(newVal, oldVal) {
        this.navChange(newVal, oldVal);
        this.activeTabModal = newVal
        this.tabs = (newVal === 'MPM' ? this.tabMPM : this.tabMP)
        this.activeTab = this.defaultActiveTab(newVal)
        this.bannerColumns[3].title = this.tabNameTextMapping[this.activeTab].name || ''
      },
    },
  },
  methods: {
    defaultActiveTab(newVal) {
      return newVal !== 'MPM' ? '1' : '6'
    },
    // nav fun
    navChange(newVal) {
      this.bannerType = newVal;
      this.activeTab = this.defaultActiveTab(newVal) //(newVal !== 'MPM' ? '1' : '6') // 1是mp首个  7是mpm首个，后端定义的string非num
      this.modalHoverImg = this.hoverImg[this.activeTab] // hoverimg中  后端定义的string所对应obj
      this.page.no = 1
      this.queryTableData(newVal, this.activeTab, this.page.no)
    },
    tabClick(name) {
      this.activeTab = name
      this.creatBtnDisplay = !(['3', '8'].includes(this.activeTab));
      this.bannerColumns[3].title = this.tabNameTextMapping[name].name || ''
      this.modalHoverImg = this.hoverImg[name]
      this.page.no = 1
      this.queryTableData(this.bannerType, name, this.page.no)
    },
    // table fun
    creatMouseHover(h, index, name, nameKey) {
      const activeIcon = 'https://ccbdatprdcne2.blob.core.chinacloudapi.cn/dktcnecmp-static/mp-bo/Info-circle-active.svg'
      const noramIcon = 'https://ccbdatprdcne2.blob.core.chinacloudapi.cn/dktcnecmp-static/mp-bo/Info-circle.svg'
      return h('div', { class: 'tabs-style' }, [
        h('div',  name),
        h('div', {
          attrs: {
            class: 'tabs-display-img',
          },
          on: {
            mouseover: () => {
              this.$set(this.hoverImageVisible, index, true);
            },
            mouseleave: () => {
              this.$set(this.hoverImageVisible, index, false);
            }
          }
        }, [
          h('img', {
            attrs: {
              src: nameKey === this.activeTab ? activeIcon : noramIcon,
              alt: 'banner',
              class: 'tabs-icon-img',
            }
          }),
          h('img', {
            attrs: {
              src: this.hoverImg[nameKey],
              alt: 'hover banner',
              class: 'tabs-tip-img',
              style: ` display: ${this.hoverImageVisible[index] ? 'block' : 'none'}; `
            }
          })
        ])
      ])
    },
    creatMoveBtn(h, params) {
      if (['3', '8'].includes(this.activeTab) || this.bannerList[1].status === 'ENDED') {
        return h('span', '');
      }
      let operations = []
      const move = (operations, moveDirection, disable = true) => {
        const attr =  {
          class: 'banner-operation-btn ml5 mr5 mt5 mb5 disabled-gray',
          props: { type: 'primary' },
        }
        if (disable) {
          attr.class = 'banner-operation-btn ml5 mr5 mt5 mb5'
          attr.on = { click: () => {
            this.moveList(params, moveDirection)
          }}
        }
        operations.push(h('i-button',
          attr
         , moveDirection === 'up' ? '上移' : '下移'))
      }
      if (params.row._index > 0 && params.row._index < this.bannerListEditNum) {
        move(operations, 'up');
        move(operations, 'down')
      } else if (params.row._index === 0) {
        move(operations, 'up', false);
        move(operations, 'down')
      } else if (params.row._index === this.bannerListEditNum) {
        move(operations, 'up');
        move(operations, 'down', false)
      }
      return h('div', operations);
    },
    creatIndexNum(h, params, bannerList) {
      return h('span', {
        domProps: {
          innerHTML: bannerList[params.row._index].activityDetail.configDto.priority
        },
      })
    },
    creatName(h, params, bannerList) {
      return h('span', {
        domProps: {
            innerHTML: bannerList[params.row._index].activityDetail.configDto.activityName
        },
      })
    },
    creatImg(h, params, bannerList) {
      return h('img', {
        attrs: {
          src: bannerList[params.row._index].activityDetail.configDto.imageUrl,
          alt: 'banner',
          style: 'width: 100px;height: 40px',
        }
      })
    },
    creatStatus(h, params) {
      const text = bannerStatusMapping[params.row.status]
      return h('div', {
        domProps: {
          innerHTML: text
        },
        class: `status-${params.row.status}`,
        attrs: {
          style: 'font-size: 11px',
        }
      })
    },
    creatFun(h, params) {
      let operations = []
      const text = bannerStatusMapping[params.row.status]
      const bannerBtnList = this.bannerBtnList
      let array = bannerStatusBtnMapping[text]
      if (bannerBtnList.length === 0) return
      if (['3', '8'].includes(this.activeTab)) { array = [ 'edit' ] } // 3,8 不显示删除按钮
      array.forEach((value) => {
        operations.push(h('i-button',
          {
            class: `group-operation-btn ml5 mr5 mt5 mb5 ${value === 'query' ? 'query' : ''}`,
            props: { type: this.bannerBtnList[value].type, ghost: this.bannerBtnList[value].type === 'error' },
            directives: [
              {
                name: 'has',
                value: this.bannerBtnList[value].permissions
              }
            ],
            on: { click: () => { this.btnOperation(bannerBtnList[value].code, params) }}
          }, bannerBtnList[value].text))
      })
      return h('div', operations);
    },
    btnOperation(code, params) {
      this[code](params)
    },
    async moveList(params, moveDirection) {
      const id = params.row.id
      if (moveDirection === 'up') {
        await this.$api.moveUpRow(id)
      } else {
        await this.$api.moveDownRow(id)
      }
      this.queryTableData(this.bannerType, this.activeTab)
    },
    edit(params) {
      this.activitySubTypeModal = this.activeTab.toString()
      this.useRowData = params
      this.id = params.row.id
      const status = bannerStatusMapping[params.row.status]
      this.modalStatus = {
        status: status,
        btn: 'edit'
      }
      this.modalTitle = '编辑banner'
      this.queryTableData(this.bannerType, this.activeTab, 1, 10)
    },
    query(params) {
      this.modalDisplaySaveBtn = false
      this.useRowData = params
      this.id = params.row.id
      const status = bannerStatusMapping[params.row.status]
      this.modalStatus = {
        status: status,
        btn: 'query'
      }
      this.modalTitle = '查看banner'
    },
    remove(params) {
      this.modalRemove = true
      this.useRowData = params
    },
    delete(params) {
      this.modalDelete = true
      this.useRowData = params
    },
    changePageNo(no) {
      this.page.no = no
      this.queryTableData(this.bannerType, this.activeTab, no, this.page.size)
    },
    changePageSize(size) {
      this.page.size = size
      this.queryTableData(this.bannerType, this.activeTab, this.page.no, size)
    },
    async queryTableData(bannerType, activeTab, pageNo = 1, pageSize = 10) {
      const req = {
        activitySubType: activeTab,
        pageSize: pageSize,
        pageNo: pageNo,
      }
      const resq = await this.$api.getTableData(req)
      this.bannerList = resq.data.items
      this.page.total = resq.data.total
      this.page.no = resq.data.pages
      resq.data.items.forEach((item, index) => {
        if(item.activityDetail.configDto.priority) {
          this.bannerListEditNum = index
        }
      })
    },
    // 模态框
    sureDeleteModel() {
      this.callDelete()
    },
    async callDelete() {
      const id = this.useRowData.row.id
      const resq = await this.$api.deleteRow(id)
      if (resq.status === 'success') {
        this.$message.success('删除成功')
      }
      this.queryTableData(this.bannerType, this.activeTab)
    },
    sureRemoveModel() {
      this.callRemove()
    },
    async callRemove() {
      const id = this.useRowData.row.id
      const resq = await this.$api.removeRow(id)
      if (resq.status === 'success') {
        this.$message.success('下架成功')
      }
      this.queryTableData(this.bannerType, this.activeTab)
    },
    modalCreate() {
      this.activitySubTypeModal = this.activeTab.toString()
      this.modalTitle = '新建banner'
      this.id = ''
    },
    modalCertain() {
      this.modalTitle = ''
      this.id = ''
      this.queryTableData(this.bannerType, this.activeTab, 1, 10)
    },
    modalCancel() {
      this.modalTitle = ''
      this.modalDisplaySaveBtn = true
      this.id = ''
    },
  }
}
</script>
<style lang="less">
.status-IN_EFFECT {
  color: #616161;
}
.status-TO_BE_EFFECT {
  color: #DB6900;
}
.status-ENDED {
  color: #949494;
}
.th-class-banner.text {
  font-size: 14px !important;
}
.banner-operation-btn {
  color: rgba(54, 67, 186, 1);
  background-color: rgba(0,0,0,0);
  border: none;
  font-size: 14px !important;
  width: 40px;
}
.banner-operation-btn:active, .banner-operation-btn:focus, .banner-operation-btn:hover  {
  background-color: rgba(0,0,0,0);
  border: none;
  box-shadow: none;
}
.disabled-gray {
  color: rgba(54, 67, 186, 0.38);
}
</style>
<style lang="less" scoped>
.model-confirm-title {
  font-size: 16px;
  padding: 20px 10px;
  color: rgba(16, 16, 16, 1)
}
/deep/ .ivu-tabs-nav-scroll {
  overflow: visible ;
}
/deep/ .ivu-tabs-nav-wrap {
  overflow: visible ;
}
/deep/ .ivu-tabs-nav-container {
  overflow: visible ;
}
/deep/ .tabs-style {
  display: flex ;
  flex-direction: row;
}
/deep/ .tabs-display-img{
  position: relative;
  display: flex ;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
/deep/ .tabs-icon-img {
  width: 14px; height: 14px;
  margin: 0 0 0 4px;
  overflow: visible !important
}
/deep/ .product-order-table-box {
  min-height: 500px;
  background: #fff;
}
/deep/ .tabs-tip-img {
  position: absolute;
  top: 20px;
  left: -20px;
  width: 240px;
  height: 463px;
  opacity: 0.8;
  z-index: 19999999;
}
/deep/ .query {
  margin-right: 68px;
}
</style>
