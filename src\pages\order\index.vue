<template>
	<div class="customer">
		<i-row class="mt20 mb30">
			<i-col span="8">
				<i-col span="14">
					<i-input class="i-search" clearable placeholder="精确查询(核销码)" v-model="searchForm.redeemCode" />
				</i-col>
				<i-col class="ml15 mr15" span="6">
					<i-select placeholder="订单状态" style="width:100px" v-model="searchForm.status">
						<i-option :key="s.value" :value="s.value" v-for="s in statusList">{{s.label}}</i-option>
					</i-select>
				</i-col>
				<i-col span="1">
					<i-button @click="searchClick" class>查询</i-button>
				</i-col>
			</i-col>
			<i-col class="text-right" span="16">
				<template v-if="searchForm.status == 'paid' && currentStore.dktStoreNumber">
					还剩
					<span class="confirm-msg">{{totalCount}}</span>单待核销
				</template>
				<template v-if="showConfirmAndExport">
					<!-- <i-button @click="exportCSV" class="mr15" icon="md-download">导出</i-button> -->
					<i-button @click="confirmAll" class type="warning">一键确认</i-button>
				</template>
			</i-col>
		</i-row>
		<i-row>
			<i-table
				:columns="orderColumns"
				:data="orderList"
				@on-selection-change="handleSelectionChange"
				border
				class="product-order-table-box"
				ref="table"
			/>
		</i-row>
		<i-row class="table-footer mt15 text-right">
			<i-page
				:page-size="page.size"
				:page-size-opts="pageSizeOpts"
        :current="page.no"
				:total="page.total"
				@on-change="changePageNo"
				@on-page-size-change="changePageSize"
				show-sizer
				show-total
			/>
		</i-row>
	</div>
</template>

<script>
import cfg from './config'
import { mapGetters, mapActions } from 'vuex'
import { deepClone, pageSizeOpts } from '@/utils/util'

export default {
  name: 'order_redeem',
  data() {
    return {
      searchForm: {
        redeemCode: '', // 核销码
        status: '' // 状态
      },
      statusList: cfg.statusList, // 状态列表
      showConfirmAndExport: false, // 是否显示一键确认及导出按钮

      page: {
        no: 1,
        size: 10,
        total: 0
      },
      pageSizeOpts: pageSizeOpts,

      // 表格操作按钮
      opBtnList: [
        { code: 'redeem', text: '核销', type: 'info' },
        { code: 'confirm', text: '确认', type: 'success' }
      ],
      // 订单列表
      orderList: [],
      orderColumns: [
        { type: 'selection', align: 'center', fixed: 'left', width: 70 },
        {
          title: '兑换单编号',
          key: 'dktBenefitTransId',
          className: 'th-class'
        },
        { title: '客户姓名', key: 'customerName', className: 'th-class' },
        {
          title: '商品名称',
          key: 'productName',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.pointOrderItems[0].productInfo.title)
          }
        },
        {
          title: 'ItemCode',
          key: 'itemCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.pointOrderItems[0].productInfo.itemCode)
          }
        },
        { title: '燃值', key: 'usedPoints', className: 'th-class' },
        {
          title: '过期日期',
          key: 'dktBenefitExpireTime',
          className: 'th-class',
          render: (h, params) => {
            return h(
              'span',
              new Date(params.row.dktBenefitExpireTime).format(
                'yyyy-MM-dd hh:mm:ss'
              )
            )
          }
        },
        {
          title: '状态',
          key: 'status',
          className: 'th-class',
          render: (h, params) => {
            return h('span', cfg.convert(params.row.status, cfg.statusList))
          }
        },
        {
          title: '操作',
          key: 'action',
          className: 'th-class',
          fixed: 'right',
          width: 140,
          render: (h, params) => {
            return this.createOpBtn(h, params.row)
          }
        }
      ],
      totalCount: '',
      orderIds: ''
    }
  },
  mounted() {
    this.loadData()
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  watch: {
    // 监控门店切换, 刷新数据
    currentStore(newVal, oldVal) {
      if (this.searchForm.status == 'paid') {
        this.currentStore.dktStoreNumber &&
          this.countPaidOrders(this.currentStore.dktStoreNumber)
      } else {
        this.searchOrder()
      }
    }
  },
  methods: {
    // 初始数据
    loadData() {
      this.searchForm.status = 'paid'
      this.currentStore.dktStoreNumber &&
        this.countPaidOrders(this.currentStore.dktStoreNumber)
    },

    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      for (let i = 0; i < this.opBtnList.length; i++) {
        let value = this.opBtnList[i]
        if (
          (row.status == 'paid' && value.code == 'confirm') ||
          (row.status == 'redeemed' && value.code == 'redeem') ||
          (row.status == 'confirmed' && value.code != 'view')
        ) {
          continue
        }
        operations.push(
          h(
            'i-button',
            {
              class: 'ml15',
              props: { type: value.type, disabled: row.status == 'expired' },
              on: {
                click: () => {
                  this.rowOperation(value.code, row)
                }
              }
            },
            value.text
          )
        )
      }
      return h('div', operations)
    },

    // 查询
    searchClick() {
      this.page.no = 1;
      this.searchOrder()
    },

    // 搜索订单
    async searchOrder() {
      if (
        this.searchForm.status == 'paid' &&
        this.searchForm.redeemCode == ''
      ) {
        this.$message.warning({
          content: '请填写核销码',
          closable: true,
          duration: 3
        })
        return
      }
      if (this.searchForm.status == 'redeemed') {
        this.showConfirmAndExport = true
      } else {
        this.showConfirmAndExport = false
      }
      this.searchForm['pageNo'] = this.page.no
      this.searchForm['dktStoreNumber'] = this.currentStore.dktStoreNumber
      this.searchForm['pageSize'] = this.page.size
      let resp = await this.$api.searchOrder(this.searchForm)
      this.orderList = resp.data.items
      this.page.total = resp.data.total
      this.totalCount = resp.data.totalCount
    },

    async countPaidOrders() {
      let resp = await this.$api.countPaidOrders({
        dktStoreNumber: this.currentStore.dktStoreNumber
      })
      if(resp && resp.data) {
        this.totalCount = resp.data
      }
    },

    // 分页操作
    changePageNo(no) {
      this.page.no = no
      this.searchOrder()
    },
    changePageSize(size) {
      this.page.size = size
      this.searchOrder()
    },

    // 表格项操作
    rowOperation(code, row) {
      switch (code) {
        case 'redeem':
          this.redeemOrder(row)
          break
        case 'confirm':
          this.confirm([row.id])
          break
        case 'view':
          this.viewOrder(row)
          break
      }
    },

    handleSelectionChange(selection) {
      this.orderIds = []
      selection.forEach(value => {
        this.orderIds.push(value.id)
      })
    },

    // 导出CSV表格
    exportCSV() {
      let orders = []
      this.orderList.forEach(value => {
        orders.push({
          dktBenefitTransId: value.dktBenefitTransId,
          customerName: value.customerName,
          productName: value.pointOrderItems[0].productInfo.title,
          itemCode: value.pointOrderItems[0].productInfo.itemCode,
          usedPoints: value.usedPoints,
          dktBenefitExpireTime: new Date(value.dktBenefitExpireTime).format(
            'yyyy-MM-dd hh:mm:ss'
          ),
          status: cfg.convert(value.status, cfg.statusList)
        })
      })
      this.$refs.table.exportCsv({
        quoted: true,
        filename: new Date().format('yyyyMMddhhmmss') + '_已领取_订单报表',
        columns: this.orderColumns.filter(
          (col, index) => index > 0 && index < 8
        ),
        data: orders
      })
    },
    // 一键确认
    confirmAll() {
      if (this.orderIds.length > 0) {
        this.confirm(this.orderIds)
      } else {
        this.$modal.warning({
          title: '提示',
          content: '请选择要一键确认的订单'
        })
      }
    },
    // 确认订单
    async confirm(ids) {
      let data = { orderIds: ids }
      let resp = await this.$api.confirmOrder(JSON.stringify(data))
      if (resp.status == 'success') {
        this.searchOrder()
      }
    },
    // 核销订单
    redeemOrder(row) {
      this.$modal.confirm({
        title: '订单核销确定',
        width: '450px',
        render: h => {
          return h('div', { class: 'order-redeem' }, [
            h('div', { class: 'redeem-warning' }, [
              h(
                'div',
                { class: 'redeem-text' },
                '2020-1-14起门店正常销售的兑换产品请使用webpos扫码核销！核销时请输入会员卡号！'
              ),
              h(
                'div',
                { class: 'redeem-text' },
                '非实物类（如场馆券）请仍然使用会员后台核销！'
              )
            ]),
            h('div', { class: 'redeem-text-margin redeem-label' }, [
              h('div', { class: 'redeem-label-title' }, '客户名称:'),
              h('div', { class: 'redeem-label-desc' }, row.customerName)
            ]),
            h('div', { class: 'redeem-text-margin redeem-label' }, [
              h('div', { class: 'redeem-label-title' }, '商品名称:'),
              h(
                'div',
                { class: 'redeem-label-desc' },
                row.pointOrderItems[0].productInfo.title
              )
            ]),
            h('div', { class: 'redeem-label' }, [
              h('div', { class: 'redeem-label-title' }, '核销门店:'),
              h('div', { class: 'redeem-label-desc' }, this.currentStore.name)
            ])
          ])
        },
        okText: '核销',
        onOk: () => {
          this.handleOrderRedeem(row)
        }
      })
    },
    // 查看订单详情
    viewOrder(row) {},
    // 订单核销
    async handleOrderRedeem(row) {
      let resp = await this.$api.redeemOrder(row.id)
      if (resp['status'] == 'success') {
        this.$message.success({
          content: `兑换单[${row.dktBenefitTransId}]核销成功`,
          closable: true,
          duration: 3
        })
        this.searchOrder()
      }
    }
  }
}
</script>

<style lang="less">
.order-redeem {
  .redeem-warning {
    margin-bottom: 15px;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.6;
    border-radius: 4px;
    border: 1px solid #ffd77a;
    background-color: #fff9e6;
  }
  .redeem-label {
    display: flex;
    align-items: center;
    &-title {
      width: 80px;
      font-weight: bold;
    }
    &-desc {
      padding-top: 1.5px;
    }
  }
  .redeem-text-margin {
    margin-bottom: 5px;
  }
}
</style>

<style lang="less" scoped>
.customer {
  .product-order-table-box {
    width: auto;
  }
  .th-class {
    text-align: center !important;
    vertical-align: middle !important;
    min-width: 120px !important;
  }
  .redeem {
    width: 300px;
    margin-top: 15px;
    &-label {
      width: 100px;
      text-align: right;
    }
    &-text {
      width: 200px;
      text-align: left;
      margin-left: 30px;
    }
  }
}
</style>
