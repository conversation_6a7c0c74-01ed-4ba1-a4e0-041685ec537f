<template>
	<div class="repo product-template-box">
		<i-modal :mask-closable="false" @on-cancel="cancelClick" footer-hide title="模板库搜索" v-model="show" width="1000">
			<div class="header mt20 mb30 flex row item-center justify-between">
				<div class="template-search-content flex row item-center justify-between">
					<i-input :autofocus="true" class="search mr15" clearable placeholder="模糊搜索（支持Item Code, Dsm Code,商品名称）批量搜索（支持Item Code并用英文逗号隔开）" v-model="word" />
				</div>
				<div>
					<i-button @click="resetClick" class="mr15">重置</i-button>
					<i-button @click="handleSearch" class="mr15">搜索</i-button>
				</div>
			</div>

			<i-table :columns="productColumns" :data="productList" class="product-template-table-box product-table-wrap" @on-select="tableSelectRow" @on-select-cancel="tableSelectCancelRow" />
			<div style="margin-top: 10px; overflow: hidden">
				<div style="float: right;">
					<i-page
						:page-size="page.size"
						:page-size-opts="pageSizeOpts"
						:total="page.total"
						@on-change="changePageNo"
						@on-page-size-change="changePageSize"
						show-sizer
						show-total
					/>
				</div>
			</div>
      <div class="bottom-btns">
        <i-button class="mr15" @click="cancelClick">取消</i-button>
        <i-button type="primary" @click="checkProductsValid">启用</i-button>
      </div>
		</i-modal>
    <!-- <i-modal :mask-closable="false" @on-ok="goPublish" title="商品发布" v-model="publishShow" width="500">
      <div class="word-wrap" v-if="currentRow">您即将发布{{this.currentStore.name}}门店{{currentRow.attributes.title}}商品（item code：{{currentRow.attributes.item_code}}，库存：{{this.stock}}），请确认？</div>
    </i-modal> -->
    <!-- 填写库存并确认发布 弹窗 -->
    <i-modal :mask-closable="false" footer-hide title="请填写库存并确认发布以下商品" :closable="false" v-model="publishModalShow" width="716">
      <div class="word-wrap">
			  <i-table :columns="selectProductColumns" border :key="Math.random()" :data="selectProducts" class="product-template-table-box" >
          <!-- 库存 -->
          <template slot-scope="{ index }" slot="stock_value">
            <i-input placeholder="" class="stock-input" v-model="selectProducts[index].stock" />
          </template>
        </i-table>
        <div class="bottom-btns multi-publish-btns">
          <i-button class="mr15" @click="hideMultiPublish">取消</i-button>
          <i-button type="primary" @click="goMultiPublish">发布</i-button>
        </div>
      </div>
    </i-modal>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { pageSizeOpts, isPositive } from '@/utils/util'
import paidMemberApi from '@/api/paidMember/index'

export default {
  name: 'template_search_modals',
  props: {
    opFrom: {
      onSale: false,
      type: '',
      batchNumber: '',
      currentSize: ''
    },
    certain: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    },
    orderNum: [Number, String]
  },
  data() {
    return {
      show: true,
      // currentRow: null,
      // publishShow: false,
      publishModalShow: false,
      dateType: 'month',
      format: 'yyyy-MM',
      open: false,
      displayDate: '',

      word: '', // 模糊搜索字段
      searchForm: {
        is_delete: false,
        item_code: '',
        type_id: 8,
        page_num: 1,
        page_size: 10,
        title: '',
      },

      pageSizeOpts: pageSizeOpts,
      page: {
        no: 1,
        size: 10,
        total: 0
      },

      stock: '', // 商品库存

      productList: [], // 模板库产品列表
      opBtnList: [
        // 表格操作按钮
        { code: 'enable', text: '启用', type: 'primary' }
      ],
      productColumns: [
        {
          type: 'selection',
          width: 50,
          align: 'center',
          className: 'select-checkbox',
        },
        {
          title: '商品图片',
          key: 'imageUrls',
          className: 'th-class th-class-img',
          render: (h, params) => {
            return this.showRowImage(h, params.row.attributes.image_urls)
          }
        },
        {
          title: 'Dsm Code',
          key: 'dsmCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.dsm_code)
          }
        },
        {
          title: 'Item Code',
          key: 'itemCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.item_code)
          }
        },
        {
          title: '商品名称',
          key: 'title',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.title)
          }
        },
        {
          title: '颜色',
          key: 'color',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.color)
          }
        },
        {
          title: '尺码',
          key: 'size',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.size)
          }
        },
        {
          title: '兑换燃值',
          key: 'point_value',
          className: 'th-class',
        },
        {
          title: '发布日期',
          key: 'publishTime',
          className: 'th-class',
          render: (h, params) => {
            return h(
              'span',
              new Date(params.row.create_time).format('yyyy-MM-dd hh:mm:ss')
            )
          }
        },
       
      ],
      selectProductColumns: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          className: 'th-class th-class-img',
        },
        {
          title: '图片',
          key: 'imageUrls',
          width: 160,
          className: 'th-class th-class-img',
          render: (h, params) => {
            return this.showRowImage(h, params.row.attributes.image_urls)
          }
        },
        {
          title: 'Item Code',
          key: 'itemCode',
          width: 120,
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.item_code)
          }
        },
        {
          title: '商品名称',
          key: 'itemCode',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.title)
          }
        },
        {
          title: '颜色',
          key: 'color',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.color)
          }
        },
        {
          title: '尺码',
          key: 'size',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.attributes.size)
          }
        },
        {
          title: '库存',
          slot: 'stock_value',
          width: 90,
          className: 'th-class',
        },
      ],
      selectProducts: [], // 选中的商品
    }
  },
  mounted() {
    this.searchNationalRepo()
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  methods: {
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'tables-pic-box' }, [
        h('img', {
          attrs: {
            src: imageUrls[0],
            class: 'table-img mt5',
            width: '45px',
            height: '45px'
          }
        })
      ])
    },
    // async goPublish() {
    //   let params = {
    //     catalogue_template_id: this.currentRow.template_id,
    //     order_number: this.orderNum,
    //     stock: this.stock,
    //     store_number: this.currentStore.dktStoreNumber,
    //   }
    //   let resp = await paidMemberApi.createProductFromTemplate(JSON.stringify(params));
    //   if (resp.status === 'success') {
    //     this.$message.success({ content: '成功发布！' , closable: true, duration: 3 })
    //     this.show = false;
    //     this.$emit('cancel');
    //     this.$emit('getOpenCardList');
    //   }
    // },
    checkStockValid() {
      // 库存不能为空
      let stockNotFill = this.selectProducts.some(item => {
        return item.stock === '' || item.stock === undefined
      })
      if(stockNotFill) {
        this.$message.warning({
          content: '请填写库存',
          closable: true,
          duration: 3
        })
        return false;
      }
      // 库存数量不得小于0
      let stockLessThanZero = this.selectProducts.some(item => {
        return item.stock < 0
      })
      if(stockLessThanZero) {
        this.$message.warning({
          content: '库存数量不得小于0',
          closable: true,
          duration: 3
        })
        return false;
      }
      // 库存必须是正数
      let stockNotPositive = this.selectProducts.some(item => {
        return !isPositive(item.stock) && item.stock !== '0'
      })
      if(stockNotPositive) {
        this.$message.warning({
          content: '库存数量必须是正数',
          closable: true,
          duration: 3
        })
        return false;
      }
      // 库存不可超过50
      let stockOverLimit = this.selectProducts.some(item => {
        return item.stock > 50
      })
      if(stockOverLimit) {
        this.$message.warning({
          content: '库存数量不得大于50',
          closable: true,
          duration: 3
        })
        return false;
      }
      return true;
    },
    hideMultiPublish() {
      this.selectProducts.forEach(item => {
        item.stock = ''
      })
      this.publishModalShow = false;
      // this.show = false;
      
    },
    async goMultiPublish() {
      // 发布前校验
      let stockValid = this.checkStockValid();
      if(!stockValid) { return }
      
      let dtoArr = [];
      this.selectProducts.forEach(item => {
        dtoArr.push({
          catalogue_template_id: item.template_id,
          stock: item.stock,
        })
      })
      let params = {
        order_number: this.orderNum,
        store_number: this.currentStore.dktStoreNumber,
        template_dtos: dtoArr
      }

      // 发布Api
      let resp = await paidMemberApi.createProductFromTemplate(JSON.stringify(params));
      if (resp.status === 'success') {
        this.$message.success({ content: '成功发布！' , closable: true, duration: 3 })
        this.publishModalShow = false;
        this.show = false;
        this.$emit('cancel');
        this.$emit('getOpenCardList');
      }
    },
    openPicker() {
      this.open = true
    },
    changeType(type) {
      switch (this.dateType) {
        case 'month':
          this.format = 'yyyy-MM'
          break
        case 'date':
          this.format = 'yyyy-MM-dd'
          break
      }
      this.displayDate = ''
    },
    // 重置搜索条件
    resetClick() {
      this.searchForm = {
        item_code: '',
        type_id: 8,
        page_num: 1,
        page_size: 10,
        title: '',
      }
      this.word = ''
      this.displayDate = ''
      this.selectProducts = []
      this.searchNationalRepo()
    },
    handleSearch() {
      this.searchForm['item_code'] = this.word
      this.searchForm['title'] = this.word
      this.searchForm['dsm_code'] = this.word
      this.searchNationalRepo()
    },
    checkHasItem(arr, curItem) {
      let hasCurItem = arr.some(item => item.template_id === curItem.template_id);
      return hasCurItem;
    },
    // 单选
    tableSelectRow(selection, curItem) {
      let hasCurItem = this.checkHasItem(this.selectProducts, curItem);
      if( !hasCurItem ) {
        this.selectProducts.push(curItem)
      }
    },
   
    // 取消单选
    tableSelectCancelRow(selection, curItem) {
      let hasCurItem = this.checkHasItem(this.selectProducts, curItem);
      if( hasCurItem ) {
        this.selectProducts = this.selectProducts.filter(item => item.template_id !== curItem.template_id);
      }
    },
    // 分页操作
    changePageNo(no) {
      this.page.no = no
      this.searchNationalRepo()
    },
    changePageSize(size) {
      this.page.size = size
      this.searchNationalRepo()
    },

    // 搜索模板产品库
    async searchNationalRepo() {
      this.searchForm['page_num'] = this.page.no
      this.searchForm['page_size'] = this.page.size
      let resp = await paidMemberApi.getTplManageList(this.searchForm);
      if (resp.status === 'success') {
        if(resp.data) {
          this.productList = resp.data.items || [];
          this.productList.forEach(item => {
            const isSelected = this.selectProducts.some(selectedItem => selectedItem.template_id === item.template_id);
            item._checked = isSelected; // 如果存在，则将 _checked 设置为 true
          })
          this.page.total = resp.data.total || 0;
        }
      }
      
    },
    checkProductsValid() {
      if(!this.selectProducts || this.selectProducts.length < 1){
        this.$message.warning({
          content: '请选择商品',
          closable: true,
          duration: 3
        })
        return
      } 
      this.checkDsmCode();
    },
    async checkDsmCode() {
      let ids = [];
      this.selectProducts.forEach(item => {
        ids.push(item.template_id);
      })
      const template_ids = ids.join(',');
      let params = {
        template_ids,
      }
      let resp = await paidMemberApi.checkSameDsmCode(params);
      if (resp.status === 'success') {
        if(!resp.data) {
          this.$message.warning({
            content: '所选产品非同一Dsm Code，请重新勾选',
            closable: true,
            duration: 3
          })
          return
        } else {
          // 生成库存字段
          this.selectProducts.forEach(item => {
            item.stock = ''
          })
          this.publishModalShow = true;
        }
      }
    },
    verifyStock(row) {
      if(this.stock < 0){
        this.$message.warning({
          content: '库存数量不得小于0',
          closable: true,
          duration: 3
        })
        this.stock = '';
        return
      } else if (!isPositive(this.stock) && this.stock !== '0') {
        this.$message.warning({
          content: '库存必须是正数',
          closable: true,
          duration: 3
        })
        this.stock = '';
        return
      } else if (this.stock > 3000) {
        this.$message.warning({
          content: '库存不可超过3000',
          closable: true,
          duration: 3
        })
        this.stock = '';
        return
      }
      // 显示商品发布弹窗
      this.publishShow = true;
    },
    cancelClick() {
      this.show = false
      this.$emit('cancel');
    }
  }
}
</script>

<style lang="less" scoped>
.template-search-content {
  width: 720px;
  .search{
    width: 580px;
  }
}
.product-template-table-box {
  .tables-pic-box {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    line-height: 60px;
    margin: 0 auto;
    .table-img {
      margin-top: 10px !important;
    }
  }

  .th-class {
    text-align: center !important;
    vertical-align: middle !important;
    min-width: 120px !important;
  }
}
.product-table-wrap{
  /deep/.ivu-table-header{
    .select-checkbox div{
      visibility: hidden;
    }
  }
}
.mt5 {
  margin-top: 5px;
}
.stock-input{
  /deep/.ivu-input{
    text-align: center;
  }
}
.bottom-btns{
  display: flex; align-items: center; justify-content: center;
  &.multi-publish-btns{
    margin-top: 15px;
  }
}
.word-wrap{ word-wrap: break-word; }
</style>
