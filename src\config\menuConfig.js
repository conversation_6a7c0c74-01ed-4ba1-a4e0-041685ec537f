export const defaultMenuList = [
  {
    path: '/',
    title: '付费会员管理',
    name: '付费会员管理',
    icon: 'md-cart',
    permissions: ['PAID_MEMBER_PRODUCT_TEMPLATE', 'PAID_MEMBER_PRODUCT_TEMPLATE_VIEW','PAID_MEMBER_PRODUCT_MANAGE', 'PAID_MEMBER_PRODUCT_BATCH_MANAGE', 'PAID_MEMBER_ORDER_VIEW'],
    children: [
      {
        path: '/paidMemberTemplate',
        title: '模板库管理',
        name: '模板库管理',
        permissions: ['PAID_MEMBER_PRODUCT_TEMPLATE', 'PAID_MEMBER_PRODUCT_TEMPLATE_VIEW']
      },
      {
        path: '/storeOpenCard',
        title: '门店开卡礼管理',
        name: '门店开卡礼管理',
        permissions: ['PAID_MEMBER_PRODUCT_MANAGE', 'PAID_MEMBER_PRODUCT_BATCH_MANAGE']
      },
      {
        path: '/orderManagement',
        title: '订单管理',
        name: '订单管理',
        permissions: ['PAID_MEMBER_ORDER_VIEW']
      }
    ]
  },
  {
    path: '/',
    title: '积分产品管理',
    name: '积分产品管理',
    icon: 'md-cart',
    permissions: ['PRODUCT_STORE_LIST_VIEW', 'PRODUCT_TEMPLATE_LIST_VIEW'],
    children: [
      {
        path: '/templateRepo',
        title: '模板库管理',
        name: '模板库管理',
        permissions: ['PRODUCT_TEMPLATE_LIST_VIEW']
      },
      {
        path: '/storeProduct',
        title: '门店产品管理',
        name: '门店产品管理',
        permissions: ['PRODUCT_STORE_LIST_VIEW']
      }
    ]
  },
  {
    path: '/',
    title: '积分兑换管理',
    name: '积分兑换管理',
    icon: 'md-card',
    permissions: ['PRODUCT_REDEMPTION'],
    children: [
      {
        path: '/productOrder',
        title: '兑换单管理',
        name: '兑换单管理',
        permissions: ['PRODUCT_REDEMPTION']
      }
    ]
  },
  {
    path: '/',
    title: '积分规则管理',
    name: '积分规则管理',
    icon: 'ios-pricetags',
    permissions: ['POINT_RULE_VIEW'],
    children: [
      {
        path: '/integralList',
        title: '规则管理',
        name: '规则管理',
        permissions: ['POINT_RULE_VIEW']
      }
    ]
  },
  {
    path: '/',
    title: '活动管理',
    name: '活动管理',
    icon: 'md-flag',
    permissions: ['STORE_EVENT_UPDATE', 'EVENT_TEMPLATE_VIEW', 'EVENT_IMAGE_POOL_VIEW'],
    children: [
      {
        path: '/galleryActivity',
        title: '集团图库管理',
        name: '集团图库管理',
        permissions: ['EVENT_IMAGE_POOL_VIEW']
      },
      {
        path: '/groupActivity',
        title: '集团活动模版',
        name: '集团活动模版',
        permissions: ['EVENT_TEMPLATE_VIEW']
      },
      {
        path: '/storeActivity',
        title: '门店活动管理',
        name: '门店活动管理',
        permissions: ['STORE_EVENT_UPDATE']
      }
    ]
  },
  {
    path: '/',
    title: '抽奖管理',
    name: '抽奖管理',
    icon: 'logo-bitcoin',
    permissions: ['LUCKY_DRAW_ORDER_VIEW', 'LUCKY_DRAW_ACTIVITY_MANAGEMENT'],
    children: [
      {
        path: '/luckyDraw',
        title: '抽奖订单管理',
        name: '抽奖订单管理',
        permissions: ['LUCKY_DRAW_ORDER_VIEW']
      },
      {
        path: '/luckyDraw/activityManage',
        title: '抽奖活动管理',
        name: '抽奖活动管理',
        permissions: ['LUCKY_DRAW_ACTIVITY_MANAGEMENT']
      }
    ]
  },
  {
    path: '/',
    title: 'Banner管理',
    name: 'Banner管理',
    icon: 'md-document',
    permissions: ['BANNER_MANAGEMENT'],
    children: [
      {
        path: '/bannerMP/MP',
        title: '会员小程序Banner管理',
        name: '会员小程序Banner管理',
        permissions: ['BANNER_MANAGEMENT']
      },
      {
        path: '/bannerMPM/MPM',
        title: '电商小程序Banner管理',
        name: '电商小程序Banner管理',
        permissions: ['BANNER_MANAGEMENT']
      }
    ]
  },
  {
    path: '/',
    title: '风控管理',
    name: '风控管理',
    icon: 'md-document',
    permissions: ['AUD_GLOBAL_LIST_READ', 'AUD_GLOBAL_LIST_WRITE', 'AUD_GLOBAL_LIST_IMPORT'],
    children: [
      {
        path: '/riskNameList',
        title: '风控管理黑白名单',
        name: '风控管理黑白名单',
        permissions: ['AUD_GLOBAL_LIST_READ', 'AUD_GLOBAL_LIST_WRITE', 'AUD_GLOBAL_LIST_IMPORT']
      },
      {
        path: '/riskModel',
        title: '场景配置',
        name: '场景配置',
        permissions: ['BANNER_MANAGEMENT']
      },
      {
        path: '/riskRules',
        title: '规则配置',
        name: '规则配置',
        permissions: ['BANNER_MANAGEMENT']
      },
    ]
  },
  {
    path: '/',
    title: '客户查找',
    name: '客户查找',
    icon: 'md-contacts',
    permissions: ['CUSTOMER_SEARCH'],
    children: [
      {
        path: '/customerSearch',
        title: '客户查找',
        name: '客户查找',
        permissions: ['CUSTOMER_SEARCH']
      }
    ]
  },
  {
    path: '/',
    title: '太阳码管理',
    name: '太阳码管理',
    icon: 'md-cash',
    permissions: ['MP_QR_CODE_MENU_VIEW'],
    children: []
  },
  {
    path: '/',
    title: '系统报告',
    name: '系统报告',
    icon: 'md-map',
    permissions: ['ADVANCED_REPORTING'],
    children: [
      {
        path: '/storeProductReport',
        title: '产品发布',
        name: '产品发布',
        permissions: ['ADVANCED_REPORTING']
      },
      {
        path: '/sessionReport',
        title: '活动报告',
        name: '活动报告',
        permissions: ['ADVANCED_REPORTING']
      },
      {
        path: '/orderReport',
        title: '产品报告',
        name: '产品报告',
        permissions: ['ADVANCED_REPORTING']
      },
      {
        path: '/luckyDrawReport',
        title: '抽奖报告',
        name: '抽奖报告',
        permissions: ['ADVANCED_REPORTING']
      }
    ]
  },
  {
    path: '/wecomDecathlon',
    title: '企微管理后台',
    name: '企微管理后台',
    icon: 'ios-aperture',
    permissions: ['WECOMBO_PAGE_VIEW'],
    children: []
  },
  {
    path: '/myDecathlon',
    title: 'MyDecathlon 后台',
    name: 'MyDecathlon 后台',
    icon: 'ios-desktop',
    permissions: ['MYDECATHLON_PAGE_VIEW'],
    children: []
  },
]
