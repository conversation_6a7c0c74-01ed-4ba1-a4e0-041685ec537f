<template>
	<div class="customer">
		<i-row class="mt20 mb30">
			<i-col span="8">
				<i-col class="mr15" span="12">
					<i-select placeholder="订单状态" style="width:180px" v-model="status">
						<i-option :key="s.label" :value="s.value" v-for="s in statusList">{{s.label}}</i-option>
					</i-select>
				</i-col>
				<i-col offset="2" span="2">
					<i-button @click="searchOrder" class>查询</i-button>
				</i-col>
			</i-col>
		</i-row>
		<i-row>
			<i-table :columns="orderColumns" :data="orderList" border class="product-order-table-box" ref="table" />
		</i-row>
		<i-row class="table-footer mt15 text-right">
			<i-page
				:page-size="page.size"
				:page-size-opts="pageSizeOpts"
        :current="page.no"
				:total="page.total"
				@on-change="changePageNo"
				@on-page-size-change="changePageSize"
				show-sizer
				show-total
			/>
		</i-row>
		<!-- 填写快递信息 -->
		<i-modal @on-ok="ok" class-name="vertical-center-modal" title="物流信息" v-model="statusFlage">
			<i-form>
				<i-row>
					<i-form-item class="luckyDraw-form row" label="订单编号：">
						<span class="luckyDraw-form-input border-0">{{filters.id}}</span>
					</i-form-item>
				</i-row>
				<i-row>
					<i-form-item class="luckyDraw-form row" label="快递单号：">
						<i-input class="luckyDraw-form-input" placeholder="请输入快递单号" v-model="filters.expressNumber" />
					</i-form-item>
				</i-row>
				<i-row>
					<i-form-item class="luckyDraw-form row" label="快递公司：">
						<i-select class="luckyDraw-form-input" placeholder="请输入快递公司" style="width:200px" v-model="filters.expressCompany">
							<i-option :key="item" :value="item" v-for="item in courierList">{{item}}</i-option>
						</i-select>
					</i-form-item>
				</i-row>
			</i-form>
		</i-modal>
	</div>
</template>

<script>
import cfg from './config'
import { deepClone, pageSizeOpts, formatDate } from '@/utils/util'
import { Message, Spin } from 'iview'
export default {
  name: 'order_redeem',
  data() {
    return {
      status: '',
      statusFlage: false,
      statusList: cfg.statusList, // 状态列表
      filters: {},
      courierList: cfg.courierList,
      page: {
        no: 1,
        size: 10,
        total: 0
      },
      pageSizeOpts: pageSizeOpts,
      // 表格操作按钮
      opBtnList: [
        {
          code: 'delivery',
          text: '',
          type: 'primary',
          permissions: ['LUCKY_DRAW_ORDER_UPDATE']
        },
        {
          code: 'views',
          text: '查看',
          type: 'success',
          permissions: ['LUCKY_DRAW_ORDER_VIEW']
        }
      ],
      // 订单列表
      orderList: [],
      orderColumns: [
        {
          title: '订单编号',
          key: 'id',
          className: 'th-class',
          width: 100
        },
        {
          title: '客户姓名',
          key: 'membership',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.membership.dktName)
          }
        },
        {
          title: '会员编号',
          key: 'dktCardNo',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.membership.dktCardNo)
          }
        },
        {
          title: '订单金额',
          key: 'totalAmount',
          className: 'th-class',
          width: 100
        },
        {
          title: '创建时间',
          key: 'createTime',
          className: 'th-class',
          render: (h, params) => {
            const startDate = new Date(params.row.createTime)
            return h('span', startDate.format('yyyy-MM-dd hh:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'status',
          className: 'th-class',
          width: 100,
          render: (h, params) => {
            let status = params.row.status == 'DELIVERED' ? '已发货' : '待发货'
            return h('span', status)
          }
        },
        {
          title: '物流编号',
          key: 'expressNumber',
          className: 'th-class'
        },
        {
          title: '操作',
          key: 'action',
          className: 'th-class',
          fixed: 'right',
          width: 210,
          render: (h, params) => {
            return this.createOpBtn(h, params.row)
          }
        }
      ],
      totalCount: '',
      orderIds: ''
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 初始数据
    loadData() {
      this.getOrderList()
    },
    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      this.opBtnList.forEach(value => {
        let content = {
          class: 'ml5 mr5',
          props: {
            type:
              row.status === 'DELIVERED' && value.code === 'delivery'
                ? 'default'
                : value.type
          },
          directives: [
            {
              name: 'has',
              value: value.permissions
            }
          ],
          on: {
            click: () => {
              this.rowOperation(value.code, row)
            }
          }
        }
        if (value.type == 'primary') {
          operations.push(
            h('i-button', content, row.status == 'DELIVERED' ? '编辑' : '发货')
          )
        } else {
          operations.push(h('i-button', content, value.text))
        }
      })
      return h('div', operations)
    },
    searchOrder() {
      this.page.no = 1;
      this.getOrderList()
    },
    // 分页操作
    changePageNo(no) {
      this.page.no = no
      this.getOrderList()
    },
    changePageSize(size) {
      this.page.size = size
      this.getOrderList()
    },

    // 表格项操作
    rowOperation(code, row) {
      switch (code) {
        case 'delivery':
          this.modalData(row)
          break
        case 'views':
          this.$router.push({
            path: `/luckyDrawDetail/${row.id}`
          })
          break
        default: {
          console.log('no found this type')
        }
      }
    },
    ok() {
      this.updateOrder()
    },
    /**
     * data handel
     */
    modalData(row) {
      const { id, expressNumber, expressCompany } = row
      this.filters = {
        id: id,
        expressNumber: expressNumber || '',
        expressCompany: expressCompany || cfg.courierList[0]
      }
      this.statusFlage = true
    },

    /**
     * network
     */
    async getOrderList() {
      let params = {
        status: this.status,
        ...this.page
      }
      let resp = await this.$api.luckyDrawList(params)
      this.orderList = resp.data.items
      this.page.total = resp.data.total
    },
    async updateOrder() {
      if (
        this.filters.expressNumber == '' ||
        this.filters.expressCompany == ''
      ) {
        Message.warning({
          content: '更新失败，快递单号和快递公司不能为空',
          closable: true,
          duration: 3
        })
        return
      }
      let resp = await this.$api.updateOrder(this.filters)
      if (resp.status == 'success') {
        this.getOrderList()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.customer {
  .product-order-table-box {
    width: auto;
  }
  .th-class {
    text-align: center !important;
    vertical-align: middle !important;
    min-width: 120px !important;
  }
  .redeem {
    width: 300px;
    margin-top: 15px;
    &-label {
      width: 100px;
      text-align: right;
    }
    &-text {
      width: 200px;
      text-align: left;
      margin-left: 30px;
    }
  }
}
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}
.luckyDraw-form {
  padding-left: 50px;
  &-input {
    width: 200px;
    margin-left: 0;
    margin-left: 20px;
  }
}
</style>
