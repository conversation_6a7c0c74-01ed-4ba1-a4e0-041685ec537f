<template>
  <div class="order_report">
    <div class="mt20 mb30 row item-center justify-between">
      <div>
        <i-date-picker :value="times" :options="options" format="yyyy-MM-dd" type="daterange" placement="bottom-end" 
          placeholder="请选择时间区间" style="width: 200px" @on-change="changeTimeRange"></i-date-picker>
        <i-button @click="handleRefresh">确定</i-button>
      </div>
      <div>
        <i-button icon="md-download" @click="handleOrderExport">导出全部</i-button>
      </div>
    </div>
    <div>
      <i-table v-show="false" ref="orders"></i-table>
      <v-chart :options="orderLines" class="line_charts" />
    </div>
  </div>
</template>

<script>
import cfg from "../order/config"
import { mapGetters, mapActions } from 'vuex'
import ECharts from 'vue-echarts/components/ECharts'
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/bar'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/title'
import 'echarts/lib/component/tooltip'

export default {
  name: "order_report",
  components: {
    'v-chart': ECharts
  },
  data() {
    return {
      times: [],
      options: {
        disabledDate (date) {
          return date && date.valueOf() > Date.now() - 86400000;
        }
      },
			orderLines: {
        title: { text: '门店产品统计' },
				tooltip: { trigger: 'axis' },
				legend: { data:['兑换数量', '核销数量'] },
        grid: { left: '3%', bottom: '3%', containLabel: true },
				xAxis: { type: 'category', boundaryGap: false, data: [] },
				yAxis: { type: 'value', minInterval: 1 },
				series: [
          { name: '兑换数量', type: 'line', data: [], symbol: 'none', smooth: true },
          { name: '核销数量', type: 'line', data: [], symbol: 'none', smooth: true }
        ]
			}
    }
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  watch: {
    // 监控门店切换, 刷新数据
    currentStore(newVal, oldVal) {
      this.currentStore.dktStoreNumber && this.handleRefresh()
    }
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 初始数据
    loadData() {
      let yesterday = new Date()
      yesterday.setDate(yesterday.getDate()-1)
      let end = yesterday.format('yyyy-MM-dd')
      yesterday.setDate(yesterday.getDate()-30)
      let start = yesterday.format('yyyy-MM-dd')
      this.times = [start, end]
      this.handleRefresh()
    },
    changeTimeRange(values) {
      this.times = values
    },
    // 刷新产品兑换报告
    async handleRefresh() {
      if (this.currentStore.dktStoreNumber 
        && this.times && this.times[0] && this.times[1]) {
        let days = (new Date(this.times[1]).getTime() - new Date(this.times[0]).getTime()) / (1000 * 60 * 60 * 24)
        if (days > 30) {
          this.$message.warning({ content: '统计请勿超过30天', closable: true, duration: 3 })
          return
        }
        let resp = await this.$api.getCountReport({
          'category': 'ORDER', 
          'dktStoreNumber': this.currentStore.dktStoreNumber, 
          'startTime': this.times[0],
          'endTime': this.times[1]
        })
        if (resp.status === 'success') {
          let xAxis = [], yAxis0 = [], yAxis1 = []
          resp.data.forEach((item) => {
            if (item.status == 'ORDER_PAID') {
              xAxis.push(item.reportDate)
              yAxis0.push(item.count)
            } else if (item.status == 'ORDER_REDEEMED') {
              yAxis1.push(item.count)
            }
          })
          this.orderLines.xAxis.data = xAxis
          this.orderLines.series[0].data = yAxis0
          this.orderLines.series[1].data = yAxis1
        }
      }
    },
    handleOrderExport() {
      this.exportOrderReport()
    },
    // 导出
    async exportOrderReport() {
      if (this.currentStore.dktStoreNumber 
        && this.times && this.times[0] && this.times[1]) {
        let exportColumns = [
          { title: '商品名称', key: 'product_name' },
          { title: 'ModelCode', key: 'model_code' },
          { title: 'ItemCode', key: 'item_code' },
          { title: '状态', key: 'status' },
          { title: '兑换积分', key: 'points' }, 
          { title: '门店名称', key: 'store_name' }, 
          { title: '门店号', key: 'store_number' }, 
          { title: '门店地址', key: 'store_address' }, 
          { title: '客户姓名', key: 'customer_name' }, 
          { title: '会员卡号', key: 'card_no' }, 
          { title: '兑换时间', key: 'create_time' },
          { title: '领取时间', key: 'redeem_time' },
        ]
        let exportData = []
        let resp = await this.$api.getOrderReports({
          'dktStoreNumber': this.currentStore.dktStoreNumber,
          'startTime': this.times[0],
          'endTime': this.times[1]
        })
        if (resp.status === 'success') {
          resp.data.forEach((order) => {
            exportData.push({
              'product_name': order.pointOrderItems[0].productInfo.title,
              'model_code': order.pointOrderItems[0].productInfo.modelCode,
              'item_code': order.pointOrderItems[0].productInfo.itemCode,
              'status': cfg.convert(order.status, cfg.statusList),
              'points': order.usedPoints,
              'store_name': order.dktStoreName,
              'store_number': '\t' + order.dktStoreNumber,
              'store_address': order.dktStoreAddress,
              'customer_name': order.customerName,
              'card_no': '\t' + order.buyerCardNo,
              'create_time': order.createTime ? new Date(order.createTime).format('yyyy-MM-dd hh:mm') : '',
              'redeem_time': order.redeemTime ? new Date(order.redeemTime).format('yyyy-MM-dd hh:mm') : ''
            })
          })
          this.$refs.orders.exportCsv({
            quoted: true,
            filename: `${this.currentStore.name}_${this.times[0]}至${this.times[1]}产品报告`,
            columns: exportColumns,
            data: exportData
          });
        }
      }
    }
  }
};
</script>
<style lang="less" scoped>
.order_report {
	.line_charts {
		width: 80%;
		height: 500px;
	}
}
</style>