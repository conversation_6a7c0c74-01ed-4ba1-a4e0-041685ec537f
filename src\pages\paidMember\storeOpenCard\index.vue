<template>
  <div class="wraper">
    <i-menu class="menu-wrap" mode="horizontal" @on-select="selectMenu" :theme="theme1" active-name="openCardManage">
      <i-menuItem name="openCardManage">
        开卡礼管理
      </i-menuItem>
      <i-menuItem name="batchManage" v-has="['PAID_MEMBER_PRODUCT_BATCH_MANAGE']">
        批量管理
      </i-menuItem>
    </i-menu>
	<template v-if="menuType === 'openCardManage'">
		<OpenCardManage></OpenCardManage>
	</template>
	<template v-if="menuType === 'batchManage'">
		<BatchManage></BatchManage>
	</template>
		
  </div>
</template>

<script>
import Template from '../../product/store/profile/template.vue';
import OpenCardManage from "./components/OpenCardManage.vue"
import BatchManage from "./components/BatchManage.vue"

export default {
  data() {
    return {
	  menuType: 'openCardManage',
      theme1: "light",
    };
  },
	components: {
		OpenCardManage,
		Template,
		BatchManage
	},
	methods: {
		// 菜单选择
    selectMenu(type) {
		this.menuType = type;
    }
	}
};
</script>

<style lang="less" scoped>
	.wraper{
		height: 100%;
		.menu-wrap{ z-index: 10; }
	}
</style>