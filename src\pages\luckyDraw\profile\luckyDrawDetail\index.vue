<template>
	<i-row class="luckDrawDetail">
		<i-row class="row item-center">
			<i-col :span="12">
				<h3>订单信息</h3>
			</i-col>
			<i-col :span="12" class="text-right" v-has="['LUCKY_DRAW_ORDER_UPDATE']">
				<i-button @click="deliverGoods" type="primary">{{orderList.status=='DELIVERED'?'编辑':'发货'}}</i-button>
			</i-col>
		</i-row>
		<i-row class="mt10 mb20">
			<i-col :span="6">
				<label>订单编号：</label>
				<span>{{orderList.id}}</span>
			</i-col>
			<i-col :span="6">
				<label>客户姓名：</label>
				<span>{{orderList.buyerName}}</span>
			</i-col>
			<i-col :span="6">
				<label>订单金额(元)：</label>
				<span>{{orderList.totalAmount}}</span>
			</i-col>
			<i-col :span="6">
				<label>订单状态：</label>
				<span>{{ orderList.status === 'WAITING_FOR_DELIVERY' ? '待发货' : '已发货' }}</span>
			</i-col>
		</i-row>
		<i-row class="mt10 mb20" v-if="orderList.expressNumber">
			<i-col :span="6">
				<label>快递编号：</label>
				<span>{{orderList.expressNumber}}</span>
			</i-col>
			<i-col :span="6" v-if="orderList.expressCompany">
				<label>快递公司：</label>
				<span>{{orderList.expressCompany}}</span>
			</i-col>
		</i-row>
		<i-row v-if="orderList.orderAddress">
			<h3 class="mt20">收件人信息</h3>
		</i-row>
		<i-row class="mt10 mb30" v-if="orderList.orderAddress">
			<i-col :span="6">
				<label>收件人姓名：</label>
				<span>{{orderList.orderAddress.fullName}}</span>
			</i-col>
			<i-col :span="6">
				<label>手机号：</label>
				<span>{{orderList.orderAddress.mobile}}</span>
			</i-col>
			<i-col :span="12">
				<label>收件地址：</label>
				<span>{{orderList.orderAddress.province+orderList.orderAddress.city+orderList.orderAddress.district+orderList.orderAddress.address}}</span>
			</i-col>
		</i-row>
		<div>
			<h3>订单商品</h3>
		</div>
		<i-row class="mt15">
			<i-table :columns="orderColumns" :data="orderList.orderItems" border class="product-order-table-box" ref="table" />
		</i-row>
		<!-- 填写快递信息 -->
		<i-modal @on-ok="ok" class-name="vertical-center-modal" title="物流信息" v-model="statusFlage">
			<i-form>
				<i-row>
					<i-form-item class="luckyDraw-form row" label="订单编号：">
						<span class="luckyDraw-form-input border-0">{{filters.id}}</span>
					</i-form-item>
				</i-row>
				<i-row>
					<i-form-item class="luckyDraw-form row" label="快递单号：">
						<i-input class="luckyDraw-form-input" placeholder="请输入快递单号" v-model="filters.expressNumber" />
					</i-form-item>
				</i-row>
				<i-row>
					<i-form-item class="luckyDraw-form row" label="快递公司：">
						<i-select class="luckyDraw-form-input" placeholder="请选择快递公司" style="width:200px" v-model="filters.expressCompany">
							<i-option :key="item" :value="item" v-for="item in courierList">{{item}}</i-option>
						</i-select>
					</i-form-item>
				</i-row>
			</i-form>
		</i-modal>
	</i-row>
</template>

<script>
import cfg from '../../config'
import { Message, Spin } from 'iview'
export default {
  name: 'luckDrawDetail',

  data() {
    return {
      // 订单列表
      orderList: '',
      statusFlage: false,
      courierList: cfg.courierList,
      filters: {},
      orderColumns: [
        {
          title: '图片',
          key: 'imageUrl',
          className: 'th-class pro-img-box',
          render: (h, params) => {
            return this.showRowImage(h, params.row.productInfo.imageUrl)
          }
        },
        {
          title: '商品ID',
          key: 'id',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.productInfo.id)
          }
        },
        {
          title: '商品名称',
          key: 'name',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.productInfo.name)
          }
        },
        {
          title: '商品描述',
          key: 'description',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.productInfo.description)
          }
        },
        {
          title: '商品单价',
          key: 'price',
          className: 'th-class',
          render: (h, params) => {
            return h('span', params.row.productInfo.price)
          }
        },
        {
          title: '数量',
          key: 'quantity',
          className: 'th-class'
        },
        {
          title: '商品总价',
          key: 'totalAmountWithTax',
          className: 'th-class'
        }
      ]
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 初始数据
    loadData() {
      this.getOrderDetail()
    },
    /**
     * page envent
     */
    deliverGoods() {
      const { id, expressNumber, expressCompany } = this.orderList
      this.filters = {
        id: id,
        expressNumber: expressNumber || '',
        expressCompany: expressCompany || cfg.courierList[0]
      }
      this.statusFlage = true
    },
    ok() {
      this.updateOrder()
    },
    /**
     * data handel
     */
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'table-pic-box' }, [
        h('img', {
          attrs: {
            src: imageUrls,
            class: 'table-img proImg',
            style: 'width:2.5vw'
          }
        })
      ])
    },
    /**
     * network
     */
    async getOrderDetail() {
      const id = this.$route.params.id
      let resp = await this.$api.luckyDrawDetail(id)
      if (resp.status === 'success') {
        this.orderList = resp.data
      }
    },
    async updateOrder() {
      if (
        this.filters.expressNumber == '' ||
        this.filters.expressCompany == ''
      ) {
        Message.warning({
          content: '更新失败，快递单号和快递公司不能为空',
          closable: true,
          duration: 3
        })
        return
      }
      let resp = await this.$api.updateOrder(this.filters)
      if (resp.status == 'success') {
        this.getOrderDetail()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.luckDrawDetail {
  span {
    color: #515a6e;
  }

  .product-order-table-box {
    width: auto;
  }
  .th-class {
    text-align: center !important;
    vertical-align: middle !important;
    min-width: 120px !important;
  }
  .redeem {
    width: 300px;
    margin-top: 15px;
    &-label {
      width: 100px;
      text-align: right;
    }
    &-text {
      width: 200px;
      text-align: left;
      margin-left: 30px;
    }
  }
}
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}
.luckyDraw-form {
  padding-left: 50px;
  &-input {
    width: 200px;
    margin-left: 0;
    margin-left: 20px;
  }
}
</style>