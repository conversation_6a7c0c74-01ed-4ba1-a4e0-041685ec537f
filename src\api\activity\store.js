/**
 * 门店活动
 */
import fetch from '@/utils/network/fetch'

const createOrUpdateActivity = (data = {}) => {
  if (data.id) {
    return fetch(`/api/v1/sports/${data.id}`, { method: 'put', data: JSON.stringify(data) })
  } else {
    return fetch('/api/v1/sports', { method: 'post', data: JSON.stringify(data) })
  }
}
// 获取活动详情
const getActivityById = (id, fullSessions) => {
  return fetch(`/api/v1/sports/${id}`, { data: { fullSessions: fullSessions } })
}
// 活动列表搜索
const searchActivity = (query = {}) => {
  return fetch('/api/v1/sports', { data: query })
}
// 发布活动
const publishActivity = id => {
  return fetch(`/api/v1/sports/${id}/publish`, { method: 'put' })
}
// 预览/生成太阳码
const previewSunCode = (id, data = {}) => {
  return fetch(`/api/v1/sports/${id}/sunCode`, { method: 'post', data: JSON.stringify(data) })
}
// 获取活动Session的用户报名列表
const getSessionMembershipList = sessionId => {
  return fetch(`/api/v1/sports/sessions/${sessionId}`)
}
// 门店取消活动
const cancelSportsSession = (data = {}) => {
  return fetch(`/api/v1/sports/sessions/${data.id}`, { method: 'put', data: JSON.stringify(data) })
}

export default {
  createOrUpdateActivity,
  getActivityById,
  searchActivity,
  publishActivity,
  previewSunCode,
  getSessionMembershipList,
  cancelSportsSession
}
