<template>
  <div class="left-rule-item">
    <div class="item-label">
      <!-- 如果有 baseTypeDisplay，则使用它作为标题，否则使用 key -->
      <span class="key-title">{{ rule.baseTypeDisplay || rule.key }}</span>
      <span class="discribe-text">{{ rule.discribe }}</span>
    </div>
    <div class="item-control">
      <!-- 通用的只读逻辑，使用禁用的 i-input 以保证对齐 -->
      <i-input
        v-if="rule.isReadOnly"
        :value="rule.value"
        disabled
        style="width: 150px;"
      />

      <!-- Value 为 List 时，渲染为多选下拉框 -->
      <i-select
        v-else-if="Array.isArray(rule.value)"
        v-model="selectedIds"
        multiple="true"
        placeholder="请选择"
        style="width: 150px"
        :disabled="disabled"
      >
        <i-option
          v-for="option in rule.value"
          :key="option.id"
          :value="option.id"
        >
          {{ option.name }}
        </i-option>
      </i-select>

      <!-- Value 为 String 时，渲染为输入框 -->
      <i-input
        v-else
        v-model="editableValue"
        placeholder="请输入"
        style="width: 150px"
        @on-change="handleInputChange"
        :disabled="disabled"
      />
    </div>
  </div>
</template>

<script>
import ChannelSelectRule from './channelSelectRule.vue'

export default {
  name: 'LeftRuleItemRule',
  components: {
    ChannelSelect: ChannelSelectRule
  },
  props: {
    rule: {
      type: Object,
      required: true
    },
    ruleIndex: {
      type: Number,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editableValue: null
    };
  },
  computed: {
    // 为多选下拉框创建一个计算属性，用于 v-model
    selectedIds: {
      get() {
        if (Array.isArray(this.rule.value)) {
          // 从选项列表中过滤出所有 selected: true 的项，并返回它们的id数组
          return this.rule.value.filter(opt => opt.selected).map(opt => opt.id);
        }
        return [];
      },
      set(newIds) {
        // 如果组件被禁用，则不发出任何更新事件
        if (this.disabled) {
          return;
        }
        // 当 i-select 的值改变时，v-model 会调用这个setter
        // 我们不直接修改prop，而是发出一个事件
        this.$emit('update-rule', {
          index: this.ruleIndex,
          newValue: newIds,
          isList: true // 标记这是一个列表类型的更新
        });
      }
    }
  },
  watch: {
    rule: {
      handler(newRule) {
        // 只为非数组、非只读的项初始化本地 editableValue
        if (!Array.isArray(newRule.value) && !newRule.isReadOnly) {
          this.editableValue = newRule.value;
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 当普通输入框的值改变时，发出事件
    handleInputChange() {
      // 如果组件被禁用，则不发出任何更新事件
      if (this.disabled) {
        return;
      }
      this.$emit('update-rule', {
        index: this.ruleIndex,
        newValue: this.editableValue,
        isList: false // 标记这不是一个列表类型的更新
      });
    }
  }
};
</script>

<style lang="less" scoped>
.left-rule-item {
  display: flex;
  align-items: center;
  padding: 10px;
  padding-right: 20px;
  border-bottom: 1px solid #e8eaec;

  &:last-child {
    border-bottom: none;
  }

  .item-label {
    display: flex;
    flex-direction: column;
    width: 160px;
    flex-shrink: 0;

    .key-title {
      font-weight: bold;
      font-size: 14px;
    }

    .discribe-text {
      font-size: 12px;
      color: #808695;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
