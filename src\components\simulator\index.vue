<template>
  <div
    class="simulator-modal column item-center"
    v-show="opened"
  >
    <div class="s-container">
      <div class="s-screen">
        <activity v-if="page === 'activity'" :details="details"/>
      </div>
    </div>
    <!-- <img class="close-img"  src="" alt="关闭"> -->
    <div class="close-img" @click="hide">
      <img src="../../assets/image/error.png" alt="">
    </div>
  </div>
</template>

<script>
import activity from "./pages/activity";

export default {
  name: "simulator",
  components: {
    activity
  },
  props: {
    // 对应组件的名称
    page: ''
  },
  data() {
    return {
      opened: false,
      details: {}
    }
  },
  mounted() {
    console.log(this.path)
  },
  methods: {
    // 对外提供方法
    show(objc) {
      this.details = objc
      this.opened = true
    },
    hide() {
      this.opened = false
    }
  },
  watch: {
    // 防止加载iframe卡顿
    opened(newV, oldV) {
      if (newV === true) {
        setTimeout(() => {
          this.isWebView = true
        }, 0)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.simulator-modal {
  position: fixed;
  z-index: 10001;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  .s-container {
    margin-top: 10vh;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 250px;
    height: 542px;
    background-size: 100%;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-image: url('../../assets/image/phone.png');
    .s-screen {
      width: 230px;
      height: 420px;
      background: #fff;
    }
  }
  .close-img {
    height: 20px;
    width: 20px;
    margin-top: 20px;
    img {
      width: 100%;
      height: auto;
    }
  }
}
</style>
