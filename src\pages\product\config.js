// 状态
const statusList = [
  { value: "UNPUBLISHED", label: "未发布" },
  { value: "PUBLISHED", label: "已发布" }
];

const levelList = [
  {
    code: "0",
    value: "ON_SALE",
    label: "限时优选",
    dataKey: "pointProductSale",
    min: 0,
    max: 5
  },
  {
    code: "1",
    value: "PDT_L1",
    label: "L1 (1 - 1000 燃值)",
    dataKey: "pointProductL1",
    min: 3,
    max: 5
  },
  {
    code: "2",
    value: "PDT_L2",
    label: "L2 (1001 - 1500 燃值)",
    dataKey: "pointProductL2",
    min: 3,
    max: 5
  },
  {
    code: "3",
    value: "PDT_L3",
    label: "L3 (1501 - 2000 燃值)",
    dataKey: "pointProductL3",
    min: 3,
    max: 5
  },
  {
    code: "4",
    value: "PDT_L4",
    label: "L4 (2000+ 燃值)",
    dataKey: "pointProductL4",
    min: 3,
    max: 5
  }
];

// 字段名称转换
const convert = function(value, list) {
  let result = value;
  for (let i = 0; i < list.length; i++) {
    let item = list[i];
    if (item.value === value) {
      result = item.label;
      break;
    }
  }
  return result;
};

export default {
  statusList,
  levelList,
  convert
};
