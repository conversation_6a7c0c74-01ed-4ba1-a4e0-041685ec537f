<template>
  <div class="gallery gallery-content">
    <div class="header mt20 mb30 flex row item-center justify-between">
        <div class="flex row itema-center">
          <i-select class="searchs mr15" v-model="searchFrom.categoryId" placeholder="请选择运动分类">
            <i-option value="">全部</i-option>
            <i-option :value="item.id" v-for="item in categoryList" :key="item.id">
              {{item.name}}
            </i-option>
          </i-select>
          <i-input v-model="searchFrom.name" class="searchs mr15" search placeholder="模糊查询(图片名称)"/>
          <i-button @click="searchGallery">查询</i-button>
        </div>
        <i-button icon="md-add" @click="createClick" type="primary" v-has="['EVENT_IMAGE_POOL_UPLOAD']">新建</i-button>
    </div>
    <i-row>
      <i-col class="flex column item-center" span="6" v-for="(item,index) in galleryList" :key='(item,index).id'>
        <i-card class="text-center cards mb15">
          <div @mouseover="onMouseOver(index, item)">
            <img class="cards-img" :src="item.url" alt="">
            <div class="text-center mt10"> {{item.name}} </div>
          </div>
          <div @mouseout="onMouseOut(index)" class="card-hover" :class="currentIndex === index  ? 'galleryDisabled' : ''"></div>
          <div class="btn-group hide-btn" :class="currentIndex === index ? 'show-btn' : ''">
            <i-button @click="handleDisable(item)" icon="md-close" shape="circle" v-has="['EVENT_IMAGE_POOL_UPLOAD']"/>
            <i-poptip trigger="hover" placement="top" title="修改图片标题">
              <i-button @click="handleEdit(item.id, item.name)" icon="md-create" shape="circle" v-has="['EVENT_IMAGE_POOL_UPLOAD']"/>
              <div slot="content" style="width:160px">
                <i-row>
                  <i-input placeholder="请输入名称" v-model="galleryTitle"/>
                </i-row>
                <i-row class="mt15">
                  <i-button @click="confirmEditTitle(item.id)" type="primary">确认</i-button>
                </i-row>
              </div>
            </i-poptip>
          </div>
        </i-card>
      </i-col>
    </i-row>
    <i-page class="text-right mt15" show-total :total="page.total" :page-size="page.size" size="small" @on-change="onPageChange"/>
    <z-modal v-if="modalTitle" :title="modalTitle" :certain="modalCertain" :cancel="modalCancel" :id="''" />
  </div>
</template>

<script>
import Modal from './profile/modal'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'activity_gallery',
  components: {
    'z-modal': Modal
  },
  data() {
    return {
      searchFrom: {
        name: '',         // 图片名称
        categoryId: ''    // 分类ID
      },
      galleryList: [],    // 图库列表
      categoryList: [],   // 运动分类
      modalTitle: '',     // 模态框
      page: {
        no: 1,      // 页码
        size: 12,   // 每页显示的条数
        total: 0    // 总条数
      },
      galleryTitle: '',
      currentIndex: null, // 当前index
      isDisabled: false,  // 禁用的状态
      id: ''
    }
  },

  mounted() {
    this.loadData()
  },

  computed: {
    ...mapGetters(['currentStore'])
  },

  watch: {
    // 监控门店切换, 刷新数据
    currentStore(newVal, oldVal) {
    }
  },

  methods: {
    // 初始数据
    loadData() {
      this.getCategories()
      this.searchGallery()
    },

    // 新建
    createClick() {
      this.modalTitle = '创建图片模版'
    },

    // 模态框
    modalCertain() {
      this.modalTitle = ''
      this.id = ''
      this.searchGallery()
    },
    modalCancel() {
      this.modalTitle = ''
      this.id = ''
    },
    // 分页改变
    onPageChange(no) {
      this.page.no = no
      this.searchGallery()
    },
    // hover相关事件
    onMouseOver(index, item) {
      this.galleryTitle = item.name
      this.currentIndex = index;
    },
    onMouseOut(index) {
      this.currentIndex = null;
    },
    // 删除事件
    handleDisable(item) {
      this.isDisabled = true;
      this.$modal.confirm({
        'title': '图片删除',
        'content': `您即将删除图片<span class='confirm-msg'>${item.name}</span>.<br>请确认!`,
        'okText': '确定',
        'cancelText': '取消',
        'onOk': () => {
          this.doDisableAction(item.id)
        }
      })
    },
    handleEdit(id, name) {
      this.galleryTitle = name
      this.id = id;
    },

    // 网络请求
    async searchGallery() {
      this.searchFrom['pageNo'] = this.page.no
      this.searchFrom['pageSize'] = this.page.size
      let resp = await this.$api.getGalleries(this.searchFrom)
      this.galleryList = resp.data.items
      this.page.total = resp.data.total
    },
    // 获取分类
    async getCategories() {
      let resp = await this.$api.getSportsCategories()
      this.categoryList = resp.data
    },
    // 删除图片
    async doDisableAction(id) {
      let resp = await this.$api.deleteGallery(id)
      if (resp.status === 'success') {
        this.searchGallery()
      }
    },
    //修改标题
    async confirmEditTitle (id) {
      let res
      if (this.id) {
        res = await this.$api.updateGalleryName(this.id, this.galleryTitle)
        if (res.status === 'success') {
          this.currentIndex = null
          this.searchGallery()
          this.certain && this.certain()
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import url('../css/activityCommon.less');
.gallery-content {
  .searchs {
    width: 200px;
  }
  .cards {
    width: 18vw;
    &-img {
      width: 14vw;
      height: 8vw;
    }
  }
  .galleryDisabled {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    opacity: .7;
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }
  .hide-btn {
     display: none;
   }
   .show-btn {
    display: flex;
   }
   .btn-group {
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 40%;
    left: 0;
    right: 0;
    button {
      margin:0 5px;
    }
   }
}
</style>

