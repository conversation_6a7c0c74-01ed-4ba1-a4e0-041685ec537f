
<template>
	<div>
		<i-table class="expand-row" :columns="itemColumns" :data="items"></i-table>
	</div>
</template>
<script>
export default {
	props: {
		items: Array
	},
	data() {
    return {
			itemColumns: [
				{ title: '图片', key: 'imageUrl', className: 'th-class item-img-box', 
					render: (h, params) => { return this.showRowImage(h, params.row.imageUrl) } },
				{ title: '名称', key: 'label', className: 'th-class', width: 300 },
				{ title: '款号', key: 'item_id', className: 'th-class', width: 100 },
				{ title: '颜色', key: 'color', className: 'th-class', width: 100 },
				{ title: '尺寸', key: 'size', className: 'th-class', width: 100 },
				{ title: '数量', key: 'quantity', className: 'th-class', width: 100 },
				{ title: '价格', key: 'unitary_amount', className: 'th-class' }
			],
		}
	},
	methods: {
    showRowImage(h, imageUrl) {
      return h('div', [ h('img', { attrs: { src: imageUrl, width: 50, height: 50, alt: '' } }) ])
		},
	}
};
</script>
<style lang="less" scoped>
.expand-row {
	margin-bottom: 16px;
}
</style>
