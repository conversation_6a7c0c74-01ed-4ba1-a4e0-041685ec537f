<template>
  <div class="lucky_draw_report">
    <div class="mt20 mb30 row item-center justify-between">
      <div>
        <i-date-picker :value="times" :options="options" format="yyyy-MM-dd" type="daterange" placement="bottom-end" 
          placeholder="请选择时间区间" style="width: 200px" @on-change="changeTimeRange"></i-date-picker>
        <i-button @click="handleRefresh">确定</i-button>
      </div>
    </div>
    <div>
      <v-chart :options="luckyDrawLines" class="line_charts" />
    </div>
  </div>
</template>

<script>
import ECharts from 'vue-echarts/components/ECharts'
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/bar'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/title'
import 'echarts/lib/component/tooltip'

export default {
  name: "lucky_draw_report",
  components: {
    'v-chart': ECharts
  },
  data() {
    return {
      times: [],
      options: {
        disabledDate (date) {
          return date && date.valueOf() > Date.now() - 86400000;
        }
      },
			luckyDrawLines: {
        title: { text: '抽奖次数统计' },
				tooltip: { trigger: 'axis' },
				legend: { data:['抽奖次数'] },
        grid: { left: '3%', bottom: '3%', containLabel: true },
				xAxis: { type: 'category', boundaryGap: false, data: [] },
				yAxis: { type: 'value', minInterval: 1 },
				series: [{ name: '抽奖次数', type: 'line', data: [], symbol: 'none', smooth: true }]
			}
    }
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 初始数据
    loadData() {
      let yesterday = new Date()
      yesterday.setDate(yesterday.getDate()-1)
      let end = yesterday.format('yyyy-MM-dd')
      yesterday.setDate(yesterday.getDate()-30)
      let start = yesterday.format('yyyy-MM-dd')
      this.times = [start, end]
      this.handleRefresh()
    },
    changeTimeRange(values) {
      this.times = values
    },
    // 刷新未发布产品门店
    async handleRefresh() {
      if (this.times && this.times[0] && this.times[1]) {
        let days = (new Date(this.times[1]).getTime() - new Date(this.times[0]).getTime()) / (1000 * 60 * 60 * 24)
        if (days > 30) {
          this.$message.warning({ content: '统计请勿超过30天', closable: true, duration: 3 })
          return
        }
        let resp = await this.$api.getCountReport({
          'category': 'LUCKY_DRAW',
          'startTime': this.times[0],
          'endTime': this.times[1]
        })
        if (resp.status === 'success') {
          let xAxis = [], yAxis = []
          resp.data.forEach((item) => {
            if (item.status == 'LUCKY_DRAWN') {
              xAxis.push(item.reportDate)
              yAxis.push(item.count)
            }
          })
          this.luckyDrawLines.xAxis.data = xAxis
          this.luckyDrawLines.series[0].data = yAxis
        }
      }
    },

    // 导出报表
    // handleExport() {
    //   let productStores = Object.assign([], this.productStoreList)
    //   productStores.forEach((value) => {
    //     value.dktStoreNumber = '\t' + value.dktStoreNumber
    //   })
    //   let floatPages = this.page.total / this.page.size
    //   let pages = this.page.total % this.page.size == 0 ? parseInt(floatPages) : parseInt(floatPages) + 1
    //   this.$refs.store.exportCsv({
    //     quoted: true,
    //     filename: `${this.batchNumber}_未发布产品门店报表_${this.page.no}-${pages}`,
    //     columns: this.productStoreColumns,
    //     data: productStores
    //   });
    // }
  }
};
</script>
<style lang="less" scoped>
.lucky_draw_report {
  .table-footer {
    margin: 20px 0;
    overflow: hidden;
	}
	.line_charts {
		width: 80%;
		height: 500px;
	}
}
</style>