<script>
import RuleEdit from './profile/ruleEdit.vue'

export default {
  name: 'RiskRules',
  components: {
    RuleEdit
  },
  data() {
    return {
      modalTitle: '',
      modalType: 'view',
      currentId: '',
      showModal: false, // 添加显示modal的控制变量
      rulesList: [],
      rulesColumns: [
        {
          title: 'ID',
          key: 'rule_id',
          width: 60,
          className: 'th-class th-class-name text'
        },
        {
          title: '规则名称',
          key: 'rule_name',
          className: 'th-class th-class-name text'
        },
        {
          title: '说明',
          key: 'rule_desc',
          className: 'th-class th-class-name text'
        },
        {
          title: '触发类型',
          key: 'trigger_type',
          className: 'th-class th-class-name text'
        },
        {
          title: '状态',
          key: 'enable_status',
          width: 100,
          className: 'th-class th-class-name text',
          render: (h, params) => {
            return h('span', params.row.enable_status ? '启用' : '停用')
          }
        },
        {
          title: '最后更新时间',
          key: 'update_time',
          width: 180,
          className: 'th-class th-class-name text'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          className: 'th-class th-class-name',
          width: 190,
          render: (h, params) => {
            const operations = []

            operations.push(h('i-button', {
              class: 'group-operation-btn ml5 mr5 mt5 mb5',
              props: {
                type: 'primary',
                ghost: true
              },
              on: {
                click: () => {
                  this.handleEdit(params)
                }
              }
            }, '编辑'))

            operations.push(h('i-button', {
              class: 'group-operation-btn ml5 mr5 mt5 mb5',
              props: {
                type: 'primary',
                ghost: true
              },
              on: {
                click: () => {
                  this.handleView(params)
                }
              }
            }, '查看'))

            return h('div', operations)
          }
        }
      ]
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    // 获取表格数据
    async queryTableData() {
      try {
        // TODO: 替换为实际的API调用
        const resp = await this.$api.queryAllRules()

        if (resp.status === 'success') {
          this.rulesList = resp.data
        }
      } catch (error) {
        this.$Message.error('获取数据失败')
      }
    },

    // 创建按钮点击事件
    handleCreate() {
      this.currentId = ''
      this.modalType = 'create'
      this.modalTitle = '创建规则则'
    },

    // 编辑按钮点击事件
    handleEdit(params) {
      this.currentId = params.row.rule_id
      this.modalType = 'edit'
      this.modalTitle = '编辑规则'
    },

    // 查看按钮点击事件
    handleView(params) {
      this.currentId = params.row.rule_id
      this.modalType = 'view'
      this.modalTitle = '查看规则'
    },

    // 弹窗取消事件
    handleModalCancel() {
      this.modalTitle = ''
      this.currentId = ''
      this.modalType = 'view'
      this.queryTableData()
    }
  }
}
</script>

<template>
  <div class="risk-rules">
    <div class="header">
      <i-button type="primary" @click="handleCreate">创建</i-button>
    </div>
    <i-row>
      <i-table :columns="rulesColumns" :data="rulesList" border class="risk-rules-table-box" />
    </i-row>
    <rule-edit
      v-if="modalTitle"
      :title="modalTitle"
      :id="currentId"
      :type="modalType"
      :isView="modalType === 'view'"
      :cancel="handleModalCancel"
    />
  </div>
</template>

<style lang="less" scoped>
.risk-rules {
  &-table-box {
    min-height: 200px;
    background: #fff;
  }
}

.header {
  margin-bottom: 16px;
}

/deep/ .th-class-name.text {
  font-size: 14px !important;
}

/deep/ .group-operation-btn {
  color: rgba(54, 67, 186, 1);
  background-color: rgba(0,0,0,0);
  border: none;
  font-size: 14px !important;

  &:active, &:focus, &:hover {
    background-color: rgba(0,0,0,0);
    border: none;
    box-shadow: none;
  }
}

/deep/ .ivu-btn-ghost {
  &.ivu-btn-primary {
    color: rgba(54, 67, 186, 1);
    background-color: transparent;
    border: none;
  }
}
</style>
