<template>
  <div class="container">
    <i-modal
      v-model="show"
      :title="title"
      :mask-closable="false"
      :closable="false"
      width="1110"
      footer-hide
    >
      <div class="modal-content mt30 flex column">
        <!-- 基础信息组件 -->
        <base-info-model
          ref="baseInfoModel"
          :type="type"
          :biz_type="biz_type"
          :scene_id="scene_id"
          @save-success="handleBaseInfoSaveSuccess"
        />

        <!-- 规则控制组件 -->
        <rule-edit-model
          ref="rulesModel"
          :type="type"
          :biz_type="biz_type"
          :scene_id="scene_id"
          @refresh-data="handleRefreshData"
        />

        <!-- 评估记录组件 -->
        <record-model
          ref="recordModel"
          :biz_type="biz_type"
          :scene_id="scene_id"
          :type="type"
        />

        <div class="modal-footer">
          <i-button v-if="type === 'edit' || type === 'create'" type="primary" @click="handleSaveModel" style="margin-right: 10px;">保存</i-button>
          <i-button @click="handleCancel">关闭</i-button>
        </div>
      </div>
    </i-modal>
  </div>
</template>

<script>
import BaseInfoModel from '../components/baseInfoModel'
import RuleEditModel from '../components/ruleEditModel'
import RecordModel from '../components/recordModel'
export default {
  name: 'ModelModal',
  components: {
    BaseInfoModel,
    RuleEditModel,
    RecordModel
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    biz_type: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'view' // view, edit, create
    },
    scene_id: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      show: true,
      modelData: null // 存储从API获取的数据
    }
  },
  mounted() {
    if (this.type === 'edit' || this.type === 'view') {
      this.initData()
    }
  },
  methods: {
    handleCancel() {
      this.show = false
      this.$emit('cancel')
    },

    handleBaseInfoSaveSuccess() {
      // 基础信息保存成功后的处理，通知父组件刷新数据
      this.$emit('save-success')
    },

    async handleSaveModel() {
      try {
        const baseInfoData = this.$refs.baseInfoModel.getFormData();

        if (this.type === 'edit') {
          // 编辑模式：使用 updateScene API
          const params = {
            scene_id: this.scene_id,
            classification: baseInfoData.classification,
            scene_name: baseInfoData.scene_name,
            scene_desc: baseInfoData.scene_desc,
            intercept_status: baseInfoData.intercept_status,
            enable_status: baseInfoData.enable_status,
            create_by: baseInfoData.create_by
          }
          const resp = await this.$api.updateScene(params)
          if (resp.status === 'success') {
            this.$message.success('更新成功')
            this.$emit('save-success')
            this.initData(); // 编辑模式下重新查询数据
          }
        } else if (this.type === 'create') {
          // 创建模式：使用 createScene API
          const params = {
            classification: baseInfoData.classification,
            scene_name: baseInfoData.scene_name,
            scene_desc: baseInfoData.scene_desc,
            intercept_status: baseInfoData.intercept_status,
            enable_status: baseInfoData.enable_status,
            create_by: baseInfoData.create_by
          }
          const resp = await this.$api.createScene(params)
          if (resp.status === 'success') {
            this.$message.success('创建成功')
            this.$emit('save-success')
            this.handleCancel()
            // 创建模式下不调用 initData，直接关闭弹窗
          }
        }
      } catch (error) {
        this.$message.error(this.type === 'edit' ? '更新失败' : '创建失败')
        console.error('保存失败:', error)
      }
    },

    // 处理子组件的数据刷新请求
    handleRefreshData() {
      this.initData();
    },

    async initData() {
      try {
        const resp = await this.$api.querySceneDetail(this.scene_id)
        // const resp = {
        //   status: 'success',
        //   data: {
        //     "scene_id": 1,
        //     "classification": "风险控制",
        //     "scene_name": "燃值抽奖",
        //     "scene_desc": "用户注册场景风险评估",
        //     "intercept_status": false,
        //     "enable_status": true,
        //     "update_time": "2025-07-21 09:24:45",
        //     "create_by": "admin",
        //     "rule_list": [
        //       {
        //         "rule_id": 5,
        //         "rule_name": "会员常去门店",
        //         "rule_desc": "会员常去门店属于相关门店",
        //         "point": 50,
        //         "enable_status": false,
        //         "update_time": "2025-07-21 09:29:34",
        //         "trigger_type": "个人信息变更"
        //       },
        //       {
        //         "rule_id": 6,
        //         "rule_name": "高频交易检测",
        //         "rule_desc": "检测用户高频交易行为",
        //         "point": 30,
        //         "enable_status": true,
        //         "update_time": "2025-07-21 10:15:22",
        //         "trigger_type": "交易行为"
        //       }
        //     ],
        //     "rule_point_sum": 80
        //   }
        // }

        if (resp.status === 'success') {
          this.modelData = resp.data;

          // 等待组件加载完成后设置数据
          this.$nextTick(() => {
            // 传递基础信息数据给 BaseInfoModel
            if (this.$refs.baseInfoModel) {
              this.$refs.baseInfoModel.setFormData(this.modelData);
            }

            // 传递规则列表数据给 RulesModel
            if (this.$refs.rulesModel) {
              this.$refs.rulesModel.setRuleList(this.modelData.rule_list || []);
            }
          });
        }
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  /deep/ .ivu-modal {
    width: 1200px !important;
    top: 50px;
    margin-bottom: 50px;
  }

  /deep/ .ivu-modal-content {
    height: calc(100vh - 100px);
  }

  /deep/ .ivu-modal-body {
    height: calc(100% - 108px);
    padding: 0;
    overflow-y: auto;
  }
}

.modal-content {
  padding: 20px;
  min-height: 100%;
  background: #f8f8f9;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #e8eaec;
  margin-top: 20px;
  background: #fff;
}

.mt30 {
  margin-top: 30px;
}
</style>
