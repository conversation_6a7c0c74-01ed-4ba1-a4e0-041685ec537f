/*
 * 网络请求封装
 */
import config from '@/config/apiConfig'
import ajax from './ajax.js'
import error from '@/config/errorConfig'
import customErrUrl from '@/config/customErrUrl'

import { Message, Spin } from 'iview'

/* 网络请求 */
const fetch = async (url, objc = {}) => {
  // 区分环境
  url = process.env.NODE_ENV === 'development' ? url : url.replace(/^\/api/, '/sports-api/bo/api')

  // 加载
  objc.loading = objc.loading ? objc.loading : true
  if (objc.loading) {
    _Util.showLoading()
  }

  // 默认请求方法
  objc.method = objc.method ? objc.method : 'get'

  // 请求参数
  objc.data = objc.data ? objc.data : {}
  if (objc.method === 'get') {
    objc.data = { params: objc.data }
  }

  // 请求处理
  try {
    // 区分表单提交
    let res = null
    if (objc.isForm) {
      let autoConfig = {
        headers: {
          'Content-Type': 'multipart/form-data',
          token: 'token'
        }
      }
      if (objc.requestBaseUrl) {
        autoConfig.requestBaseUrl = objc.requestBaseUrl;
      }
      res = await ajax.post(url, objc.data, autoConfig)
    } else {
      if (objc.requestBaseUrl) {
        objc.data.requestBaseUrl = objc.requestBaseUrl;
      }
      res = await ajax[objc.method](url, objc.data)
    }
    _Util.hideLoading()
    let isCustomErr = customErrUrl.customErrList.indexOf(url) > -1;
    // 统一处理返回
    if (res.status === 200) {
      if (res.data.status == 'fail' && !isCustomErr) {
        let errorMessage = ''
        const { code, message } = res.data
        if (error[code]) {
          errorMessage = error[code]
        } else if (message) {
          errorMessage = message
        } else {
          errorMessage = error['DEFAULT']
        }
        _Util.showError(errorMessage)
      }
      return res.data
    } else {
      let errStruct = _Util.buildErrorConfig(res.code)
      return errStruct
    }
  } catch (error) {
    _Util.hideLoading()
    _Util.showWarning('您的网络似乎有点问题!')
  }
}

/* 处理工具 */
const _Util = {
  // 构造错误数据结构
  buildErrorConfig(errCode) {
    let msg = config.ERROR_CONF[errCode]
    return {
      code: errCode,
      errMsg: msg ? msg : '您的网络似乎有点问题!'
    }
  },

  // 打开loading
  showLoading() {
    Spin.show()
  },

  // 关闭loading
  hideLoading() {
    setTimeout(() => {
      Spin.hide()
    }, 500)
  },

  // 提示
  showSuccess(msg) {
    Message.success({
      content: msg,
      closable: true,
      duration: 3
    })
  },
  showWarning(msg) {
    Message.warning({
      content: msg,
      closable: true,
      duration: 3
    })
  },
  showError(msg) {
    Message.error({
      content: msg,
      closable: true,
      duration: 3
    })
  }
}

export default fetch
