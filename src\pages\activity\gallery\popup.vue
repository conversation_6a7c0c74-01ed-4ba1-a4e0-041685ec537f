<template>
  <div class="container">
    <i-modal footer-hide :mask-closable="false" title="选择图片" width="80vw" v-model="show" @on-cancel="certainChoose" >
      <div class="gallery">
      <div class="header mt10 mb15 row item-center justify-between" style="padding:0 2%">
        <div class="row item-center">
        <i-select class="search mr15" v-model="galleryForm.categoryId" clearable placeholder="请选择活动分类">
          <i-option value="">全部</i-option>
          <i-option :value="item.id" v-for="item in categoryList" :key="item.id">{{item.name}}</i-option>
        </i-select>
        <i-input v-model="galleryForm.name" class="search mr15" search placeholder="模糊查询(图片名称)" clearable />
        <i-button @click="searchGallery">查询</i-button>
        </div>
      </div>
      <i-row>
        <i-col class="mb15" span="6" v-for="(item) in galleryList" :key='item.id' style="display:flex;align-items:center;justify-content:center">
          <i-card class="card">
            <img class="card-img" :src="item.url" alt="" @click="certainChoose(item)">
            <div class="text-center">{{item.name}}</div>
          </i-card>
        </i-col>
      </i-row>
      </div>
      <div style="margin: 10px;overflow: hidden">
        <div style="float: right;">
          <i-page :total="total" :current="pageNo" :page-size="pageSize" 
            :page-size-opts="pageSizeOption" @on-change="changePageNo" @on-page-size-change="changePageSize"/>
        </div>
      </div>
    </i-modal>
  </div>
</template>
<script>
export default {
  name: "gallery_popup_modal",
  props: {
    close: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      pageSizeOption: [10, 20, 50, 100],
      pageNo: 1,
      pageSize: 4,
      total: 0,

      show: false,
      categoryList: [],     // 分类列表
      galleryList: [],      // 图库列表
      galleryForm: {
        categoryId: '',     // 分类ID
        name: ''            // 图片名称
      }
    }
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData() {
      this.getCategories()
      this.searchGallery()
      this.show = true
    },

    // 获取分类列表
    async getCategories() {
      let categories = await this.$api.getSportsCategories()
      this.categoryList = categories.data
    },
    // 搜索图片
    async searchGallery() {
      let params = {
        name: this.galleryForm.name,
        categoryId: this.galleryForm.categoryId,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      let galleries = await this.$api.getGalleries(params)
      this.galleryList = galleries.data.items
      this.total = galleries.data.total
    },
    // 分页操作
    changePageNo(no) {
      this.pageNo = no
      this.searchGallery()
    },
    changePageSize(size) {
      this.pageSize = size
      this.searchActivity()
    },
    // 选择图片
    certainChoose(item) {
      this.show = false
      this.close && this.close(item)
    },
  }
};
</script>
<style lang="less" scoped>
.gallery {
  .search {
    width: 200px;
  }
  .card {
    width: 16.4vw;
    &-img {
      width: 14vw;
      height: 8vw;
    }
  }
}
</style>
