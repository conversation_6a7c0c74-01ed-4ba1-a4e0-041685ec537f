<template>
  <div class="activity-manage">
    <p class="tab">{{ activeName === 'awardConfig' ? '奖品配置' : '活动配置' }}</p>
    <!-- 奖品配置 Start-->
    <template v-if="activeName === 'awardConfig'">
      <div class="top-tips">
        <p>当前概率合计:<i :class="['percent', totalProbabilityColor]">{{ curTotalProbability }}%</i></p>
      </div>
      <div class="conent-wrap">
        <i-row class="table-wrap">
          <i-table class="text-center" border :columns="activityColumns" :data="productInfoList">
            <template slot-scope="{ index, row }" slot="drawPossibility">
              <div :class="['slot-input', checkProbability(row) ? 'highlight' : '']">
                <i-input placeholder="" :disabled="pageInfo.pageType === 'view'" type="text" size="small"
                  v-model="productInfoList[index].drawPossibility" @on-change="handleGailvChange($event, index)" />
                <p style="margin-left: 5px;">%</p>
              </div>
            </template>
            <!-- 操作 -->
            <template slot-scope="{ index, row }" slot="action">
              <i-button :disabled="isRowBtnDisabled(row)" @click="toEditSingleAward(row)">编辑</i-button>
            </template>
          </i-table>
        </i-row>
      </div>

      <div class="bottom-bar">
        <template v-if="pageInfo.pageType === 'view'">
          <i-button type="primary" @click="goNext">下一步</i-button>
          <i-button class="cancel" @click="goReturn">取消</i-button>
        </template>
        <template v-else>
          <i-button type="primary" @click="goNext">下一步</i-button>
          <i-button v-if="showSaveDraftBtn" class="save" @click="handleDraftSave">保存并返回</i-button>
          <i-button class="cancel" @click="handleCancel">取消</i-button>
        </template>
      </div>
    </template>
    <!-- 奖品配置 End-->

    <!-- 活动配置 Start-->
    <template v-if="activeName === 'activityConfig'">
      <div class="activity-config">
        <i-form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="120">
          <i-form-item label="背景图" prop="imageUrl" class="upload-item">
            <upload-img :disabledStatus="pageInfo.pageType === 'view'" :imageUrl="formValidate.imageUrl"
              @updateImageUrl="updateBgImage" :pixelLimit="bgPixelLimit" :sizeLimit="bgSizeLimit" />
          </i-form-item>
          <i-form-item label="活动规则" prop="activityRule" class="activity-rule">
            <div class="rule-input">
              <i-input v-model="formValidate.activityRule" :disabled="pageInfo.pageType === 'view'" type="textarea"
                :rows="4" :maxlength="activityRuleMaxLength" show-word-limit placeholder="请输入活动规则"></i-input>
              <p class="text-count">{{ formValidate.activityRule.length }}/{{ activityRuleMaxLength }}</p>
            </div>
          </i-form-item>
          <i-form-item label="卡片标题" prop="activityName" class="card-title">
            <div class="rule-input">
              <i-input v-model="formValidate.activityName" :disabled="pageInfo.pageType === 'view'"
                :maxlength="activityNameMaxLength" placeholder="请输入活动名称"></i-input>
              <p class="text-count">{{ formValidate.activityName.length }}/{{ activityNameMaxLength }}</p>
            </div>
          </i-form-item>
          <i-form-item label="转发卡片" prop="forwardImageUrl" class="upload-item">
            <upload-img :disabledStatus="pageInfo.pageType === 'view'" :imageUrl="formValidate.forwardImageUrl"
              @updateImageUrl="updateShareCardImage" :pixelLimit="forwardImageUrlPixelLimit"
              :sizeLimit="sharecardSizeLimit" />
          </i-form-item>
          <i-form-item label="活动生效时间：" prop="activityEffectType">
            <i-radio-group v-model="formValidate.activityEffectType" @on-change="handleActivityTypeChange">
              <i-radio label="NOW" :disabled="disableEditEffectTime">立即</i-radio>
              <i-radio label="TIMING" :disabled="disableEditEffectTime">定时</i-radio>
              <i-date-picker :disabled="disableEditEffectTime" v-if="formValidate.activityEffectType === 'TIMING'"
                @on-change="changePlanEffectTime" format="yyyy-MM-dd HH:mm:ss" v-model="formValidate.planEffectTime"
                type="datetime" placeholder="Select date and time" style="width: 200px"></i-date-picker>
            </i-radio-group>
          </i-form-item>
          <div class="bottom-bar">
            <template v-if="pageInfo.pageType === 'view'">
              <i-button type="primary" @click="goPreviousStep">上一步</i-button>
              <i-button class="cancel" @click="goReturn">取消</i-button>
            </template>
            <template v-else>
              <i-button type="primary" @click="handleSubmit('formValidate')">发布</i-button>
              <i-button v-if="showSaveDraftBtn" class="save" @click="handleDraftSave">保存并返回</i-button>
              <i-button class="save" @click="goPreviousStep">上一步</i-button>
              <i-button class="save cancel" @click="handleCancel">取消</i-button>
            </template>
          </div>
        </i-form>
      </div>
    </template>
    <!-- 活动配置 End-->
  </div>
</template>

<script>
import uploadImg from '@/components/uploadImg/uploadImg';
import toggleTab from '@/components/toggleTab/index';
import { pageUrl } from '@/config/constant'
import { addNum } from '@/utils/util';
export default {
  name: 'awardActivityConfig',
  components: {
    'upload-img': uploadImg,
    'toggle-tab': toggleTab
  },
  computed: {
    // 是否禁用编辑活动生效时间
    disableEditEffectTime() {
      const { pageType, activityStatus } = this.pageInfo;
      const isView = pageType === 'view';
      const isEditInEffect = pageType === 'edit' && activityStatus === 'IN_EFFECT';
      return isView || isEditInEffect;
    },
    // 是否显示 保存草稿按钮
    showSaveDraftBtn() {
      const { pageType, activityStatus } = this.pageInfo;
      const isInEffect = pageType === 'edit' && activityStatus === 'IN_EFFECT';      // 编辑生效中的活动 不显示保存草稿按钮
      const isTobeEffect = pageType === 'edit' && activityStatus === 'TO_BE_EFFECT'; // 编辑待生效的活动 不显示保存草稿按钮
      return !(isInEffect || isTobeEffect);
    }
  },
  data() {
    return {
      activeName: "awardConfig",
      pageInfo: {
        pageType: '',
        id: '',
      },
      editableRow: [2,4,5],
      goNextClicked: false,
      curTotalProbability: 0,
      totalProbabilityColor: '',
      bgPixelLimit: [750,1026], 
      bgSizeLimit: 2,
      forwardImageUrlPixelLimit: [500,400],
      sharecardSizeLimit: 2,
      activityColumns: [
        { title: '序号', key: 'position', width:60, className: 'th-class pro-img-box'},
        { title: '奖品图', key: 'imageUrl', className: 'th-class pro-img-box', render: (h, params) => { return this.showRowSingleImage(h, params.row.imageUrl) }},
        { title: '奖品名称', key: 'name', className: 'th-class', render: (h, params) => { return h('span', params.row.name) }},
        { title: '中奖概率', slot: 'drawPossibility', className: 'th-class', width:114 },
        { title: 'Model code', key: 'modelCode', className: 'th-class', render: (h, params) => { return this.showRowModalCode(h, params.row) }},
        { title: 'Item code', key: 'itemCode', className: 'th-class', render: (h, params) => { return this.showRowItemCode(h, params.row) }},
        { title: '价格', key: 'price', className: 'th-class', render: (h, params) => { return this.showRowPrice(h, params.row) }},
        { title: '奖品数量', key: 'prizeQuantity', className: 'th-class', render: (h, params) => { return this.showPriceQuantity(h, params.row) }},
        { title: '奖品简介', key: 'description', className: 'th-class', render: (h, params) => { return  this.showRowDescription(h, params.row) }},
        { title: '操作',  slot: 'action', className: 'th-class', fixed: 'right', width: 90}
      ],
      // 奖品配置列表
      productInfoList: [
        { position: 1, imageUrl: 'https://membership-pr.oss-cn-shanghai.aliyuncs.com/gallery/mp/luckyDraw/prize-coupon.png', name: '迪卡侬官网免邮券', drawPossibility: '', modelCode: '', itemCode: '', price: null, prizeQuantity: null, description: '' },
        { position: 2, imageUrl: '', name: '', drawPossibility: '', modelCode: '', itemCode: '', price: null, prizeQuantity: '', description: '' },
        { position: 3, imageUrl: 'https://membership-pr-oss.decathlon.com.cn/gallery/mp/luckyDraw/prize-ranzhi200.png', name: '200燃值', drawPossibility: '', modelCode: '', itemCode: '', price: null, prizeQuantity: null, description: '' },
        { position: 4, imageUrl: '', name: '', drawPossibility: '', modelCode: '', itemCode: '', price: null, prizeQuantity: null, description: '' },
        { position: 5, imageUrl: '', name: '', drawPossibility: '', modelCode: '', itemCode: '', price: null, prizeQuantity: null, description: '' },
        { position: 6, imageUrl: 'https://membership-pr-oss.decathlon.com.cn/gallery/mp/luckyDraw/prize-thanks.png', name: '谢谢参与', drawPossibility: '', modelCode: '', itemCode: '', price: null, prizeQuantity: null, description: '' },
        { position: 7, imageUrl: 'https://membership-pr-oss.decathlon.com.cn/gallery/mp/luckyDraw/prize-ranzhi10.png', name: '10燃值', drawPossibility: '', modelCode: '', itemCode: '', price: null, prizeQuantity: null, description: '' },
        { position: 8, imageUrl: 'https://membership-pr-oss.decathlon.com.cn/gallery/mp/luckyDraw/prize-again.png', name: '再来一次', drawPossibility: '', modelCode: '', itemCode: '', price: null, prizeQuantity: null, description: '' },
      ],
      // 活动配置数据
      formValidate: {
        imageUrl: '',             // 背景图
        activityRule: '',      // 活动规则
        activityName: '',         // 卡片标题
        forwardImageUrl: '',         // 转发卡片
        // pathUrl: '',          // 卡片path
        activityEffectType: '',      // 活动生效类型
        planEffectTime: '' // 活动生效时间
      },
      activityRuleMaxLength: 3000,
      activityNameMaxLength: 24,
      ruleValidate: {
        imageUrl: [
          { required: true, message: '请上传背景图', trigger: 'blur' }
        ],
        activityRule: [
          { required: true, message: '请填写活动规则', trigger: 'blur' },
        ],
        activityName: [
          { required: true, message: '请填写卡片标题', trigger: 'blur' }
        ],
        forwardImageUrl: [
          { required: true, message: '请上传转发卡片', trigger: 'blur' }
        ],
        activityEffectType: [
          { required: true, message: '请选择活动生效时间', trigger: 'change' }
        ],
      }
    };
  },
  created(op) {
    this.calcTotalProbability();
    this.initLogic();
  },
  methods: {
    // 检查概率是否填写
    checkProbability(row) {
      return this.goNextClicked && (row.drawPossibility === '' || row.drawPossibility === null) 
    },
    initLogic() {
      let curPageInfo = this.$local.getStorage('PAGE_INFO') || {};
      const { actionType, awardActivityInfo }  = curPageInfo
      this.pageInfo = curPageInfo;
      // 从单个奖品编辑页面返回时，需要将之前的数据回显
      if( actionType === 'updateAwardActivityInfo' ) {
        this.productInfoList = awardActivityInfo.productInfoList || [];
        this.formValidate = awardActivityInfo.formValidate || {};
        this.calcTotalProbability();
      } else {
        this.getPageData();
      }
    },
    getPageData() {
      const { id } = this.pageInfo;
      if( id ) {
        this.getAwardActivityInfo(id);
      }
    },
    async getAwardActivityInfo(id) {
      let resp = await this.$api.ldActivityDetail(id);
      if (resp.status === 'success' && resp.data) {
        this.productInfoList = resp.data.productInfoList || [];
        this.formValidate = resp.data.configDto || {};
        // 生效中的活动，编辑状态时，活动时间都切换为disabled
        const { pageType, activityStatus } = this.pageInfo;
        const isEditInEffect = pageType === 'edit' && activityStatus === 'IN_EFFECT';
        if(isEditInEffect) {
          this.formValidate.activityEffectType = 'NOW';
          this.formValidate.planEffectTime = '';
        }
        // 计算概率合计
        this.calcTotalProbability();
      }

    },
    changePlanEffectTime(val) {
      this.formValidate.planEffectTime = val;
    },
    isRowBtnDisabled(row) {
      const pageEditDisabled = this.pageInfo.pageType === 'view';
      const btnEditDisabled = !this.editableRow.includes(row.position);
      return pageEditDisabled || btnEditDisabled;
    },
    calcTotalProbability() {
      let total = 0;
      this.productInfoList.forEach(item => {
        total = addNum(total, item.drawPossibility || 0);
      });
      this.curTotalProbability = total;
      this.gettotalProbabilityColor();
    },
    toEditSingleAward(row) {
      const { pageType, id, activityStatus } = this.pageInfo;
      this.$local.setStorage('PAGE_INFO', {
        pageType,
        id,
        activityStatus,
        awardActivityInfo: {
          productInfoList: this.productInfoList || [],
          formValidate: this.formValidate || {}
        }
      })
      const path = `${pageUrl.luckyDraw.editSingleAward}/${row.position}`;
      this.$router.push({
        path: path,
      })
    },
    handleGailvChange(ev, index) {
      let val = ev.target.value;
      if(val === '') {
        return;
      }
      // 清除“数字”和“.”以外的字符
      val = val.replace(/[^\d.]/g, "") 
      // 只允许一个小数点
      const parts = val.split('.');
      if (parts.length > 2) {
        val = `${parts[0]}.${parts.slice(1).join('')}`;
      }
      // 限制小数点后4位
      let value = val.toString();
      if (value.includes('.')) {
        const parts = value.split('.');
        if (parts[1].length > 4) {
          val = `${parts[0]}.${parts[1].substring(0, 4)}`;
        }
      }
      this.$nextTick(() => {
        this.productInfoList[index].drawPossibility = val;
        this.calcTotalProbability();
      });
    },
    gettotalProbabilityColor() {
      let isBlue = (this.curTotalProbability === 100) || (this.curTotalProbability === 0);
      this.totalProbabilityColor = isBlue ? 'blue' : 'red';
    },
    // showRowSingleImage
    showRowSingleImage(h, imageUrl) {
      if(!imageUrl) {
        return h('span', '')
      }
      return h('div', { class: 'table-pic-box' }, [h('img', { attrs: { src: imageUrl, class: 'table-img proImg' } })])
    },
    handleVirtualItemShow(h, row, itemKey) {
      const isVirtualAward = !this.editableRow.includes(row.position);
      const needHide = row[itemKey] === null || row[itemKey] === '';
      const val = isVirtualAward && needHide ? '/' : row[itemKey];
      // return h('span', val)
      return h('div', { class: 'overflow-two-row' }, val)
    },
    showRowModalCode(h, row) {
      return this.handleVirtualItemShow(h, row, 'modelCode');
    },
    showRowItemCode(h, row) {
      return this.handleVirtualItemShow(h, row, 'itemCode');
    },
    showRowPrice(h, row) {
      return this.handleVirtualItemShow(h, row, 'price');
    },
    showPriceQuantity(h, row) {
      return this.handleVirtualItemShow(h, row, 'prizeQuantity');
    },
    showRowDescription(h, row) {
      return this.handleVirtualItemShow(h, row, 'description');
    },
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'table-pic-box' }, [h('img', { attrs: { src: imageUrls[0], class: 'table-img proImg' } })])
    },
    goReturn() {
      const path = `${pageUrl.luckyDraw.activityManage}`;
      this.$router.replace(path)
    },
    goNext() {
      this.goNextClicked = true;
      this.checkAwardList();
    },
    checkAwardList() {
      const hasEmptyPossibility = this.productInfoList.filter(item => {
        return item.drawPossibility === '' || item.drawPossibility === null;
      });
      // 奖品概率 不可为空
      const hasEmpty = hasEmptyPossibility.length > 0;
      if (hasEmpty) {
        this.$message.error({ content: '奖品信息不可为空', closable: true, duration: 2 });
        return;
      }
      
      this.calcTotalProbability();
      // 检查中奖概率是否为100%
      if (this.curTotalProbability !== 100) {
        this.$message.error({ content: '中奖概率需为100%', closable: true, duration: 2 });
        return;
      }
      // 检查是否有未填写的奖品信息
      let hasEmptyAwardInfo = this.productInfoList.filter(item => {
        const isVirtualAward = !this.editableRow.includes(item.position);
        if(isVirtualAward) {
          return item.imageUrl === '' || item.name === '';
        } else {
          return item.imageUrl === '' || item.name === '' || item.modelCode === '' || item.itemCode === '' || item.price === '' || item.prizeQuantity === '' || item.description === ''
        }
      });
      if (hasEmptyAwardInfo.length > 0) {
        this.$message.error({ content: '奖品信息不可为空', closable: true, duration: 2 });
        return;
      }
      this.activeName = 'activityConfig'
    },
    updateBgImage(url) {
      this.formValidate.imageUrl = url;
      this.$refs["formValidate"].validateField('imageUrl');
    },
    updateShareCardImage(url) {
      this.formValidate.forwardImageUrl = url;
      this.$refs["formValidate"].validateField('forwardImageUrl');
    },
    handleActivityTypeChange() {
      let isTiming = this.formValidate.activityEffectType === 'TIMING';
      if(!isTiming) {
        this.formValidate.planEffectTime = ''
      }
    },
    // 发布
    handleSubmit (name) {
      const { activityEffectType, planEffectTime } = this.formValidate;
      this.$refs[name].validate((valid) => {
        if (valid) {
          if(activityEffectType === 'TIMING' && !planEffectTime) {
            this.$message.error('请选择活动生效时间');
            return;
          }
          // 增加：发布活动时，校验定时时间不能早于当前时间
          if(activityEffectType === 'TIMING' && planEffectTime) {
            const now = new Date().getTime();
            const planTime = new Date(planEffectTime).getTime();
            if(planTime < now) {
              this.$message.error('活动生效时间不可早于当前时间');
              return;
            }
          }
          this.activityPublish();
        } else {
          console.log('>>>>> submit error')
        }
      })
    },
    formatTime(time) {
      if(!time) { return ''; }
      return new Date(time).format('yyyy-MM-dd hh:mm:ss');
    },

    // 保存草稿
    async handleDraftSave() {
      const { pageType, id } = this.pageInfo;
      const finalId = pageType === 'edit' ? id : null;
      let { planEffectTime, activityEffectType } = this.formValidate;
      // 保存草稿时，如果活动生效时间为空，则默认设为立即生效
      if(activityEffectType === '') {
        this.formValidate.activityEffectType = 'NOW';
      }
      this.formValidate.planEffectTime  = this.formatTime(planEffectTime);
      let params = {
        activityDetail: {
          configDto: this.formValidate,
          productInfoList: this.productInfoList
        },
        id: finalId
      }
      let resp = await this.$api.ldActivityDraftSave(params);
      if (resp.status === 'success') {
        this.$message.success('保存草稿成功');
        // 跳转到抽奖活动管理页面
        const path = `${pageUrl.luckyDraw.activityManage}`;
        this.$router.push({
          path
        })
      }
    },
    handleReset (name) {
      this.$refs[name].resetFields();
    },
    goPreviousStep(){
      this.activeName = 'awardConfig'
    },
    
    async activityPublish() {
      const { pageType, id } = this.pageInfo;
      const finalId = pageType === 'edit' ? id : null;
      let { planEffectTime } = this.formValidate;
      this.formValidate.planEffectTime  =  this.formatTime(planEffectTime);
      let params = {
        activityDetail: {
          configDto: this.formValidate,
          productInfoList: this.productInfoList
        },
        id: finalId
      }
      let resp = await this.$api.ldActivityPublish(params);
      if (resp.status === 'success') {
        this.$message.success('发布成功');
        // 跳转到抽奖活动管理页面
        const path = `${pageUrl.luckyDraw.activityManage}`;
        this.$router.push({
          path
        })
      }
    },
    handleCancel(){
      const path = `${pageUrl.luckyDraw.activityManage}`;
      this.$modal.confirm({
        'title': '退出后编辑内容将不会保存，是否确认退出？',
        'content': ``,
        'okText': '确认',
        'cancelText': '取消',
        'onOk': () => {
          this.$router.replace(path)
        }
      })
    },
  },
};
</script>

<style scoped lang="less">
.activity-manage {
  ul,li { list-style: none; outline: none; }
  background: #FFFFFF;
  
  .tab-list{
    display: flex; align-items: center;
    width: 100%; height: 60px; overflow: hidden;
    border-bottom: 1px solid #dcdee2;
  }
  .tab{
      padding: 25px 20px 0;
      line-height: 22px; font-size: 20px;
      box-sizing: border-box;
      &.active{
        color: #3080c3;
        border-bottom: 3px solid #3080c3;
      }
    }
  .top-tips{
    height: 60px;
    color: #101010;
    font-size: 14px; line-height: 70px;
    display: flex; justify-content: flex-end;
    margin-right: 24px;
    .percent{
      margin-left: 4px;
      font-style: normal;
      font-weight: bolder;
      color: #3643BA;
      &.blue{
        color: #3643BA;
      }
      &.red{
        color: #FF4D4F;
      }
    }
  }
  .conent-wrap{
    padding: 0 24px 24px;
  }
  /deep/.table-wrap{
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }
    .slot-input{
      display: flex; align-items: center; justify-content: center;
      &.highlight .ivu-input{
        border: 1px solid red;
      }
    }
    .award-probability{
      display: flex; align-items: center;
    }
    .overflow-two-row {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .pro-img-box {
      .table-pic-box {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        padding:10px 0;
        height: auto;
        overflow: hidden;
        text-align: center;
        box-sizing: border-box;
        .proImg {
          width: 48px!important;
          height: 48px!important;
        }
      }
    }
    .ivu-btn-error{
      border: 1px solid #FF4D4F;
      background: #FFF ;
      color: #FF4D4F;
    }
  }
  .bottom-bar{
    width: 100%; height: 80px;
    border-top: 1px solid #D9D9D9;
    padding: 14px 40px 0;
    .save{ margin-left: 16px; }
    .cancel{ float: right; }
  }
  /deep/.activity-config{
    padding: 24px 10px;
    .ivu-form{
      .activity-rule, .card-title{
        .rule-input {
          position: relative;
          width: 380px;
          .text-count{
            position: absolute; right: 3px; bottom: -16px;
            display: inline-block;
            height: 16px; line-height: 16px;
            padding: 0 4px;
            background: #D9D9D9;
            color: #FFFFFF; font-size: 9px; 
          }
        }
      }
      .card-title, .card-path{
        .rule-input{
          width: 300px;
        }
        .ivu-input-wrapper {
          width: 300px;
        }
      }
    }
  }
    
}
</style>