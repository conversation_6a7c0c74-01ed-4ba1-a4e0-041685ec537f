<template>
	<div class="integral-list">
		<i-row>
			<i-col class="text-right mb30 mt10" span="24">
				<i-button @click="createIntegral" icon="md-add" type="primary" v-has="['POINT_RULE_UPDATE']">创建</i-button>
			</i-col>
		</i-row>
		<i-row>
			<i-table :columns="orderColumns" :data="orderList" border class="product-order-table-box" ref="table" />
		</i-row>
		<!-- 填写快递信息 -->
		<i-modal :title="modalTitle" class-name="vertical-center-modal" footer-hide v-model="statusFlage">
			<i-form :label-width="100">
				<i-row>
					<i-col :cols="12">
						<i-form-item class="integralList-form" label="规则名称*：">
							<i-input
								:disabled="disabledStatus"
								class="integralList-form-input"
								placeholder="请输入规则名称"
								v-if="modalShowType != 1"
								v-model="modalDataFilter.planName"
							/>
							<span v-else>{{modalDataShow.planName}}</span>
							<p class="color-red" v-show="rules.planName.show">{{rules.planName.error}}</p>
						</i-form-item>
					</i-col>
				</i-row>
				<i-row>
					<i-col :cols="12">
						<i-form-item class="integralList-form" label="规则描述*：" prop="description">
							<i-input
								:autosize="{minRows: 1,maxRows: 3}"
								:disabled="disabledStatus"
								class="integralList-form-input"
								placeholder="请输入规则描述"
								required="true"
								type="textarea"
								v-if="modalShowType != 1 "
								v-model="modalDataFilter.description"
							/>

							<span v-else>{{modalDataShow.description}}</span>
							<p class="color-red" v-show="rules.description.show">{{rules.description.error}}</p>
						</i-form-item>
					</i-col>
				</i-row>
				<i-row>
					<i-col :cols="12">
						<i-form-item class="integralList-form" label="开始时间*：">
							<i-date-picker
								:disabled="disabledStatus"
								:options="optionsStart"
								@on-change="changeStartTime()"
								format="yyyy-MM-dd HH:mm:ss"
								placeholder="请选择开始时间"
								style="width: 300px;margin-right:10px"
								type="date"
								v-if="modalShowType != 1 "
								v-model="modalDataFilter.startTime"
							></i-date-picker>

							<span v-else>{{ new Date(modalDataShow.startTime).format('yyyy-MM-dd') }} 00:00:00</span>
							<p class="color-red" v-show="rules.startTime.show">{{rules.startTime.error}}</p>
						</i-form-item>
					</i-col>
				</i-row>
				<i-row>
					<i-col :cols="12">
						<i-form-item class="integralList-form" label="结束时间*：">
							<i-date-picker
								:disabled="modalDataFilter.startTime==''"
								:options="optionsEnd"
								@on-change="changeEndTime()"
								format="yyyy-MM-dd HH:mm:ss"
								placeholder="请选择结束时间"
								style="width: 300px;margin-right:10px"
								type="date"
								v-if="modalShowType != 1 "
								v-model="modalDataFilter.endTime"
							/>
							<span v-else>{{ processingTime(modalDataShow.endTime,-1) }} 23:59:59</span>
							<p class="color-red" v-show="rules.endTime.show">{{rules.endTime.error}}</p>
						</i-form-item>
					</i-col>
				</i-row>

				<i-row v-if="modalShowType!==2">
					<i-col>
						<i-form-item class="integralList-form" label="规则状态*：">
							<i-switch :disabled="disabledStatus" v-if="modalShowType  != 1" v-model="switchState"></i-switch>
							<span v-else>{{editData.status==="ENABLE"?'已启用':'未启用'}}</span>
						</i-form-item>
					</i-col>
				</i-row>
				<i-row>
					<i-col :cols="12">
						<i-form-item class="integralList-form" label="规则类型*：">
							<i-radio-group @on-change="changeRadioOperation" v-if="modalShowType != 1 " v-model="modalDataFilter.eventType">
								<i-radio
									:disabled="disabledStatus"
									:key="item.type"
									:label="item.type"
									v-for="item in integralType"
								>{{item.name}}</i-radio>
							</i-radio-group>
							<span v-else>{{modalDataShow.eventType==="REGISTER"?'注册送积分':'购物送积分'}}</span>
							<p class="color-red" v-show="rules.eventType.show">{{rules.eventType.error}}</p>
						</i-form-item>
					</i-col>
				</i-row>
				<i-row v-if="eventType==='REGISTER'">
					<i-col :cols="12">
						<i-form-item class="integralList-form" label="赠送积分*：">
							<input
								:disabled="disabledStatus"
								class="integralList-form-input ivu-input ivu-input-default"
                onkeyup="this.value=this.value.replace(/[^\d]/ig,'')"
								placeholder="请输入赠送积分（1-200）"
								v-if="modalShowType != 1 "
								v-model="modalDataFilter.formula"
							/>
							<span v-else>{{modalDataShow.formula}}</span>
							<p class="color-red" v-show="rules.formula.show">{{rules.formula.error}}</p>
						</i-form-item>
					</i-col>
				</i-row>
				<i-row v-if="eventType==='PURCHASE'">
					<i-col :cols="12">
						<i-form-item class="integralList-form" label="赠送积分(倍)*：">
              <select
                :disabled="disabledStatus"
                class="integralList-form-input ivu-input ivu-input-default custom-select"
                v-if="modalShowType != 1"
                v-model.number="modalDataFilter.formula"
              >
                <option :value="1">1</option>
                <option :value="2">2</option>
                <option :value="3">3</option>
              </select>
							<span v-else>{{modalDataShow.formula}}</span>
							<p class="color-red" v-show="rules.formula.show">{{rules.formula.error}}</p>
						</i-form-item>
					</i-col>
					<i-col :cols="12">
						<i-form-item class="integralList-form" label="购物总价(元)：">
							<input
								:disabled="disabledStatus"
								class="integralList-form-input ivu-input ivu-input-default"
								onkeyup="this.value=this.value.replace(/[^\d]/ig,'')"
								placeholder="请输入购物总价"
								v-if="modalShowType != 1 "
								v-model="modalDataFilter.amount"
							/>
							<span v-else>{{modalDataShow.amount}}</span>
						</i-form-item>
					</i-col>
				</i-row>
				<div class="modal-items control-area text-right">
					<i-button @click="statusFlage=false" class="mr30">取消</i-button>
					<i-button @click="ok" type="primary">确认</i-button>
				</div>
			</i-form>
		</i-modal>
	</div>
</template>
<script>
import cfg from './config'
import { mapGetters, mapActions } from 'vuex'
import { deepClone, formatDate, checkFill } from '@/utils/util'
import { Message, Spin } from 'iview'
export default {
  name: 'integral-list',
  data() {
    return {
      integralType: cfg.integralType,
      modalDataFilter: cfg.modalDataFilter,
      rulesList: cfg.statusType,
      // 表格操作按钮
      opBtnList: cfg.opBtnList,
      rules: cfg.rules,
      statusFlage: false,
      switchState: false,
      disabledStatus: false,
      modalTitle: '',
      modalDataShow: '',
      // 判断模态框显示创建（true） 回显(false)
      modalShowType: 0,
      editData: {
        storeNumber: '',
        status: '',
        planId: ''
      },
      additionalParameters: '',
      optionsEnd: {},
      // 判断类型是购物还是注册
      eventType: '',
      // 订单列表
      orderList: [],
      optionsStart: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now()
        }
      },
      orderColumns: [
        {
          title: 'ID',
          key: 'planId',
          className: 'th-class',
          width: 80
        },
        {
          title: '规则名称',
          key: 'planName',
          className: 'th-class'
        },
        {
          title: '规则描述',
          key: 'description',
          className: 'th-class'
        },
        {
          title: '开始时间',
          key: 'startTime',
          className: 'th-class',
          render: (h, params) => {
            const startDate = new Date(params.row.startTime)
            return h('span', startDate.format('yyyy-MM-dd') + ' 00:00:00')
          }
        },
        {
          title: '结束时间',
          key: 'endTime',
          className: 'th-class',
          render: (h, params) => {
            return h(
              'span',
              this.processingTime(params.row.endTime, -1) + ' 23:59:59'
            )
          }
        },
        {
          title: '规则状态',
          key: 'status',
          className: 'th-class',
          render: (h, params) => {
            const statusItem = cfg.statusType.find(item => {
              if (params.row.status === item.type) return item
            })
            return h(
              'span',
              statusItem ? statusItem.name : params.row.eventType
            )
          }
        },
        {
          title: '规则类型',
          key: 'eventType',
          className: 'th-class',
          render: (h, params) => {
            const ruleItem = this.integralType.find(item => {
              if (params.row.eventType === item.type) return item
            })
            return h('span', ruleItem ? ruleItem.name : params.row.eventType)
          }
        },
        {
          title: '操作',
          key: 'action',
          className: 'th-class',
          fixed: 'right',
          width: 180,
          render: (h, params) => {
            return this.createOpBtn(h, params.row)
          }
        }
      ],
      totalCount: '',
      orderIds: ''
    }
  },
  computed: {
    ...mapGetters(['currentStore'])
  },
  watch: {
    // 监控门店切换, 刷新数据
    currentStore(newVal, oldVal) {
      this.loadData()
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 初始数据
    loadData() {
      this.integralList()
    },
    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      this.opBtnList.forEach(value => {
        operations.push(
          h(
            'i-button',
            {
              class: 'ml5 mr5',
              props: { type: value.type },
              directives: [
                {
                  name: 'has',
                  value: value.permissions
                }
              ],
              on: {
                click: () => {
                  this.rowOperation(value.code, row)
                }
              }
            },
            value.text
          )
        )
      })
      return h('div', operations)
    },
    // 创建一个积分
    createIntegral() {
      this.modalShowType = 2
      this.handleReset()
      this.modalDataShow = this.eventType = ''
      this.modalTitle = cfg.modalTitle.creat
      this.statusFlage = true
      this.disabledStatus = false
    },
    // 查看订单详情
    viewOrder(row) {
      this.modalShowType = 1
      this.handleReset()
      this.getintegralDetail(row.planId)
      this.modalTitle = cfg.modalTitle.views
      this.statusFlage = true
    },
    editOrder(row) {
      this.modalShowType = 3
      this.handleReset()
      this.modalTitle = cfg.modalTitle.edit
      this.getintegralDetail(row.planId)
      this.statusFlage = true
    },
    changeStartTime() {
      let time = Date.parse(this.modalDataFilter.startTime)
      this.optionsEnd = {
        disabledDate(date) {
          return date && date.valueOf() < time
        }
      }
    },
    changeEndTime() {
      let time = Date.parse(this.modalDataFilter.endTime)
      this.modalDataFilter.endTime =
        new Date(this.modalDataFilter.endTime).format('yyyy-MM-dd') + '23:59:59'
    },
    // 表格项操作
    rowOperation(type, row) {
      if (type === 'views') {
        this.viewOrder(row)
      } else {
        this.editOrder(row)
      }
    },
    ok() {
      switch (this.modalShowType) {
        case 1:
          this.statusFlage = false
          break
        case 2:
          this.createOneintegralRule()
          break
        case 3:
          this.editOneintegralRule()
          break
        default: {
          console.log('没有该类型')
        }
      }
    },
    changeRadioOperation() {
      this.eventType = this.modalDataFilter.eventType;
      this.modalDataFilter.formula = '';
      this.rules.formula.show = false;
    },
    /*
     *  data Handel
     */
    // 编辑时给模态框赋值
    copyDataTomodal(data) {
      for (const item in this.modalDataFilter) {
        this.modalDataFilter[item] = data[item]
      }
      for (const item in this.editData) {
        this.editData[item] = data[item]
      }
      this.switchState = cfg.switchState[this.editData.status]
      this.modalDataFilter.endTime =
        this.processingTime(this.modalDataFilter.endTime, -1) + '  23:59:59'
      this.disabledFlag(data.startTime)
      this.changeStartTime()
    },
    // 判断模态框非空处理
    filterTheData() {
      let flag = false
      const rules = this.rules
      for (const key in rules) {
        const val = this.modalDataFilter[key]
        if (val === '' || !val) {
          this.rules[key] && (this.rules[key].show = true)
          flag = true
        } else {
          this.rules[key] && (this.rules[key].show = false)
        }

        // formula校验
        if (key === 'formula' && this.eventType === 'REGISTER') {
          const number = Number(val)
          const isValid = Number.isInteger(number) && number >= 1 && number <= 200
          if (!isValid) {
            this.rules.formula.show = true
            this.rules.formula.error = '请输入1-200的正整数'
            flag = true
          }
        }
        else if (key === 'formula' && this.eventType === 'PURCHASE') {
          if (!val) {
            this.rules.formula.show = true
            this.rules.formula.error = '请输入赠送积分倍数'
            flag = true
          }
        }
      }
      return flag
    },
    handleReset() {
      for (const i in this.modalDataFilter) {
        this.modalDataFilter[i] = ''
      }
      for (const item in this.rules) {
        this.rules[item].show = false
      }
    },
    // 编辑积分规则 处理模态框disabled状态
    disabledFlag(t) {
      let startTime = new Date(t).format('yyyy-MM-dd')
      let today = new Date().format('yyyy-MM-dd')
      startTime = Date.parse(new Date(startTime))
      today = Date.parse(new Date(today))
      if (startTime <= today) {
        this.disabledStatus = true
      } else {
        this.disabledStatus = false
      }
    },
    // 处理时间格式
    formatTime() {
      this.modalDataFilter.startTime = new Date(
        this.modalDataFilter.startTime
      ).format('yyyy-MM-dd')
      this.modalDataFilter.endTime = this.processingTime(
        this.modalDataFilter.endTime,
        1
      )
    },
    // 根据产品需求 回显时间-1   创建更新时间+1
    processingTime(t, type) {
      let oneDay = 86400000 * type
      let time = new Date(t)
      time = Date.parse(new Date(time)) + oneDay
      time = new Date(time)
      time = time.format('yyyy-MM-dd')
      return time
    },

    /**
     * network
     */
    async integralList() {
      let resp = await this.$api.getIntegralList(
        this.currentStore.dktStoreNumber
      )
      if(resp && resp.data) {
        this.orderList = resp.data
      }
    },
    async getintegralDetail(id) {
      let resp = await this.$api.getOneIntegraDetail(id)
      this.modalDataShow = resp.data
      this.copyDataTomodal(resp.data)
      this.eventType = resp.data.eventType
    },
    async createOneintegralRule() {
      if (this.filterTheData()) {
        return
      }
      this.formatTime()
      let parmas = {
        ...this.modalDataFilter,
        storeNumber: this.currentStore.dktStoreNumber
      }
      let resp = await this.$api.creatOneIntegra(parmas)
      if (resp.status == 'success') {
        this.statusFlage = false
        this.integralList()
      }
    },
    async editOneintegralRule() {
      let originalStatus = this.editData.status
      if (this.filterTheData()) {
        return
      }
      this.editData.status = this.switchState ? 'ENABLE' : 'DISABLE'
      this.formatTime()
      let params = {
        ...this.modalDataFilter,
        ...this.editData,
        storeNumber: this.currentStore.dktStoreNumber
      }
      let resp = await this.$api.updateOneIntegraDetail(params)
      if (resp.status == 'success') {
        this.statusFlage = false
        this.integralList()
      } else {
        this.editData.status = originalStatus
      }
    },
  }
}
</script>

<style lang="less" scoped>
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}
.integralList-form {
  padding-left: 50px;
  .ivu-form-item-label {
    width: 200px;
  }
  &-input {
    width: 300px;
    margin-left: 0;
  }
}
ivu-input {
  font-size: 12px !important;
}
.control-area {
  margin-top: 60px;
}
.custom-select {
  height: auto;
  appearance: none; /* 隐藏默认箭头 */
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url('../../assets/arrow.png') no-repeat right 10px center;
  background-size: 12px 12px;
  padding-right: 30px; /* 控制右侧图标与文字间距 */
}
</style>