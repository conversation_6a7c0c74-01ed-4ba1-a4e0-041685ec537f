<template>
  <div class="batch-wrap">
    <template v-if="!dragUploadVisible">
      <p class="text top-text">一张表格解决多店好礼发布</p>
      <p class="text">先下载模版，填写后上传并发布</p>
      <div class="btns">
        <i-button class="mr15" @click="goDownloadTemplate">下载模板</i-button>
        <i-button type="primary" @click="showDragUpload">上传模板</i-button>
      </div>
    </template>
    <template v-else>
      <i-upload
        class="drag-upload-wrap"
        :before-upload="beforeUpload"
        action
        name="file"
        type="drag"
      >
        <div style="padding: 30px">
          <i-icon size="52" type="ios-cloud-upload" style="color: #3399ff" />
          <p>点击或拖动文件在这里上传</p>
          <p>{{ uploadFile ? uploadFile.name : "" }}</p>
        </div>
      </i-upload>
      <div class="upload-wrap">
        <i-button type="primary" class="upload-btn" @click="goPublish"
          >发布</i-button
        >
      </div>
    </template>
    <i-modal v-model="errTipModal"
        title="错误信息">
        <p slot="header" style="color:#f60;">
            <Icon type="ios-information-circle"></Icon>
            <span>错误信息</span>
        </p>
        <p v-if="errTipData.code && errTipData.code === '1014'">全部上架失败！</p>
        <p v-for="(item, index) in errTipData.data" :key="index">第 {{ item.row }} 行商品上架失败，{{ item.message }}</p>
        <p v-if="errTipData.code && errTipData.code === '1015'">其余商品上架成功！</p>
        <div slot="footer">
          <i-button type="primary" @click="hideDragUpload">确定</i-button>
        </div>
      </i-modal>
  </div>
</template>
<style lang="less" scoped>
.batch-wrap {
  width: 100%;
  height: 100%;
  background: #ffffff;
  text-align: center;
  .text {
    line-height: 20px;
    &.top-text {
      padding-top: 100px;
    }
  }
}
</style>
<script>
import comConfig from '@/config/apiConfig.js'
import paidMemberApi from "@/api/paidMember/index";

export default {
  data() {
    return {
      errTipModal: false,
      errTipData: {

      },
      dragUploadVisible: false,
      uploadFile: null,
    };
  },
  methods: {
    async goDownloadTemplate() {
      let resp = await paidMemberApi.downloadTemplate(this.searchForm);
      let hostUrl = comConfig.BASE_URL ; 
      let url = '/api/v2/benefit/templates/doc'
      let afterUrl = process.env.NODE_ENV === 'development' ? url : url.replace(/^\/api/, '/sports-api/bo/api')
      let downloadUrl = hostUrl + afterUrl;
      window.open(downloadUrl, '_blank')
    },
    showDragUpload() {
      this.dragUploadVisible = true;
    },
    hideDragUpload() {
      this.errTipModal = false;
    },
    beforeUpload(file) {
      this.uploadFile = file;
      return false;
    },
    // 处理表单数据
    handleFormData(file) {
      let params = new FormData()
      params.append('file', this.uploadFile, this.uploadFile.name)
      return params
    },
    async goPublish() {
      if (!this.uploadFile) {
        this.$message.warning({
          content: "请先选择上传文件！",
          closable: true,
          duration: 3,
        });
        return;
      } else {
				let objc = {
          isForm: true,
          method: 'post',
          data: this.handleFormData()
        }
				let resp = await paidMemberApi.uploadTemplate(objc);
        if (resp.status === 'success') {
          this.$message.success({
            content: '发布成功!',
            closable: true,
            duration: 3
          })
        } else if((resp.code === '1014') || (resp.code === '1015')){
          this.errTipModal = true;
          this.errTipData.data = resp.data;
          this.errTipData.code = resp.code;
        }
			}
    },
  },
};
</script>

<style lang="less" scoped>
.batch-wrap {
  .btns {
    margin-top: 20px;
  }
  .drag-upload-wrap {
    padding-top: 100px;
    width: 400px;
    margin: 0 auto;
  }
  .upload-wrap {
    margin-top: 100px;
    float: right;
    margin-right: 100px;
    .upload-btn {
      width: 75px;
    }
  }
}
</style>