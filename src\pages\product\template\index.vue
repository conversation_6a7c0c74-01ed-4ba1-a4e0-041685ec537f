<template>
  <div class="template_repo template-repo-box">
    <i-row class="mt20 mb30">
      <i-col span="22">
        <i-col span="6" class="mr30 text-center">
          <i-input v-model="searchForm.itemCode" class="" :autofocus="true" search placeholder="精确查询(Item Code)" clearable />
        </i-col>
        <i-col span="4" class="mr10" style="margin-left:-20px">
          <i-select v-model="searchForm.typeName" placeholder="产品类型" clearable>
            <i-option v-for="l in levelList" :key="l.value" :value="l.value" >{{l.label}}</i-option>
          </i-select>
        </i-col>
        <i-col span="5">
          <i-date-picker :open="open" :type="type" @on-change="changePicker" @on-clear="closePicker" @on-ok="closePicker" :format="format" style="margin-top:-1px">
            <i-input v-model="displayDate" placeholder="发布时间" class="text mr15" clearable @on-focus="openPicker" @on-blur="closePicker">
              <i-select v-model="type" slot="prepend" style="width:50px" @on-change="changeType">
                <i-option value="month">月</i-option>
                <i-option value="date">日</i-option>
              </i-select>
            </i-input>
          </i-date-picker>
        </i-col>
        <i-col span="6">
          <i-button class="mr15" @click="searchClick">查询</i-button>
          <i-button class="mr15" @click="resetClick">重置</i-button>
        </i-col>
      </i-col>
      <i-col span="2" class="text-right">
        <i-button type="primary" icon="md-add" @click="popupNationalCreate" v-has="['PRODUCT_TEMPLATE_CREATION']">添加</i-button>
      </i-col>
    </i-row>

    <i-row>
      <i-table class="text-center" border :columns="templateColumns" :data="templateList" />
    </i-row>
    <i-row class="mt15">
      <i-col class="text-right">
        <i-page :total="page.total" show-total 
          :current="page.no"
          :page-size="page.size" show-sizer 
          :page-size-opts="pageSizeOpts"
          @on-change="changePageNo" @on-page-size-change="changePageSize" />
      </i-col>
    </i-row>

    <t-modal v-if="showDialog" :title="title" :certain="modalCertain" :cancel="modalCancel"/>
    <e-modal v-if="showEditDialog" :certain="modalCertain" :cancel="modalCancel" :id="id" :from="from"/>
  </div>
</template>

<script>
import cfg from '../config'
import TemplateModal from './profile/modal'
import EditProductModal from '../edit'
import { pageSizeOpts, checkFill, judgeFill } from '@/utils/util'

export default {
  name: 'template_repo',
  components: {
    't-modal': TemplateModal,
    'e-modal': EditProductModal
  },
  data() {
    return {
      levelList: cfg.levelList.filter((l) => l.code != '0'),

      // 分页相关配置
      pageSizeOpts: pageSizeOpts,
      page: {
        no: 1,
        size: 10,
        total: 0
      },

      type: 'month',
      format: 'yyyy-MM',
      open: false,
      displayDate: '',
      searchForm: {
        typeName: '',       // 产品类型
        itemCode: '',       // Item Code
        publishMonth: '',   // 发布月份
        publishDate: ''     // 发布日期
      },

      templateList: [],     // 产品模板列表
      
      title: '',            // 对话框标题
      showDialog: false,

      // 表格操作按钮
      opBtnList: [
        { code: 'edit', text: '编辑', permissions: ['PRODUCT_TEMPLATE_CREATION'] },
        { code: 'disable', text: '禁用', type: 'warning', permissions: ['PRODUCT_TEMPLATE_CREATION'] }
      ],

      templateColumns: [
        { title: '图片', key: 'imageUrls', className: 'th-class pro-img-box', render: (h, params) => { return this.showRowImage(h, params.row.productInfo.imageUrls) }},
        { title: 'Model Code', key: 'modelCode', className: 'th-class', render: (h, params) => { return h('span', params.row.productInfo.modelCode) }},
        { title: 'Item Code', key: 'itemCode', className: 'th-class', render: (h, params) => { return h('span', params.row.productInfo.itemCode) }},
        { title: '名称', key: 'title', className: 'th-class', render: (h, params) => { return h('span', params.row.productInfo.title) }},
        { title: '描述', key: 'description', className: 'th-class', render: (h, params) => { return h('span', params.row.productInfo.description) }},
        { title: '类型', key: 'type', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.type, cfg.levelList)) }},
        { title: '燃值', key: 'points', className: 'th-class'},
        { title: '状态', key: 'status', className: 'th-class', render: (h, params) => { return h('span', cfg.convert(params.row.status, cfg.statusList)) }},
        { title: '操作',  key: 'action', className: 'th-class', fixed: 'right', width: 180, render: (h, params) => { return this.createOpBtn(h, params.row) }}
      ],

      from: '',
      showEditDialog: false,
      id: '',
    }
  },
  mounted() {
    this.searchNationalProduct()
  },
  methods: {
    // 渲染表格图片
    showRowImage(h, imageUrls) {
      return h('div', { class: 'table-pic-box' }, [h('img', { attrs: { src: imageUrls[0], class: 'table-img proImg' } })])
    },

    // 渲染表格操作按钮
    createOpBtn(h, row) {
      let operations = []
      this.opBtnList.forEach((value) => {
        operations.push(
          h('i-button', { 
            class: 'ml5 mr5', 
            props: { type: value.type },
            directives: [
              {
                name: 'has',
                value: value.permissions
              } 
            ],
            on: { click: () => { this.rowOperation(value.code, row) } } },
            value.text)
        )
      })
      return h('div', operations)
    },
    openPicker() {
      this.open = true
    },
    changeType(type) {
      switch(this.type) {
        case 'month':
          this.format = 'yyyy-MM'
          break
        case 'date':
          this.format = 'yyyy-MM-dd'
          break
      }
      this.displayDate = ''
    },
    changePicker(date) {
      this.displayDate = date
      switch(this.type) {
        case 'month':
          this.searchForm.publishMonth = date
          this.searchForm.publishDate = ''
          break
        case 'date':
          this.searchForm.publishMonth = ''
          this.searchForm.publishDate = date
          break
      }
      this.open = false
    },
    // 关闭日期选择
    closePicker() {
      this.open = false
    },
    // 查询
    searchClick() {
      this.page.no = 1;
      this.searchNationalProduct()
    },
    // 重置搜索条件
    resetClick() {
      this.searchForm = {}
      this.displayDate = ''
      this.searchNationalProduct()
    },
    // 搜索模板库产品
    async searchNationalProduct() {
      this.searchForm['pageNo'] = this.page.no
      this.searchForm['pageSize'] = this.page.size
      let resp = await this.$api.searchNationalProduct(this.searchForm);
      if(resp && resp.data){
        this.templateList = resp.data.items
        this.page.total = resp.data.total
      }
    },

    // 分页操作
    changePageNo(no) {
      this.page.no = no
      this.searchNationalProduct()
    },
    changePageSize(size) {
      this.page.size = size
      this.searchNationalProduct()
    },

    // 表格项操作
    rowOperation(code, row) {
      switch(code) {
        case 'disable':
          this.$modal.confirm({
					  'title': '模板库产品禁用',
					  'content': `您即将禁用模板库产品<span class='confirm-msg'>${row.productInfo.title}</span>`,
					  'okText': '确定',
					  'cancelText': '取消',
					  'onOk': () => {
						  this.disableNationalProduct(row.id)
					  }
          })
          break
        case 'edit':
          this.editProduct(row.id)
          break
      }
    },

    // 禁用模板库产品
    async disableNationalProduct(id) {
      await this.$api.disableTemplateProduct(id)
      this.searchNationalProduct()
    },

    // 添加模版库产品
    popupNationalCreate() {
      this.title = '添加模版库产品'
      this.showDialog = true
    },
    // 编辑模版库产品
    editProduct(id) {
      this.showEditDialog = true
      this.from = 'template'
      this.id = id
    },
    modalCertain() {
      this.closeDialog()
      this.searchNationalProduct()
    },
    modalCancel() {
      this.closeDialog()
    },
    closeDialog() {
      if (this.id) {
        this.showEditDialog = false
      } else {
        this.showDialog = false
      }
      this.id = ''
    }
  }
}
</script>

<style lang="less">
.template-repo-box {
  .pro-img-box {
    .table-pic-box {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      padding:10px 0;
      height: auto;
      overflow: hidden;
      text-align: center;
      box-sizing: border-box;
      .proImg {
        width: 2.5vw!important;
        height: 2.5vw!important;
      }
    }
  }
}
.ml5 {
  margin-left: 5px;
}
.mr5 {
  margin-right: 5px;
}
</style>
